using UnityEngine;
using System.Collections;

/// <summary>
/// MaterialManager - Handles real-time material changes for the Executive Office
/// This script receives commands from the web interface and applies materials to room elements
/// </summary>
public class MaterialManager : MonoBehaviour
{
    [Header("Floor Materials")]
    [SerializeField] private Material[] floorMaterials;
    
    [Head<PERSON>("Wall Materials")]
    [SerializeField] private Material[] wallMaterials;
    
    [Header("Ceiling Materials")]
    [SerializeField] private Material[] ceilingMaterials;
    
    [Header("Room Renderers")]
    [SerializeField] private Renderer floorRenderer;
    [SerializeField] private Renderer[] wallRenderers;
    [SerializeField] private Renderer ceilingRenderer;
    
    [Head<PERSON>("Animation Settings")]
    [SerializeField] private float transitionDuration = 0.5f;
    [SerializeField] private bool useSmootTransitions = true;
    
    private void Start()
    {
        Debug.Log("🏢 MaterialManager initialized for Executive Office");
        ValidateSetup();
    }
    
    /// <summary>
    /// Main method called from JavaScript to update materials
    /// </summary>
    /// <param name="jsonData">JSON string containing material update data</param>
    public void UpdateMaterial(string jsonData)
    {
        try
        {
            Debug.Log($"📤 Received material update: {jsonData}");
            
            MaterialData data = JsonUtility.FromJson<MaterialData>(jsonData);
            
            if (data == null)
            {
                Debug.LogError("❌ Failed to parse material data JSON");
                return;
            }
            
            switch(data.elementType.ToLower())
            {
                case "floor":
                    UpdateFloorMaterial(data);
                    break;
                case "walls":
                    UpdateWallMaterial(data);
                    break;
                case "ceiling":
                    UpdateCeilingMaterial(data);
                    break;
                default:
                    Debug.LogWarning($"⚠️ Unknown element type: {data.elementType}");
                    break;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Error updating material: {e.Message}");
        }
    }
    
    private void UpdateFloorMaterial(MaterialData data)
    {
        Material newMaterial = GetMaterialById(floorMaterials, data.materialId);
        if (newMaterial != null && floorRenderer != null)
        {
            if (useSmootTransitions)
            {
                StartCoroutine(SmoothMaterialTransition(floorRenderer, newMaterial));
            }
            else
            {
                floorRenderer.material = newMaterial;
            }
            
            Debug.Log($"✅ Floor material updated to: {data.name} ({data.materialId})");
        }
        else
        {
            Debug.LogWarning($"⚠️ Floor material not found: {data.materialId}");
        }
    }
    
    private void UpdateWallMaterial(MaterialData data)
    {
        Material newMaterial = GetMaterialById(wallMaterials, data.materialId);
        if (newMaterial != null && wallRenderers != null)
        {
            foreach(Renderer wallRenderer in wallRenderers)
            {
                if (wallRenderer != null)
                {
                    if (useSmootTransitions)
                    {
                        StartCoroutine(SmoothMaterialTransition(wallRenderer, newMaterial));
                    }
                    else
                    {
                        wallRenderer.material = newMaterial;
                    }
                }
            }
            Debug.Log($"✅ Wall materials updated to: {data.name} ({data.materialId})");
        }
        else
        {
            Debug.LogWarning($"⚠️ Wall material not found: {data.materialId}");
        }
    }
    
    private void UpdateCeilingMaterial(MaterialData data)
    {
        Material newMaterial = GetMaterialById(ceilingMaterials, data.materialId);
        if (newMaterial != null && ceilingRenderer != null)
        {
            if (useSmootTransitions)
            {
                StartCoroutine(SmoothMaterialTransition(ceilingRenderer, newMaterial));
            }
            else
            {
                ceilingRenderer.material = newMaterial;
            }
            
            Debug.Log($"✅ Ceiling material updated to: {data.name} ({data.materialId})");
        }
        else
        {
            Debug.LogWarning($"⚠️ Ceiling material not found: {data.materialId}");
        }
    }
    
    private Material GetMaterialById(Material[] materials, string id)
    {
        foreach(Material mat in materials)
        {
            if (mat.name.ToLower().Contains(id.ToLower()) || 
                mat.name.ToLower().Replace(" ", "").Contains(id.ToLower().Replace(" ", "")))
            {
                return mat;
            }
        }
        return null;
    }
    
    private IEnumerator SmoothMaterialTransition(Renderer targetRenderer, Material newMaterial)
    {
        Material originalMaterial = targetRenderer.material;
        float elapsedTime = 0f;
        
        // Create a temporary material for blending
        Material tempMaterial = new Material(originalMaterial);
        targetRenderer.material = tempMaterial;
        
        while (elapsedTime < transitionDuration)
        {
            float t = elapsedTime / transitionDuration;
            
            // Smooth transition curve
            t = Mathf.SmoothStep(0f, 1f, t);
            
            // Blend between materials (simplified - in practice you'd blend textures/colors)
            if (t >= 0.5f)
            {
                targetRenderer.material = newMaterial;
                break;
            }
            
            elapsedTime += Time.deltaTime;
            yield return null;
        }
        
        // Ensure final material is set
        targetRenderer.material = newMaterial;
        
        // Clean up temporary material
        if (tempMaterial != originalMaterial && tempMaterial != newMaterial)
        {
            DestroyImmediate(tempMaterial);
        }
    }
    
    private void ValidateSetup()
    {
        bool isValid = true;
        
        if (floorMaterials == null || floorMaterials.Length == 0)
        {
            Debug.LogWarning("⚠️ No floor materials assigned!");
            isValid = false;
        }
        
        if (wallMaterials == null || wallMaterials.Length == 0)
        {
            Debug.LogWarning("⚠️ No wall materials assigned!");
            isValid = false;
        }
        
        if (ceilingMaterials == null || ceilingMaterials.Length == 0)
        {
            Debug.LogWarning("⚠️ No ceiling materials assigned!");
            isValid = false;
        }
        
        if (floorRenderer == null)
        {
            Debug.LogWarning("⚠️ Floor renderer not assigned!");
            isValid = false;
        }
        
        if (wallRenderers == null || wallRenderers.Length == 0)
        {
            Debug.LogWarning("⚠️ No wall renderers assigned!");
            isValid = false;
        }
        
        if (ceilingRenderer == null)
        {
            Debug.LogWarning("⚠️ Ceiling renderer not assigned!");
            isValid = false;
        }
        
        if (isValid)
        {
            Debug.Log("✅ MaterialManager setup is valid and ready!");
        }
        else
        {
            Debug.LogError("❌ MaterialManager setup has issues - please check inspector assignments");
        }
    }
    
    /// <summary>
    /// Public method to get available materials for web interface
    /// </summary>
    public string GetAvailableMaterials()
    {
        var materialsData = new
        {
            floor = GetMaterialNames(floorMaterials),
            walls = GetMaterialNames(wallMaterials),
            ceiling = GetMaterialNames(ceilingMaterials)
        };
        
        return JsonUtility.ToJson(materialsData);
    }
    
    private string[] GetMaterialNames(Material[] materials)
    {
        if (materials == null) return new string[0];
        
        string[] names = new string[materials.Length];
        for (int i = 0; i < materials.Length; i++)
        {
            names[i] = materials[i] != null ? materials[i].name : "Missing Material";
        }
        return names;
    }
}

/// <summary>
/// Data structure for material update commands from web interface
/// </summary>
[System.Serializable]
public class MaterialData
{
    public string elementType;    // "floor", "walls", "ceiling"
    public string materialId;     // Material identifier
    public string color;          // Hex color code
    public string texture;        // Texture name/path
    public string name;           // Display name
}
