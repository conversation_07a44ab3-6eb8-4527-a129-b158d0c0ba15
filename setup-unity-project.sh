#!/bin/bash

# Unity 3D Cozy Bedroom Setup Script
echo "🛏️ Setting up Unity 3D Cozy Bedroom Project..."

# Create Unity project directory
UNITY_PROJECT_DIR="$HOME/Desktop/CozyBedroomEditor"
echo "📁 Creating Unity project directory: $UNITY_PROJECT_DIR"

if [ ! -d "$UNITY_PROJECT_DIR" ]; then
    mkdir -p "$UNITY_PROJECT_DIR"
fi

# Create Unity project structure
echo "🏗️ Creating Unity project structure..."
mkdir -p "$UNITY_PROJECT_DIR/Assets/Scripts"
mkdir -p "$UNITY_PROJECT_DIR/Assets/Materials/Bedroom/Floors"
mkdir -p "$UNITY_PROJECT_DIR/Assets/Materials/Bedroom/Walls"
mkdir -p "$UNITY_PROJECT_DIR/Assets/Materials/Bedroom/Ceilings"
mkdir -p "$UNITY_PROJECT_DIR/Assets/Materials/Bedroom/Furniture"
mkdir -p "$UNITY_PROJECT_DIR/Assets/Scenes"

# Copy Unity scripts
echo "📝 Copying Unity scripts..."
cp "Unity-Project/Assets/Scripts/BedroomMaterialManager.cs" "$UNITY_PROJECT_DIR/Assets/Scripts/"
cp "Unity-Project/Assets/Scripts/BedroomCameraController.cs" "$UNITY_PROJECT_DIR/Assets/Scripts/"
cp "Unity-Project/Assets/Scripts/BedroomSceneSetup.cs" "$UNITY_PROJECT_DIR/Assets/Scripts/"
cp "Unity-Project/Assets/Scripts/BedroomMaterialCreator.cs" "$UNITY_PROJECT_DIR/Assets/Scripts/"
cp "Unity-Project/QuickSetup.cs" "$UNITY_PROJECT_DIR/Assets/Scripts/"

echo "✅ Unity project structure created!"
echo "📍 Project location: $UNITY_PROJECT_DIR"
echo ""
echo "🎯 Next steps:"
echo "1. Open Unity Hub"
echo "2. Click 'Open' and select: $UNITY_PROJECT_DIR"
echo "3. Choose Unity 2022.3 LTS with URP template"
echo "4. Follow the setup instructions below..."
echo ""