# Unity 3D Realistic Office Setup Guide

## 🎯 Project Overview
Create a photorealistic executive office in Unity that can be modified in real-time through web interface.

## 📋 Unity Project Setup

### 1. Create New Unity Project
```
- Unity Version: 2022.3 LTS (recommended)
- Template: 3D (Built-in Render Pipeline) or URP
- Project Name: "ExecutiveOfficeEditor"
```

### 2. Essential Packages to Install
```
Window > Package Manager > Install:
- Universal Render Pipeline (URP) - for better graphics
- ProBuilder - for quick 3D modeling
- Cinemachine - for camera controls
- Post Processing - for realistic lighting
```

## 🏢 Room Structure

### 3. Basic Room Geometry
```csharp
// Create these GameObjects:
- Floor (Plane scaled to 10x10)
- Ceiling (Plane at height 3.5)
- Wall_North (Cube scaled to 10x0.2x3.5)
- Wall_South (Cube scaled to 10x0.2x3.5)
- Wall_East (Cube scaled to 0.2x10x3.5)
- Wall_West (Cube scaled to 0.2x10x3.5)
```

### 4. Office Furniture (Use ProBuilder or Asset Store)
```
Essential Furniture:
- Executive Desk (large wooden desk)
- Office Chair (leather executive chair)
- Bookshelf (tall wooden bookshelf)
- Conference Table (oval/rectangular)
- Office Chairs x4 (around conference table)
- Filing Cabinet
- Floor Lamp
- Window Blinds
- Artwork/Paintings
- Plants (potted plants)
- Computer/Monitor
- Coffee Table
```

## 🎨 Materials System

### 5. Create Material Categories
```
Assets/Materials/
├── Floors/
│   ├── HardwoodOak.mat
│   ├── MarbleWhite.mat
│   ├── CarpetGray.mat
│   └── TileModern.mat
├── Walls/
│   ├── PaintWhite.mat
│   ├── WoodPaneling.mat
│   ├── Wallpaper.mat
│   └── Brick.mat
├── Ceilings/
│   ├── PlasterWhite.mat
│   ├── WoodBeams.mat
│   ├── ModernTile.mat
│   └── Acoustic.mat
└── Furniture/
    ├── WoodDark.mat
    ├── LeatherBrown.mat
    ├── MetalSteel.mat
    └── Glass.mat
```

## 💡 Lighting Setup

### 6. Realistic Lighting
```csharp
// Main Lighting:
- Directional Light (Sun through windows)
- Area Lights (ceiling fixtures)
- Point Lights (desk lamps)
- Ambient Lighting (subtle fill)

// Post-Processing:
- Bloom (subtle glow)
- Color Grading (warm office tone)
- Vignette (slight edge darkening)
- Anti-aliasing (smooth edges)
```

## 🎮 Camera System

### 7. Camera Controls
```csharp
// Cinemachine Virtual Camera:
- Orbital camera around room center
- Mouse drag to rotate
- Scroll wheel to zoom
- Smooth damping for professional feel
- Constrain vertical rotation (don't go under floor)
```

## 🔧 Material Manager Script

### 8. C# Script for Material Switching
```csharp
// MaterialManager.cs - receives commands from web interface
public class MaterialManager : MonoBehaviour
{
    [Header("Floor Materials")]
    public Material[] floorMaterials;
    
    [Header("Wall Materials")]
    public Material[] wallMaterials;
    
    [Header("Ceiling Materials")]
    public Material[] ceilingMaterials;
    
    [Header("Room Objects")]
    public Renderer floorRenderer;
    public Renderer[] wallRenderers;
    public Renderer ceilingRenderer;
    
    // Called from JavaScript
    public void UpdateMaterial(string jsonData)
    {
        MaterialData data = JsonUtility.FromJson<MaterialData>(jsonData);
        
        switch(data.elementType.ToLower())
        {
            case "floor":
                UpdateFloorMaterial(data.materialId);
                break;
            case "walls":
                UpdateWallMaterial(data.materialId);
                break;
            case "ceiling":
                UpdateCeilingMaterial(data.materialId);
                break;
        }
    }
    
    private void UpdateFloorMaterial(string materialId)
    {
        Material newMaterial = GetMaterialById(floorMaterials, materialId);
        if (newMaterial != null && floorRenderer != null)
        {
            floorRenderer.material = newMaterial;
            Debug.Log($"Floor material updated to: {materialId}");
        }
    }
    
    private void UpdateWallMaterial(string materialId)
    {
        Material newMaterial = GetMaterialById(wallMaterials, materialId);
        if (newMaterial != null)
        {
            foreach(Renderer wallRenderer in wallRenderers)
            {
                if (wallRenderer != null)
                    wallRenderer.material = newMaterial;
            }
            Debug.Log($"Wall material updated to: {materialId}");
        }
    }
    
    private void UpdateCeilingMaterial(string materialId)
    {
        Material newMaterial = GetMaterialById(ceilingMaterials, materialId);
        if (newMaterial != null && ceilingRenderer != null)
        {
            ceilingRenderer.material = newMaterial;
            Debug.Log($"Ceiling material updated to: {materialId}");
        }
    }
    
    private Material GetMaterialById(Material[] materials, string id)
    {
        foreach(Material mat in materials)
        {
            if (mat.name.ToLower().Contains(id.ToLower()))
                return mat;
        }
        return null;
    }
}

[System.Serializable]
public class MaterialData
{
    public string elementType;
    public string materialId;
    public string color;
    public string texture;
    public string name;
}
```

## 🌐 WebGL Build Settings

### 9. Build Configuration
```
File > Build Settings:
- Platform: WebGL
- Compression Format: Gzip
- Code Optimization: Size
- Strip Engine Code: Enabled
- Managed Stripping Level: Medium

Player Settings:
- Company Name: Pionare
- Product Name: Executive Office Editor
- WebGL Template: Default (or custom)
- Auto Graphics API: Disabled
- Graphics APIs: WebGL 2.0 only
```

## 📁 Project Structure
```
ExecutiveOfficeEditor/
├── Assets/
│   ├── Materials/
│   ├── Models/
│   ├── Scripts/
│   ├── Textures/
│   ├── Scenes/
│   │   └── ExecutiveOffice.unity
│   └── Prefabs/
├── Build/
│   └── WebGL/
└── ProjectSettings/
```

## 🎯 Next Steps

1. **Create Unity Project** with above specifications
2. **Model the Office** using ProBuilder or import models
3. **Set up Materials** with realistic textures
4. **Implement MaterialManager** script
5. **Configure Lighting** for photorealism
6. **Build for WebGL** and integrate with React

## 🚀 Quick Start Implementation

### Step-by-Step Unity Setup:

1. **Create New Unity Project**
   - Unity Hub > New Project > 3D (URP)
   - Name: "ExecutiveOfficeEditor"

2. **Install Required Packages**
   ```
   Window > Package Manager:
   - Universal Render Pipeline
   - ProBuilder
   - Cinemachine
   - Post Processing
   ```

3. **Create Room Structure**
   ```
   Hierarchy:
   ├── ExecutiveOffice
   │   ├── Room
   │   │   ├── Floor
   │   │   ├── Ceiling
   │   │   ├── Wall_North
   │   │   ├── Wall_South
   │   │   ├── Wall_East
   │   │   └── Wall_West
   │   ├── Furniture
   │   │   ├── Desk
   │   │   ├── Chair
   │   │   ├── Bookshelf
   │   │   └── ...
   │   ├── Lighting
   │   │   ├── MainLight
   │   │   ├── CeilingLights
   │   │   └── AmbientLight
   │   └── Camera
   │       └── Main Camera (with OfficeCameraController)
   ```

4. **Add Scripts**
   - Attach `MaterialManager.cs` to empty GameObject named "MaterialManager"
   - Attach `OfficeCameraController.cs` to Main Camera
   - Assign all renderers in MaterialManager inspector

5. **Create Materials**
   - Create material folders as outlined above
   - Assign materials to MaterialManager arrays
   - Use high-quality textures for realism

6. **Setup Lighting**
   - Use URP Lit materials
   - Add Post-Processing Volume
   - Configure realistic lighting setup

7. **Build for WebGL**
   - File > Build Settings > WebGL
   - Player Settings > Publishing Settings > Compression Format: Gzip
   - Build to `/public/unity/executive-office/Build/`

## 🎯 Expected Results

With this setup, you'll have:
- ✅ **Photorealistic 3D office** that looks like a real room
- ✅ **Smooth camera controls** (mouse drag to rotate, scroll to zoom)
- ✅ **Real-time material changes** via web interface
- ✅ **Professional lighting** with shadows and reflections
- ✅ **Responsive performance** optimized for web
- ✅ **No animations** - static, realistic environment

The final result will be a Unity WebGL build that integrates seamlessly with your React interface, allowing users to modify floor, wall, and ceiling materials in real-time on a truly realistic 3D office model!
