using UnityEngine;

/// <summary>
/// OfficeCameraController - Smooth orbital camera controls for the Executive Office
/// Provides professional camera movement with mouse and touch controls
/// </summary>
public class OfficeCameraController : MonoBehaviour
{
    [Header("Camera Settings")]
    [SerializeField] private Transform target; // Center point of the office
    [SerializeField] private float distance = 8f;
    [SerializeField] private float minDistance = 3f;
    [SerializeField] private float maxDistance = 15f;
    
    [Header("Rotation Settings")]
    [SerializeField] private float rotationSpeed = 2f;
    [SerializeField] private float minVerticalAngle = 10f;
    [SerializeField] private float maxVerticalAngle = 80f;
    
    [Header("Zoom Settings")]
    [SerializeField] private float zoomSpeed = 2f;
    [SerializeField] private float zoomSmoothTime = 0.3f;
    
    [Header("Smoothing")]
    [SerializeField] private float rotationSmoothTime = 0.1f;
    [SerializeField] private bool enableSmoothing = true;
    
    // Private variables
    private float currentX = 0f;
    private float currentY = 20f; // Start with slight downward angle
    private float targetDistance;
    private float distanceVelocity;
    
    // Smoothing variables
    private Vector2 rotationVelocity;
    private Vector2 currentRotation;
    private Vector2 targetRotation;
    
    // Input tracking
    private bool isRotating = false;
    private Vector3 lastMousePosition;
    
    private void Start()
    {
        // Initialize camera position
        if (target == null)
        {
            // Create a target at the center of the office if none assigned
            GameObject targetGO = new GameObject("CameraTarget");
            targetGO.transform.position = Vector3.zero;
            target = targetGO.transform;
        }
        
        targetDistance = distance;
        currentRotation = new Vector2(currentX, currentY);
        targetRotation = currentRotation;
        
        UpdateCameraPosition();
        
        Debug.Log("🎥 Office Camera Controller initialized");
    }
    
    private void Update()
    {
        HandleInput();
        UpdateCameraMovement();
    }
    
    private void HandleInput()
    {
        // Mouse rotation
        if (Input.GetMouseButtonDown(0))
        {
            isRotating = true;
            lastMousePosition = Input.mousePosition;
        }
        
        if (Input.GetMouseButtonUp(0))
        {
            isRotating = false;
        }
        
        if (isRotating && Input.GetMouseButton(0))
        {
            Vector3 mouseDelta = Input.mousePosition - lastMousePosition;
            
            // Update target rotation
            targetRotation.x += mouseDelta.x * rotationSpeed * 0.1f;
            targetRotation.y -= mouseDelta.y * rotationSpeed * 0.1f;
            
            // Clamp vertical rotation
            targetRotation.y = Mathf.Clamp(targetRotation.y, minVerticalAngle, maxVerticalAngle);
            
            lastMousePosition = Input.mousePosition;
        }
        
        // Mouse wheel zoom
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (Mathf.Abs(scroll) > 0.01f)
        {
            targetDistance -= scroll * zoomSpeed;
            targetDistance = Mathf.Clamp(targetDistance, minDistance, maxDistance);
        }
        
        // Touch controls for mobile
        HandleTouchInput();
    }
    
    private void HandleTouchInput()
    {
        if (Input.touchCount == 1)
        {
            Touch touch = Input.GetTouch(0);
            
            if (touch.phase == TouchPhase.Moved)
            {
                Vector2 touchDelta = touch.deltaPosition;
                
                targetRotation.x += touchDelta.x * rotationSpeed * 0.05f;
                targetRotation.y -= touchDelta.y * rotationSpeed * 0.05f;
                
                targetRotation.y = Mathf.Clamp(targetRotation.y, minVerticalAngle, maxVerticalAngle);
            }
        }
        else if (Input.touchCount == 2)
        {
            // Pinch to zoom
            Touch touch1 = Input.GetTouch(0);
            Touch touch2 = Input.GetTouch(1);
            
            Vector2 touch1PrevPos = touch1.position - touch1.deltaPosition;
            Vector2 touch2PrevPos = touch2.position - touch2.deltaPosition;
            
            float prevTouchDeltaMag = (touch1PrevPos - touch2PrevPos).magnitude;
            float touchDeltaMag = (touch1.position - touch2.position).magnitude;
            
            float deltaMagnitudeDiff = prevTouchDeltaMag - touchDeltaMag;
            targetDistance += deltaMagnitudeDiff * zoomSpeed * 0.01f;
            targetDistance = Mathf.Clamp(targetDistance, minDistance, maxDistance);
        }
    }
    
    private void UpdateCameraMovement()
    {
        // Smooth rotation
        if (enableSmoothing)
        {
            currentRotation = Vector2.SmoothDamp(currentRotation, targetRotation, ref rotationVelocity, rotationSmoothTime);
        }
        else
        {
            currentRotation = targetRotation;
        }
        
        currentX = currentRotation.x;
        currentY = currentRotation.y;
        
        // Smooth zoom
        distance = Mathf.SmoothDamp(distance, targetDistance, ref distanceVelocity, zoomSmoothTime);
        
        // Update camera position
        UpdateCameraPosition();
    }
    
    private void UpdateCameraPosition()
    {
        if (target == null) return;
        
        // Calculate rotation
        Quaternion rotation = Quaternion.Euler(currentY, currentX, 0);
        
        // Calculate position
        Vector3 direction = rotation * Vector3.back;
        Vector3 position = target.position + direction * distance;
        
        // Apply to camera
        transform.position = position;
        transform.LookAt(target.position);
    }
    
    /// <summary>
    /// Set camera to focus on a specific point in the office
    /// </summary>
    public void FocusOnPoint(Vector3 point, float focusDistance = 5f)
    {
        target.position = point;
        targetDistance = focusDistance;
    }
    
    /// <summary>
    /// Reset camera to default position
    /// </summary>
    public void ResetCamera()
    {
        targetRotation = new Vector2(0f, 20f);
        targetDistance = 8f;
        
        if (target != null)
        {
            target.position = Vector3.zero;
        }
    }
    
    /// <summary>
    /// Set camera rotation smoothly
    /// </summary>
    public void SetRotation(float x, float y)
    {
        targetRotation.x = x;
        targetRotation.y = Mathf.Clamp(y, minVerticalAngle, maxVerticalAngle);
    }
    
    /// <summary>
    /// Set camera distance smoothly
    /// </summary>
    public void SetDistance(float newDistance)
    {
        targetDistance = Mathf.Clamp(newDistance, minDistance, maxDistance);
    }
    
    /// <summary>
    /// Enable or disable camera controls
    /// </summary>
    public void SetControlsEnabled(bool enabled)
    {
        this.enabled = enabled;
    }
    
    private void OnDrawGizmosSelected()
    {
        if (target != null)
        {
            // Draw camera target
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(target.position, 0.5f);
            
            // Draw distance range
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(target.position, minDistance);
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(target.position, maxDistance);
            
            // Draw current camera line
            Gizmos.color = Color.blue;
            Gizmos.DrawLine(target.position, transform.position);
        }
    }
}
