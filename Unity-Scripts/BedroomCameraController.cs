using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// BedroomCameraController - Smooth camera controls optimized for bedroom viewing
/// Includes Unity UI controls for camera presets and settings
/// </summary>
public class BedroomCameraController : MonoBehaviour
{
    [Header("Camera Settings")]
    [SerializeField] private Transform target; // Center of the bedroom
    [SerializeField] private float distance = 6f;
    [SerializeField] private float minDistance = 2f;
    [SerializeField] private float maxDistance = 12f;
    
    [Header("Rotation Settings")]
    [SerializeField] private float rotationSpeed = 2f;
    [SerializeField] private float minVerticalAngle = 5f;
    [SerializeField] private float maxVerticalAngle = 75f;
    
    [Header("Zoom Settings")]
    [SerializeField] private float zoomSpeed = 2f;
    [SerializeField] private float zoomSmoothTime = 0.3f;
    
    [Header("Smoothing")]
    [SerializeField] private float rotationSmoothTime = 0.1f;
    [SerializeField] private bool enableSmoothing = true;
    
    [Header("Camera Presets")]
    [SerializeField] private CameraPreset[] cameraPresets;
    
    [Header("UI Controls")]
    [SerializeField] private Canvas cameraUICanvas;
    [SerializeField] private Button[] presetButtons;
    [SerializeField] private Slider zoomSlider;
    [SerializeField] private Toggle smoothingToggle;
    [SerializeField] private Button resetButton;
    
    // Private variables
    private float currentX = 45f; // Start at 45-degree angle
    private float currentY = 25f; // Slight downward angle
    private float targetDistance;
    private float distanceVelocity;
    
    // Smoothing variables
    private Vector2 rotationVelocity;
    private Vector2 currentRotation;
    private Vector2 targetRotation;
    
    // Input tracking
    private bool isRotating = false;
    private Vector3 lastMousePosition;
    
    // Default values for reset
    private float defaultX = 45f;
    private float defaultY = 25f;
    private float defaultDistance = 6f;
    
    private void Start()
    {
        // Initialize camera position
        if (target == null)
        {
            // Create a target at the center of the bedroom
            GameObject targetGO = new GameObject("BedroomCameraTarget");
            targetGO.transform.position = new Vector3(0, 1.5f, 0); // Bedroom center, slightly elevated
            target = targetGO.transform;
        }
        
        targetDistance = distance;
        currentRotation = new Vector2(currentX, currentY);
        targetRotation = currentRotation;
        
        SetupUI();
        UpdateCameraPosition();
        
        Debug.Log("🎥 Bedroom Camera Controller initialized");
    }
    
    private void SetupUI()
    {
        // Setup zoom slider
        if (zoomSlider != null)
        {
            zoomSlider.minValue = minDistance;
            zoomSlider.maxValue = maxDistance;
            zoomSlider.value = distance;
            zoomSlider.onValueChanged.AddListener(OnZoomSliderChanged);
        }
        
        // Setup smoothing toggle
        if (smoothingToggle != null)
        {
            smoothingToggle.isOn = enableSmoothing;
            smoothingToggle.onValueChanged.AddListener(OnSmoothingToggled);
        }
        
        // Setup reset button
        if (resetButton != null)
        {
            resetButton.onClick.AddListener(ResetCamera);
        }
        
        // Setup preset buttons
        for (int i = 0; i < presetButtons.Length && i < cameraPresets.Length; i++)
        {
            int presetIndex = i; // Capture for closure
            presetButtons[i].onClick.AddListener(() => ApplyCameraPreset(presetIndex));
            
            // Update button text
            Text buttonText = presetButtons[i].GetComponentInChildren<Text>();
            if (buttonText != null)
            {
                buttonText.text = cameraPresets[presetIndex].presetName;
            }
        }
    }
    
    private void Update()
    {
        HandleInput();
        UpdateCameraMovement();
    }
    
    private void HandleInput()
    {
        // Mouse rotation
        if (Input.GetMouseButtonDown(0))
        {
            isRotating = true;
            lastMousePosition = Input.mousePosition;
        }
        
        if (Input.GetMouseButtonUp(0))
        {
            isRotating = false;
        }
        
        if (isRotating && Input.GetMouseButton(0))
        {
            Vector3 mouseDelta = Input.mousePosition - lastMousePosition;
            
            // Update target rotation
            targetRotation.x += mouseDelta.x * rotationSpeed * 0.1f;
            targetRotation.y -= mouseDelta.y * rotationSpeed * 0.1f;
            
            // Clamp vertical rotation
            targetRotation.y = Mathf.Clamp(targetRotation.y, minVerticalAngle, maxVerticalAngle);
            
            lastMousePosition = Input.mousePosition;
        }
        
        // Mouse wheel zoom
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (Mathf.Abs(scroll) > 0.01f)
        {
            targetDistance -= scroll * zoomSpeed;
            targetDistance = Mathf.Clamp(targetDistance, minDistance, maxDistance);
            
            // Update UI slider
            if (zoomSlider != null)
            {
                zoomSlider.value = targetDistance;
            }
        }
        
        // Keyboard shortcuts
        HandleKeyboardInput();
        
        // Touch controls for mobile
        HandleTouchInput();
    }
    
    private void HandleKeyboardInput()
    {
        // Number keys for presets
        for (int i = 1; i <= cameraPresets.Length && i <= 9; i++)
        {
            if (Input.GetKeyDown(KeyCode.Alpha0 + i))
            {
                ApplyCameraPreset(i - 1);
            }
        }
        
        // R key for reset
        if (Input.GetKeyDown(KeyCode.R))
        {
            ResetCamera();
        }
        
        // T key to toggle UI
        if (Input.GetKeyDown(KeyCode.T))
        {
            ToggleCameraUI();
        }
    }
    
    private void HandleTouchInput()
    {
        if (Input.touchCount == 1)
        {
            Touch touch = Input.GetTouch(0);
            
            if (touch.phase == TouchPhase.Moved)
            {
                Vector2 touchDelta = touch.deltaPosition;
                
                targetRotation.x += touchDelta.x * rotationSpeed * 0.05f;
                targetRotation.y -= touchDelta.y * rotationSpeed * 0.05f;
                
                targetRotation.y = Mathf.Clamp(targetRotation.y, minVerticalAngle, maxVerticalAngle);
            }
        }
        else if (Input.touchCount == 2)
        {
            // Pinch to zoom
            Touch touch1 = Input.GetTouch(0);
            Touch touch2 = Input.GetTouch(1);
            
            Vector2 touch1PrevPos = touch1.position - touch1.deltaPosition;
            Vector2 touch2PrevPos = touch2.position - touch2.deltaPosition;
            
            float prevTouchDeltaMag = (touch1PrevPos - touch2PrevPos).magnitude;
            float touchDeltaMag = (touch1.position - touch2.position).magnitude;
            
            float deltaMagnitudeDiff = prevTouchDeltaMag - touchDeltaMag;
            targetDistance += deltaMagnitudeDiff * zoomSpeed * 0.01f;
            targetDistance = Mathf.Clamp(targetDistance, minDistance, maxDistance);
            
            if (zoomSlider != null)
            {
                zoomSlider.value = targetDistance;
            }
        }
    }
    
    private void UpdateCameraMovement()
    {
        // Smooth rotation
        if (enableSmoothing)
        {
            currentRotation = Vector2.SmoothDamp(currentRotation, targetRotation, ref rotationVelocity, rotationSmoothTime);
        }
        else
        {
            currentRotation = targetRotation;
        }
        
        currentX = currentRotation.x;
        currentY = currentRotation.y;
        
        // Smooth zoom
        distance = Mathf.SmoothDamp(distance, targetDistance, ref distanceVelocity, zoomSmoothTime);
        
        // Update camera position
        UpdateCameraPosition();
    }
    
    private void UpdateCameraPosition()
    {
        if (target == null) return;
        
        // Calculate rotation
        Quaternion rotation = Quaternion.Euler(currentY, currentX, 0);
        
        // Calculate position
        Vector3 direction = rotation * Vector3.back;
        Vector3 position = target.position + direction * distance;
        
        // Apply to camera
        transform.position = position;
        transform.LookAt(target.position);
    }
    
    // UI Event Handlers
    private void OnZoomSliderChanged(float value)
    {
        targetDistance = value;
    }
    
    private void OnSmoothingToggled(bool isOn)
    {
        enableSmoothing = isOn;
    }
    
    // Camera Preset Methods
    public void ApplyCameraPreset(int presetIndex)
    {
        if (presetIndex >= 0 && presetIndex < cameraPresets.Length)
        {
            CameraPreset preset = cameraPresets[presetIndex];
            
            targetRotation.x = preset.rotationX;
            targetRotation.y = preset.rotationY;
            targetDistance = preset.distance;
            
            if (zoomSlider != null)
            {
                zoomSlider.value = targetDistance;
            }
            
            Debug.Log($"📷 Applied camera preset: {preset.presetName}");
        }
    }
    
    public void ResetCamera()
    {
        targetRotation.x = defaultX;
        targetRotation.y = defaultY;
        targetDistance = defaultDistance;
        
        if (zoomSlider != null)
        {
            zoomSlider.value = targetDistance;
        }
        
        Debug.Log("🔄 Camera reset to default position");
    }
    
    public void ToggleCameraUI()
    {
        if (cameraUICanvas != null)
        {
            cameraUICanvas.gameObject.SetActive(!cameraUICanvas.gameObject.activeSelf);
        }
    }
    
    // Public API methods
    public void SetCameraPosition(float x, float y, float dist)
    {
        targetRotation.x = x;
        targetRotation.y = Mathf.Clamp(y, minVerticalAngle, maxVerticalAngle);
        targetDistance = Mathf.Clamp(dist, minDistance, maxDistance);
        
        if (zoomSlider != null)
        {
            zoomSlider.value = targetDistance;
        }
    }
    
    public void FocusOnBedroom()
    {
        ApplyCameraPreset(0); // Assume first preset is overview
    }
    
    private void OnDrawGizmosSelected()
    {
        if (target != null)
        {
            // Draw camera target
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(target.position, 0.3f);
            
            // Draw distance range
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(target.position, minDistance);
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(target.position, maxDistance);
            
            // Draw current camera line
            Gizmos.color = Color.blue;
            Gizmos.DrawLine(target.position, transform.position);
        }
    }
}

[System.Serializable]
public class CameraPreset
{
    public string presetName;
    public float rotationX;
    public float rotationY;
    public float distance;
    
    public CameraPreset(string name, float x, float y, float dist)
    {
        presetName = name;
        rotationX = x;
        rotationY = y;
        distance = dist;
    }
}
