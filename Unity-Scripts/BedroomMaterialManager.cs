using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// BedroomMaterialManager - Handles material changes with Unity UI controls
/// This script creates in-Unity UI panels for material selection
/// </summary>
public class BedroomMaterialManager : MonoBehaviour
{
    [Header("Room Renderers")]
    [SerializeField] private Renderer floorRenderer;
    [SerializeField] private Renderer[] wallRenderers;
    [SerializeField] private Renderer ceilingRenderer;
    [SerializeField] private Renderer[] furnitureRenderers;
    
    [Header("Floor Materials")]
    [SerializeField] private Material[] floorMaterials;
    [SerializeField] private string[] floorMaterialNames;
    
    [Header("Wall Materials")]
    [SerializeField] private Material[] wallMaterials;
    [SerializeField] private string[] wallMaterialNames;
    
    [Header("Ceiling Materials")]
    [SerializeField] private Material[] ceilingMaterials;
    [SerializeField] private string[] ceilingMaterialNames;
    
    [Head<PERSON>("UI References")]
    [SerializeField] private Canvas materialUICanvas;
    [SerializeField] private GameObject materialPanelPrefab;
    [SerializeField] private Transform uiParent;
    [SerializeField] private Button toggleUIButton;
    
    // UI Components
    private Dropdown floorDropdown;
    private Dropdown wallDropdown;
    private Dropdown ceilingDropdown;
    private GameObject materialPanel;
    private bool isUIVisible = true;
    
    // Current material indices
    private int currentFloorIndex = 0;
    private int currentWallIndex = 0;
    private int currentCeilingIndex = 0;
    
    private void Start()
    {
        Debug.Log("🛏️ Bedroom Material Manager initialized");
        CreateMaterialUI();
        InitializeDefaultMaterials();
        
        // Setup toggle button
        if (toggleUIButton != null)
        {
            toggleUIButton.onClick.AddListener(ToggleUI);
        }
    }
    
    private void CreateMaterialUI()
    {
        if (materialUICanvas == null)
        {
            Debug.LogError("❌ Material UI Canvas not assigned!");
            return;
        }
        
        // Create main material panel
        materialPanel = new GameObject("MaterialPanel");
        materialPanel.transform.SetParent(uiParent != null ? uiParent : materialUICanvas.transform);
        
        // Add panel background
        Image panelBg = materialPanel.AddComponent<Image>();
        panelBg.color = new Color(0, 0, 0, 0.8f);
        
        // Setup panel rect transform
        RectTransform panelRect = materialPanel.GetComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0, 0);
        panelRect.anchorMax = new Vector2(0.3f, 1);
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // Add vertical layout
        VerticalLayoutGroup layout = materialPanel.AddComponent<VerticalLayoutGroup>();
        layout.padding = new RectOffset(20, 20, 20, 20);
        layout.spacing = 15;
        layout.childControlHeight = false;
        layout.childControlWidth = true;
        layout.childForceExpandWidth = true;
        
        // Create title
        CreateUITitle("🛏️ Bedroom Materials", materialPanel.transform);
        
        // Create material dropdowns
        floorDropdown = CreateMaterialDropdown("Floor Material", floorMaterialNames, materialPanel.transform, OnFloorMaterialChanged);
        wallDropdown = CreateMaterialDropdown("Wall Material", wallMaterialNames, materialPanel.transform, OnWallMaterialChanged);
        ceilingDropdown = CreateMaterialDropdown("Ceiling Material", ceilingMaterialNames, materialPanel.transform, OnCeilingMaterialChanged);
        
        // Create additional controls
        CreateAdditionalControls();
        
        Debug.Log("✅ Material UI created successfully");
    }
    
    private void CreateUITitle(string titleText, Transform parent)
    {
        GameObject titleObj = new GameObject("Title");
        titleObj.transform.SetParent(parent);
        
        Text title = titleObj.AddComponent<Text>();
        title.text = titleText;
        title.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        title.fontSize = 24;
        title.fontStyle = FontStyle.Bold;
        title.color = Color.white;
        title.alignment = TextAnchor.MiddleCenter;
        
        RectTransform titleRect = titleObj.GetComponent<RectTransform>();
        titleRect.sizeDelta = new Vector2(0, 40);
    }
    
    private Dropdown CreateMaterialDropdown(string labelText, string[] options, Transform parent, UnityEngine.Events.UnityAction<int> onValueChanged)
    {
        // Create container
        GameObject container = new GameObject(labelText + "Container");
        container.transform.SetParent(parent);
        
        VerticalLayoutGroup containerLayout = container.AddComponent<VerticalLayoutGroup>();
        containerLayout.spacing = 5;
        containerLayout.childControlHeight = false;
        containerLayout.childControlWidth = true;
        containerLayout.childForceExpandWidth = true;
        
        RectTransform containerRect = container.GetComponent<RectTransform>();
        containerRect.sizeDelta = new Vector2(0, 70);
        
        // Create label
        GameObject labelObj = new GameObject("Label");
        labelObj.transform.SetParent(container.transform);
        
        Text label = labelObj.AddComponent<Text>();
        label.text = labelText;
        label.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        label.fontSize = 16;
        label.color = new Color(1, 0.84f, 0); // Gold color
        
        RectTransform labelRect = labelObj.GetComponent<RectTransform>();
        labelRect.sizeDelta = new Vector2(0, 25);
        
        // Create dropdown
        GameObject dropdownObj = new GameObject("Dropdown");
        dropdownObj.transform.SetParent(container.transform);
        
        Image dropdownBg = dropdownObj.AddComponent<Image>();
        dropdownBg.color = new Color(0.2f, 0.2f, 0.2f, 0.9f);
        
        Dropdown dropdown = dropdownObj.AddComponent<Dropdown>();
        
        // Setup dropdown template (simplified)
        GameObject template = new GameObject("Template");
        template.transform.SetParent(dropdownObj.transform);
        template.SetActive(false);
        
        RectTransform templateRect = template.AddComponent<RectTransform>();
        templateRect.anchorMin = new Vector2(0, 0);
        templateRect.anchorMax = new Vector2(1, 0);
        templateRect.pivot = new Vector2(0.5f, 1);
        templateRect.sizeDelta = new Vector2(0, 150);
        
        // Add options
        dropdown.options.Clear();
        foreach (string option in options)
        {
            dropdown.options.Add(new Dropdown.OptionData(option));
        }
        
        dropdown.onValueChanged.AddListener(onValueChanged);
        
        RectTransform dropdownRect = dropdownObj.GetComponent<RectTransform>();
        dropdownRect.sizeDelta = new Vector2(0, 35);
        
        return dropdown;
    }
    
    private void CreateAdditionalControls()
    {
        // Create reset button
        GameObject resetButtonObj = new GameObject("ResetButton");
        resetButtonObj.transform.SetParent(materialPanel.transform);
        
        Image resetBg = resetButtonObj.AddComponent<Image>();
        resetBg.color = new Color(0.8f, 0.2f, 0.2f, 0.8f);
        
        Button resetButton = resetButtonObj.AddComponent<Button>();
        resetButton.onClick.AddListener(ResetToDefaults);
        
        // Button text
        GameObject resetTextObj = new GameObject("Text");
        resetTextObj.transform.SetParent(resetButtonObj.transform);
        
        Text resetText = resetTextObj.AddComponent<Text>();
        resetText.text = "🔄 Reset to Defaults";
        resetText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        resetText.fontSize = 14;
        resetText.color = Color.white;
        resetText.alignment = TextAnchor.MiddleCenter;
        
        RectTransform resetTextRect = resetTextObj.GetComponent<RectTransform>();
        resetTextRect.anchorMin = Vector2.zero;
        resetTextRect.anchorMax = Vector2.one;
        resetTextRect.offsetMin = Vector2.zero;
        resetTextRect.offsetMax = Vector2.zero;
        
        RectTransform resetButtonRect = resetButtonObj.GetComponent<RectTransform>();
        resetButtonRect.sizeDelta = new Vector2(0, 40);
    }
    
    private void InitializeDefaultMaterials()
    {
        // Apply default materials
        if (floorMaterials.Length > 0) ApplyFloorMaterial(0);
        if (wallMaterials.Length > 0) ApplyWallMaterial(0);
        if (ceilingMaterials.Length > 0) ApplyCeilingMaterial(0);
    }
    
    // Material change handlers
    private void OnFloorMaterialChanged(int index)
    {
        ApplyFloorMaterial(index);
        currentFloorIndex = index;
        Debug.Log($"🛏️ Floor material changed to: {floorMaterialNames[index]}");
    }
    
    private void OnWallMaterialChanged(int index)
    {
        ApplyWallMaterial(index);
        currentWallIndex = index;
        Debug.Log($"🛏️ Wall material changed to: {wallMaterialNames[index]}");
    }
    
    private void OnCeilingMaterialChanged(int index)
    {
        ApplyCeilingMaterial(index);
        currentCeilingIndex = index;
        Debug.Log($"🛏️ Ceiling material changed to: {ceilingMaterialNames[index]}");
    }
    
    // Material application methods
    private void ApplyFloorMaterial(int index)
    {
        if (index >= 0 && index < floorMaterials.Length && floorRenderer != null)
        {
            floorRenderer.material = floorMaterials[index];
        }
    }
    
    private void ApplyWallMaterial(int index)
    {
        if (index >= 0 && index < wallMaterials.Length && wallRenderers != null)
        {
            foreach (Renderer wallRenderer in wallRenderers)
            {
                if (wallRenderer != null)
                {
                    wallRenderer.material = wallMaterials[index];
                }
            }
        }
    }
    
    private void ApplyCeilingMaterial(int index)
    {
        if (index >= 0 && index < ceilingMaterials.Length && ceilingRenderer != null)
        {
            ceilingRenderer.material = ceilingMaterials[index];
        }
    }
    
    // UI Control methods
    public void ToggleUI()
    {
        isUIVisible = !isUIVisible;
        if (materialPanel != null)
        {
            materialPanel.SetActive(isUIVisible);
        }
        Debug.Log($"🎮 Material UI toggled: {(isUIVisible ? "Visible" : "Hidden")}");
    }
    
    public void ResetToDefaults()
    {
        floorDropdown.value = 0;
        wallDropdown.value = 0;
        ceilingDropdown.value = 0;
        
        ApplyFloorMaterial(0);
        ApplyWallMaterial(0);
        ApplyCeilingMaterial(0);
        
        Debug.Log("🔄 Materials reset to defaults");
    }
    
    // External API for web interface (optional)
    public void SetMaterialFromWeb(string jsonData)
    {
        try
        {
            MaterialChangeData data = JsonUtility.FromJson<MaterialChangeData>(jsonData);
            
            switch (data.elementType.ToLower())
            {
                case "floor":
                    if (data.materialIndex >= 0 && data.materialIndex < floorMaterials.Length)
                    {
                        floorDropdown.value = data.materialIndex;
                        ApplyFloorMaterial(data.materialIndex);
                    }
                    break;
                case "walls":
                    if (data.materialIndex >= 0 && data.materialIndex < wallMaterials.Length)
                    {
                        wallDropdown.value = data.materialIndex;
                        ApplyWallMaterial(data.materialIndex);
                    }
                    break;
                case "ceiling":
                    if (data.materialIndex >= 0 && data.materialIndex < ceilingMaterials.Length)
                    {
                        ceilingDropdown.value = data.materialIndex;
                        ApplyCeilingMaterial(data.materialIndex);
                    }
                    break;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Error setting material from web: {e.Message}");
        }
    }
}

[System.Serializable]
public class MaterialChangeData
{
    public string elementType;
    public int materialIndex;
    public string materialName;
}
