# 🚀 **QUICK START - 5 Minutes to Realistic Bedroom**

## ⚡ **SUPER FAST SETUP**

### **1. Create Project (1 min)**
- Unity Hub → New Project → **3D (HDRP)** → Name: `RealisticBedroomDesigner`

### **2. Add Scripts (2 min)**
- Create folder: `Assets/Scripts/`
- Add `RealisticBedroomCreator.cs` and `RealisticCameraController.cs` to Scripts folder

### **3. Create Bedroom (1 min)**
- Hierarchy → Create Empty → Name: "BedroomCreator"
- Add Component → RealisticBedroomCreator
- Click **"🛏️ Create Realistic Bedroom"**

### **4. Test (1 min)**
- Press Play ▶️
- Use **WASD + Mouse** to explore
- Press **T** for materials UI

---

## 🎮 **ESSENTIAL CONTROLS**

| Key | Action |
|-----|--------|
| **WASD** | Move around |
| **Mouse** | Look around |
| **T** | Toggle material UI |
| **R** | Reset materials |
| **SPACE** | Random design |
| **ESC** | Toggle cursor |

---

## 🛏️ **WHAT YOU GET**

✅ **Photorealistic bedroom** with HDRP graphics  
✅ **Complete furniture** (bed, nightstands, dresser, chair)  
✅ **Professional lighting** (sunlight, ceiling, bedside lamps)  
✅ **Material system** (4 options each for floor, walls, ceiling)  
✅ **Smooth camera** controls with room constraints  
✅ **Interactive UI** for real-time customization  

---

## 🔧 **TROUBLESHOOTING**

**Bedroom not appearing?**
- Check Console for errors
- Try clicking "Create Realistic Bedroom" again
- Ensure using HDRP template

**Camera not working?**
- Press ESC to unlock cursor
- Check RealisticCameraController is attached
- Restart Play mode

**Materials look wrong?**
- Press R to reset
- Press SPACE for random
- Check Lighting → Generate Lighting

---

## 📁 **FILES NEEDED**

1. **RealisticBedroomCreator.cs** - Main bedroom generator
2. **RealisticCameraController.cs** - WASD + Mouse controls

**That's it! Your realistic bedroom is ready in 5 minutes! 🛏️✨**
