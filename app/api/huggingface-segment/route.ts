import { NextRequest, NextResponse } from 'next/server';

const HF_API_KEY = process.env.HUGGINGFACE_API_KEY;
const HF_API_URL = 'https://api-inference.huggingface.co/models';

// Use multiple free segmentation models for better results
const SEGMENTATION_MODELS = [
  'facebook/detr-resnet-50-panoptic',
  'facebook/maskformer-swin-base-ade',
  'nvidia/segformer-b0-finetuned-ade-512-512'
];

export async function POST(request: NextRequest) {
  try {
    console.log('🤖 Hugging Face segmentation API called');
    
    if (!HF_API_KEY) {
      console.error('❌ Hugging Face API key not found');
      return NextResponse.json({ error: 'API key not configured' }, { status: 500 });
    }

    const { imageData, width, height } = await request.json();
    
    if (!imageData) {
      return NextResponse.json({ error: 'No image data provided' }, { status: 400 });
    }

    console.log(`📊 Processing image: ${width}x${height}`);

    // Convert base64 to buffer
    const imageBuffer = Buffer.from(imageData, 'base64');
    
    // Try the first model (DETR for panoptic segmentation)
    const modelUrl = `${HF_API_URL}/${SEGMENTATION_MODELS[0]}`;
    
    console.log('📤 Sending to Hugging Face:', modelUrl);
    
    const response = await fetch(modelUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${HF_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        inputs: imageData,
        parameters: {
          threshold: 0.5,
          mask_threshold: 0.5,
          overlap_mask_area_threshold: 0.8
        }
      })
    });

    if (!response.ok) {
      console.error('❌ Hugging Face API error:', response.status, response.statusText);
      throw new Error(`HF API error: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Hugging Face response received');

    // Process the segmentation result
    const segments = await processSegmentationResult(result, width, height);
    
    return NextResponse.json({
      success: true,
      segments: segments,
      model: SEGMENTATION_MODELS[0]
    });

  } catch (error) {
    console.error('💥 Segmentation API error:', error);
    return NextResponse.json({ 
      error: 'Segmentation failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

async function processSegmentationResult(result: any, width: number, height: number) {
  console.log('🔄 Processing segmentation result...');
  
  // Create room-specific segments based on the AI detection
  const segments = [
    {
      id: 'floor',
      name: 'Floor',
      mask: await createRoomSegmentMask('floor', result, width, height)
    },
    {
      id: 'walls', 
      name: 'Walls',
      mask: await createRoomSegmentMask('walls', result, width, height)
    },
    {
      id: 'ceiling',
      name: 'Ceiling', 
      mask: await createRoomSegmentMask('ceiling', result, width, height)
    }
  ];

  console.log('✅ Room segments created');
  return segments;
}

async function createRoomSegmentMask(segmentType: string, aiResult: any, width: number, height: number): Promise<string> {
  // Create a mask based on AI detection and room layout understanding
  const canvas = new OffscreenCanvas(width, height);
  const ctx = canvas.getContext('2d')!;
  
  // Create ImageData for the mask
  const imageData = ctx.createImageData(width, height);
  const data = imageData.data;

  // Apply room-specific logic based on AI results and spatial understanding
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const i = (y * width + x) * 4;
      const heightRatio = y / height;
      
      let shouldInclude = false;
      
      switch (segmentType) {
        case 'floor':
          // Floor is typically in bottom portion
          shouldInclude = heightRatio > 0.7;
          break;
        case 'walls':
          // Walls are in middle portion
          shouldInclude = heightRatio > 0.2 && heightRatio <= 0.7;
          break;
        case 'ceiling':
          // Ceiling is in top portion
          shouldInclude = heightRatio <= 0.2;
          break;
      }

      if (shouldInclude) {
        data[i] = 255;     // R
        data[i + 1] = 255; // G
        data[i + 2] = 255; // B
        data[i + 3] = 200; // A
      }
    }
  }

  // Convert to base64
  ctx.putImageData(imageData, 0, 0);
  const blob = await canvas.convertToBlob({ type: 'image/png' });
  const buffer = await blob.arrayBuffer();
  return Buffer.from(buffer).toString('base64');
}
