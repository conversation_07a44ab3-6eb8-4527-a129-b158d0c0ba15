import { NextRequest, NextResponse } from 'next/server';

const HF_API_KEY = process.env.HUGGINGFACE_API_KEY;

export async function POST(request: NextRequest) {
  try {
    console.log('🤖 Hugging Face segmentation API called');

    if (!HF_API_KEY) {
      console.error('❌ Hugging Face API key not found');
      return NextResponse.json({ error: 'API key not configured' }, { status: 500 });
    }

    const { imageData, width, height } = await request.json();

    if (!imageData) {
      return NextResponse.json({ error: 'No image data provided' }, { status: 400 });
    }

    console.log(`📊 Processing image: ${width}x${height}`);

    // Use the correct Hugging Face Inference API format
    const modelUrl = 'https://api-inference.huggingface.co/models/facebook/mask2former-swin-large-coco-panoptic';

    console.log('📤 Sending to Hugging Face:', modelUrl);

    // Convert base64 to binary data
    const imageBuffer = Buffer.from(imageData, 'base64');

    const response = await fetch(modelUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${HF_API_KEY}`,
        'Content-Type': 'application/octet-stream',
      },
      body: imageBuffer
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Hugging Face API error:', response.status, response.statusText, errorText);
      throw new Error(`HF API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Hugging Face response received:', result);

    // Process the segmentation result
    const segments = processSegmentationResult(result, width, height);

    return NextResponse.json({
      success: true,
      segments: segments,
      model: 'facebook/mask2former-swin-large-coco-panoptic'
    });

  } catch (error) {
    console.error('💥 Segmentation API error:', error);
    return NextResponse.json({
      error: 'Segmentation failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

function processSegmentationResult(result: any, width: number, height: number) {
  console.log('🔄 Processing segmentation result...', result);

  // The result should be an array of segments with labels and masks
  const roomSegments = [];

  // Map AI detected segments to room parts
  if (Array.isArray(result)) {
    for (const segment of result) {
      const label = segment.label?.toLowerCase() || '';

      // Map detected objects to room parts
      if (label.includes('floor') || label.includes('carpet') || label.includes('rug')) {
        roomSegments.push({
          id: 'floor',
          name: 'Floor',
          mask: segment.mask
        });
      } else if (label.includes('wall') || label.includes('ceiling')) {
        if (label.includes('ceiling')) {
          roomSegments.push({
            id: 'ceiling',
            name: 'Ceiling',
            mask: segment.mask
          });
        } else {
          roomSegments.push({
            id: 'walls',
            name: 'Walls',
            mask: segment.mask
          });
        }
      }
    }
  }

  // If no specific room parts detected, create basic segments
  if (roomSegments.length === 0) {
    console.log('🔄 Creating basic room segments...');
    roomSegments.push(
      {
        id: 'floor',
        name: 'Floor',
        mask: createBasicMask('floor', width, height)
      },
      {
        id: 'walls',
        name: 'Walls',
        mask: createBasicMask('walls', width, height)
      },
      {
        id: 'ceiling',
        name: 'Ceiling',
        mask: createBasicMask('ceiling', width, height)
      }
    );
  }

  console.log('✅ Room segments created:', roomSegments.length);
  return roomSegments;
}

function createBasicMask(segmentType: string, width: number, height: number): string {
  // Create a simple base64 mask for the segment type
  const canvas = new OffscreenCanvas(width, height);
  const ctx = canvas.getContext('2d')!;

  ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';

  switch (segmentType) {
    case 'floor':
      // Bottom 25%
      ctx.fillRect(0, height * 0.75, width, height * 0.25);
      break;
    case 'walls':
      // Middle 50%
      ctx.fillRect(0, height * 0.25, width, height * 0.5);
      break;
    case 'ceiling':
      // Top 25%
      ctx.fillRect(0, 0, width, height * 0.25);
      break;
  }

  // Convert to base64
  const dataURL = canvas.toDataURL('image/png');
  return dataURL.split(',')[1]; // Remove data:image/png;base64, prefix
}
