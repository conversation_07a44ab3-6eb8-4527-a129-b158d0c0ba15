import { NextRequest, NextResponse } from 'next/server';

const HF_API_KEY = process.env.HUGGINGFACE_API_KEY;

export async function POST(request: NextRequest) {
  try {
    console.log('🤖 Hugging Face segmentation API called');

    if (!HF_API_KEY) {
      console.error('❌ Hugging Face API key not found');
      return NextResponse.json({ error: 'API key not configured' }, { status: 500 });
    }

    const { imageData, width, height } = await request.json();

    if (!imageData) {
      return NextResponse.json({ error: 'No image data provided' }, { status: 400 });
    }

    console.log(`📊 Processing image: ${width}x${height}`);

    // Use the correct Hugging Face Inference API format
    const modelUrl = 'https://api-inference.huggingface.co/models/facebook/mask2former-swin-large-coco-panoptic';

    console.log('📤 Sending to Hugging Face:', modelUrl);

    // Convert base64 to binary data
    const imageBuffer = Buffer.from(imageData, 'base64');

    const response = await fetch(modelUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${HF_API_KEY}`,
        'Content-Type': 'application/octet-stream',
      },
      body: imageBuffer
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Hugging Face API error:', response.status, response.statusText, errorText);
      throw new Error(`HF API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Hugging Face response received:', result);

    // Process the segmentation result
    const segments = processSegmentationResult(result, width, height);

    return NextResponse.json({
      success: true,
      segments: segments,
      model: 'facebook/mask2former-swin-large-coco-panoptic'
    });

  } catch (error) {
    console.error('💥 Segmentation API error:', error);
    return NextResponse.json({
      error: 'Segmentation failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

function processSegmentationResult(result: any, width: number, height: number) {
  console.log('🔄 Processing segmentation result...', result);

  // Always create basic segments for now (we'll enhance this later)
  console.log('🔄 Creating basic room segments...');
  const roomSegments = [
    {
      id: 'floor',
      name: 'Floor',
      mask: createBasicMaskData('floor', width, height)
    },
    {
      id: 'walls',
      name: 'Walls',
      mask: createBasicMaskData('walls', width, height)
    },
    {
      id: 'ceiling',
      name: 'Ceiling',
      mask: createBasicMaskData('ceiling', width, height)
    }
  ];

  console.log('✅ Room segments created:', roomSegments.length);
  return roomSegments;
}

function createBasicMaskData(segmentType: string, width: number, height: number): string {
  // Create mask data as a simple JSON representation that the frontend can parse
  const maskInfo = {
    type: segmentType,
    width: width,
    height: height,
    region: getMaskRegion(segmentType)
  };

  // Return as base64 encoded JSON for now
  return Buffer.from(JSON.stringify(maskInfo)).toString('base64');
}

function getMaskRegion(segmentType: string) {
  switch (segmentType) {
    case 'floor':
      return { startY: 0.75, endY: 1.0 }; // Bottom 25%
    case 'walls':
      return { startY: 0.25, endY: 0.75 }; // Middle 50%
    case 'ceiling':
      return { startY: 0.0, endY: 0.25 }; // Top 25%
    default:
      return { startY: 0.0, endY: 1.0 };
  }
}
