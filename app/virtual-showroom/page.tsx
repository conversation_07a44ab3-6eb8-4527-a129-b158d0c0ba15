'use client';

import React, { useState } from 'react';
import { Suspense } from 'react';
import HeaderComponent from '../Components/HeaderComponent/HeaderComponent';
import FooterComponent from '../Components/FooterComponent/FooterComponent';
import RoomConfigurator from '../Components/RoomConfigurator/RoomConfigurator';
import ShowroomComponent from '../Components/VirtualComponent/ShowroomComponent';
import OfficeShowroom from '../Components/OfficeShowroomComponent/OfficeShowroom';
import ModelLibrary from '../Components/RoomConfigurator/components/ModelLibrary';
import VRGallery from '../Components/VRGallery/VRGallery';
import { FloorPlan } from '../Components/RoomConfigurator/types';
import styles from './page.module.css';

type ShowroomMode = 'home' | 'configurator' | 'gallery' | 'office' | 'models' | 'explore-gallery';

export default function VirtualShowroomPage() {
  const [currentMode, setCurrentMode] = useState<ShowroomMode>('home');
  const [savedFloorPlan, setSavedFloorPlan] = useState<FloorPlan | null>(null);

  const handleConfigurationChange = (floorPlan: FloorPlan) => {
    setSavedFloorPlan(floorPlan);
  };

  const handleExport = (data: any) => {
    // Create downloadable file
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `room-configuration-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderContent = () => {
    switch (currentMode) {
      case 'configurator':
        return (
          <Suspense fallback={<div className={styles.loading}>Loading 3D Room Configurator...</div>}>
            <RoomConfigurator
              initialFloorPlan={savedFloorPlan || undefined}
              onConfigurationChange={handleConfigurationChange}
              onExport={handleExport}
            />
          </Suspense>
        );
      
      case 'gallery':
        return (
          <div className={styles.galleryContainer}>
            <div className={styles.galleryHeader}>
              <h1>Virtual Gallery</h1>
              <p>Explore our immersive room experiences</p>
            </div>
            <ShowroomComponent />
          </div>
        );
      
      case 'office':
        return (
          <div className={styles.officeContainer}>
            <div className={styles.officeHeader}>
              <h1>Office Showroom</h1>
              <p>Interactive office space with product hotspots</p>
            </div>
            <OfficeShowroom />
          </div>
        );
      
      case 'models':
        return (
          <div className={styles.modelsContainer}>
            <div className={styles.modelsHeader}>
              <h1>3D Model Library</h1>
              <p>Browse and manage 3D models for your room configurations</p>
            </div>
            <ModelLibrary showUploader={true} />
          </div>
        );

      case 'explore-gallery':
        return <VRGallery />;
      
      default:
        return (
          <div className={styles.homeContainer}>
            {/* Hero Section */}
            <section className={styles.hero}>
              <div className={styles.heroContent}>
                <h1>Virtual Showroom</h1>
                <p>Experience our products in immersive 3D environments</p>
                <div className={styles.heroButtons}>
                  <button
                    className={styles.primaryButton}
                    onClick={() => setCurrentMode('configurator')}
                  >
                    Start 3D Room Designer
                  </button>
                  <button
                    className={styles.secondaryButton}
                    onClick={() => setCurrentMode('gallery')}
                  >
                    Design Your Room with AI
                  </button>
                  <button
                    className={styles.vrGalleryButton}
                    onClick={() => setCurrentMode('explore-gallery')}
                  >
                    🏛️ Explore VR Gallery
                  </button>
                </div>
              </div>
              <div className={styles.heroImage}>
                <div className={styles.placeholderImage}>
                  🏠 3D Room Preview
                </div>
              </div>
            </section>

            {/* Features Grid */}
            <section className={styles.features}>
              <div className={styles.featuresGrid}>
                <div 
                  className={styles.featureCard}
                  onClick={() => setCurrentMode('configurator')}
                >
                  <div className={styles.featureIcon}>🏗️</div>
                  <h3>3D Room Configurator</h3>
                  <p>Design custom rooms with precise dimensions and materials</p>
                  <ul>
                    <li>Custom room dimensions</li>
                    <li>Material selection</li>
                    <li>Real-time 3D preview</li>
                    <li>Export configurations</li>
                  </ul>
                </div>

                <div
                  className={styles.featureCard}
                  onClick={() => setCurrentMode('gallery')}
                >
                  <div className={styles.featureIcon}>🤖</div>
                  <h3>AI Room Designer</h3>
                  <p>Upload your room photo and transform it with AI-powered design</p>
                  <ul>
                    <li>Upload room photos</li>
                    <li>AI-powered transformations</li>
                    <li>Custom design prompts</li>
                    <li>Before/after comparisons</li>
                  </ul>
                </div>

                <div
                  className={styles.featureCard}
                  onClick={() => setCurrentMode('explore-gallery')}
                >
                  <div className={styles.featureIcon}>🏛️</div>
                  <h3>Explore VR Gallery</h3>
                  <p>Immersive 3D rooms with real-time customization capabilities</p>
                  <ul>
                    <li>VR-like navigation</li>
                    <li>Real-time material changes</li>
                    <li>Multiple room types</li>
                    <li>Interactive customization</li>
                  </ul>
                </div>

                <div
                  className={styles.featureCard}
                  onClick={() => setCurrentMode('office')}
                >
                  <div className={styles.featureIcon}>🏢</div>
                  <h3>Office Showroom</h3>
                  <p>Interactive office space with detailed product information</p>
                  <ul>
                    <li>Product hotspots</li>
                    <li>Detailed specifications</li>
                    <li>Office layouts</li>
                    <li>Professional designs</li>
                  </ul>
                </div>

                <div 
                  className={styles.featureCard}
                  onClick={() => setCurrentMode('models')}
                >
                  <div className={styles.featureIcon}>📦</div>
                  <h3>3D Model Library</h3>
                  <p>Browse and manage 3D models for room configurations</p>
                  <ul>
                    <li>Extensive model catalog</li>
                    <li>Upload custom models</li>
                    <li>Search and filter</li>
                    <li>Model management</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Benefits Section */}
            <section className={styles.benefits}>
              <div className={styles.benefitsContent}>
                <h2>Why Choose Our Virtual Showroom?</h2>
                <div className={styles.benefitsGrid}>
                  <div className={styles.benefit}>
                    <div className={styles.benefitIcon}>⚡</div>
                    <h4>Fast & Efficient</h4>
                    <p>Design and visualize rooms in minutes, not hours</p>
                  </div>
                  <div className={styles.benefit}>
                    <div className={styles.benefitIcon}>🎯</div>
                    <h4>Precise Control</h4>
                    <p>Input exact dimensions from your floor plans</p>
                  </div>
                  <div className={styles.benefit}>
                    <div className={styles.benefitIcon}>🎨</div>
                    <h4>Material Library</h4>
                    <p>Extensive collection of flooring, ceiling, and wall materials</p>
                  </div>
                  <div className={styles.benefit}>
                    <div className={styles.benefitIcon}>💾</div>
                    <h4>Save & Export</h4>
                    <p>Save configurations and export for sharing</p>
                  </div>
                </div>
              </div>
            </section>
          </div>
        );
    }
  };

  return (
    <div className={styles.page}>
      <HeaderComponent />
      
      {/* Navigation */}
      {currentMode !== 'home' && (
        <nav className={styles.navigation}>
          <div className={styles.navContent}>
            <button 
              className={styles.backButton}
              onClick={() => setCurrentMode('home')}
            >
              ← Back to Home
            </button>
            
            <div className={styles.navTabs}>
              <button
                className={currentMode === 'configurator' ? styles.active : ''}
                onClick={() => setCurrentMode('configurator')}
              >
                3D Designer
              </button>
              <button
                className={currentMode === 'gallery' ? styles.active : ''}
                onClick={() => setCurrentMode('gallery')}
              >
                AI Designer
              </button>
              <button
                className={currentMode === 'explore-gallery' ? styles.active : ''}
                onClick={() => setCurrentMode('explore-gallery')}
              >
                VR Gallery
              </button>
              <button
                className={currentMode === 'office' ? styles.active : ''}
                onClick={() => setCurrentMode('office')}
              >
                Office
              </button>
              <button
                className={currentMode === 'models' ? styles.active : ''}
                onClick={() => setCurrentMode('models')}
              >
                Models
              </button>
            </div>
          </div>
        </nav>
      )}

      {/* Main Content */}
      <main className={styles.main}>
        {renderContent()}
      </main>

      {currentMode === 'home' && <FooterComponent />}
    </div>
  );
}
