.page {
  min-height: 100vh;
  background: #f8f9fa;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Navigation */
.navigation {
  background: white;
  border-bottom: 2px solid #e9ecef;
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.navContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.backButton {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.backButton:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.navTabs {
  display: flex;
  gap: 0.5rem;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 0.5rem;
}

.navTabs button {
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  color: #6c757d;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.navTabs button:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.navTabs button.active {
  background: #667eea;
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* Main Content */
.main {
  flex: 1;
}

/* Home Container */
.homeContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section */
.hero {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  padding: 4rem 0;
  min-height: 60vh;
}

.heroContent h1 {
  font-size: 3.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  line-height: 1.1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.heroContent p {
  font-size: 1.25rem;
  color: #6c757d;
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

.heroButtons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.primaryButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.primaryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
}

.secondaryButton {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 1rem 2rem;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.secondaryButton:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.vrGalleryButton {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.vrGalleryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.5);
}

.heroImage {
  display: flex;
  justify-content: center;
  align-items: center;
}

.placeholderImage {
  width: 400px;
  height: 300px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px dashed #dee2e6;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: #6c757d;
  font-weight: 600;
}

/* Features Grid */
.features {
  padding: 4rem 0;
  background: white;
  margin: 2rem 0;
  border-radius: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  padding: 0 2rem;
  max-width: 1600px;
  margin: 0 auto;
}

.featureCard {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 16px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.featureCard:hover {
  border-color: #667eea;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  background: white;
}

.featureIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.featureCard h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.featureCard p {
  margin: 0 0 1.5rem 0;
  color: #6c757d;
  line-height: 1.6;
}

.featureCard ul {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}

.featureCard li {
  color: #495057;
  padding: 0.25rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.featureCard li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: bold;
}

/* Benefits Section */
.benefits {
  padding: 4rem 0;
}

.benefitsContent h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 3rem 0;
  font-weight: 700;
}

.benefitsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.benefit {
  text-align: center;
  padding: 1.5rem;
}

.benefitIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.benefit h4 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.benefit p {
  margin: 0;
  color: #6c757d;
  line-height: 1.6;
}

/* Container Styles for Different Modes */
.galleryContainer,
.officeContainer,
.modelsContainer {
  min-height: calc(100vh - 200px);
}

.galleryHeader,
.officeHeader,
.modelsHeader {
  text-align: center;
  padding: 2rem;
  background: white;
  margin-bottom: 2rem;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.galleryHeader h1,
.officeHeader h1,
.modelsHeader h1 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 2.5rem;
  font-weight: 700;
}

.galleryHeader p,
.officeHeader p,
.modelsHeader p {
  margin: 0;
  color: #6c757d;
  font-size: 1.1rem;
}

/* Loading State */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh;
  font-size: 1.2rem;
  color: #6c757d;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .heroContent h1 {
    font-size: 3rem;
  }
  
  .featuresGrid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .placeholderImage {
    width: 300px;
    height: 225px;
  }
}

@media (max-width: 768px) {
  .homeContainer {
    padding: 0 1rem;
  }
  
  .navContent {
    padding: 0 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .navTabs {
    width: 100%;
    justify-content: center;
  }
  
  .navTabs button {
    flex: 1;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .hero {
    padding: 2rem 0;
  }
  
  .heroContent h1 {
    font-size: 2.5rem;
  }
  
  .heroButtons {
    justify-content: center;
  }
  
  .primaryButton,
  .secondaryButton,
  .vrGalleryButton {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
  
  .featuresGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .benefitsGrid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .placeholderImage {
    width: 250px;
    height: 188px;
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .heroContent h1 {
    font-size: 2rem;
  }
  
  .heroContent p {
    font-size: 1.1rem;
  }
  
  .heroButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton,
  .vrGalleryButton {
    width: 100%;
    max-width: 300px;
  }
  
  .featureCard {
    padding: 1.5rem;
  }
  
  .benefitsContent h2 {
    font-size: 2rem;
  }
  
  .placeholderImage {
    width: 200px;
    height: 150px;
    font-size: 1.2rem;
  }
}

/* Focus States */
.primaryButton:focus,
.secondaryButton:focus,
.vrGalleryButton:focus,
.backButton:focus,
.navTabs button:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.featureCard:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .featureCard {
    border-color: #000;
  }
  
  .featureCard:hover {
    border-color: #0066cc;
    background: #e6f3ff;
  }
  
  .navTabs button.active {
    background: #000;
    color: white;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .primaryButton,
  .secondaryButton,
  .vrGalleryButton,
  .featureCard,
  .backButton,
  .navTabs button {
    transition: none;
  }

  .primaryButton:hover,
  .secondaryButton:hover,
  .vrGalleryButton:hover,
  .featureCard:hover {
    transform: none;
  }
}
