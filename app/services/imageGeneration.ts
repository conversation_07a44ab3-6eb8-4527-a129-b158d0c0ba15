// Image generation service using Hugging Face Stable Diffusion
export interface MaterialOptions {
  flooring: {
    id: string;
    name: string;
    description: string;
  };
  walls: {
    id: string;
    name: string;
    description: string;
  };
  ceiling: {
    id: string;
    name: string;
    description: string;
  };
  lighting: {
    id: string;
    name: string;
    description: string;
  };
}

export interface RoomData {
  title: string;
  category: string;
  roomSize: string;
  originalImage: string;
}

class ImageGenerationService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_HUGGINGFACE_API_KEY || '';
    this.baseUrl = 'https://api-inference.huggingface.co/models';

    // Debug logging
    console.log('🔑 API Key loaded:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT FOUND');
    console.log('🌐 Base URL:', this.baseUrl);
  }

  /**
   * Generate a customized room image using Stable Diffusion
   */
  async generateCustomizedRoom(
    roomData: RoomData,
    materials: MaterialOptions
  ): Promise<string> {
    try {
      console.log('🎨 Starting room customization...');
      console.log('📋 Room data:', roomData);
      console.log('🎯 Materials:', materials);

      // Check if this is an image-to-image request (user uploaded image)
      if (roomData.originalImage && materials.flooring.id === 'custom') {
        console.log('🖼️ Processing image-to-image generation...');
        return await this.generateImageToImageDesign(roomData.originalImage, materials.flooring.description);
      }

      // Create a detailed prompt based on room and materials
      const prompt = this.createPrompt(roomData, materials);

      // Generate image using Stable Diffusion
      const imageBlob = await this.callStableDiffusion(prompt);

      // Convert blob to base64 URL for display
      const imageUrl = await this.blobToBase64(imageBlob);

      console.log('✅ Room customization completed successfully');
      return imageUrl;
    } catch (error) {
      console.error('❌ Error generating customized room:', error);
      throw new Error('Failed to generate customized room image');
    }
  }

  /**
   * Generate image-to-image design based on uploaded image and user prompt
   */
  private async generateImageToImageDesign(originalImage: string, userPrompt: string): Promise<string> {
    try {
      console.log('🖼️ Processing REAL image-to-image generation...');

      // Convert base64 to blob for upload
      const imageBlob = await this.base64ToBlob(originalImage);

      // Use proper image-to-image model
      const resultBlob = await this.callImageToImageAPI(imageBlob, userPrompt);

      // Convert result back to base64
      const imageUrl = await this.blobToBase64(resultBlob);

      console.log('✅ Image-to-image generation completed');
      return imageUrl;

    } catch (error) {
      console.error('❌ Error in generateImageToImageDesign:', error);
      throw error;
    }
  }

  /**
   * Analyze uploaded room image to extract descriptive features
   */
  private async analyzeRoomImage(imageDataUrl: string): Promise<string> {
    try {
      console.log('🔍 Analyzing uploaded room image...');

      // For now, we'll use a simple heuristic approach
      // In the future, this could use an image analysis API

      // Extract basic room characteristics from the image
      // This is a simplified approach - in production, you'd use computer vision
      const roomFeatures = [
        'interior room space',
        'existing furniture and layout',
        'current lighting conditions',
        'architectural elements',
        'color scheme and materials',
        'spatial proportions and dimensions'
      ];

      const description = `A ${roomFeatures.join(', ')} with the current design elements and styling`;

      console.log('📋 Room analysis:', description);
      return description;

    } catch (error) {
      console.error('❌ Error analyzing room image:', error);
      return 'an interior room space';
    }
  }

  /**
   * Create enhanced prompt for image-to-image generation
   */
  private createImageToImagePrompt(roomDescription: string, userPrompt: string): string {
    const enhancedPrompt = `
      Real photograph of ${roomDescription} redesigned with: ${userPrompt}.

      Maintain the basic room structure and layout while applying the requested changes.
      Real photography, actual interior design, natural lighting, authentic materials,
      professional interior photography, magazine quality room photo,
      no animation, no 3D rendering, no cartoon style,
      photographic realism, actual room transformation, real space.

      Style: realistic photography, natural, authentic, professional interior design,
      real materials, genuine room transformation.
    `.replace(/\s+/g, ' ').trim();

    return enhancedPrompt;
  }

  /**
   * Create realistic prompt optimized for photorealistic image generation
   */
  private createRealisticPrompt(userPrompt: string): string {
    const realisticPrompt = `
      Professional interior design photograph, ${userPrompt},
      photorealistic, natural lighting, high resolution photography,
      real materials, authentic textures, professional architectural photography,
      magazine quality interior design, realistic room transformation,
      natural colors, proper lighting, sharp focus, detailed textures,
      interior design portfolio quality, real space photography,
      no artificial effects, no stylization, no animation, no cartoon elements,
      genuine interior design, actual room photograph, realistic proportions,
      professional real estate photography style, natural ambiance.
    `.replace(/\s+/g, ' ').trim();

    return realisticPrompt;
  }

  /**
   * Create a detailed prompt for image generation with emphasis on photorealism
   */
  private createPrompt(roomData: RoomData, materials: MaterialOptions): string {
    // Check if this is a custom prompt (from the new AI designer)
    if (materials.flooring.id === 'custom' && materials.flooring.description) {
      return this.createRealisticPrompt(`${materials.flooring.description}, interior design`);
    }

    const roomType = roomData.title.toLowerCase();

    // Extract material descriptions for natural language
    const flooringDesc = this.getMaterialDescription(materials.flooring);
    const wallsDesc = this.getMaterialDescription(materials.walls);
    const ceilingDesc = this.getMaterialDescription(materials.ceiling);
    const lightingDesc = this.getMaterialDescription(materials.lighting);

    // Create a detailed, realistic prompt
    const basePrompt = `
      Professional interior design photograph of a ${roomType},
      featuring ${flooringDesc} flooring,
      ${wallsDesc} walls,
      ${ceilingDesc} ceiling,
      and ${lightingDesc} lighting
    `.replace(/\s+/g, ' ').trim();

    return this.createRealisticPrompt(basePrompt);
  }

  /**
   * Extract material description for prompt
   */
  private getMaterialDescription(material: { id: string; name: string; description: string }): string {
    // Convert material ID to descriptive terms
    const materialMap: Record<string, string> = {
      // Flooring
      'hardwood-oak': 'premium oak hardwood',
      'marble-white': 'white Carrara marble',
      'vinyl-luxury': 'luxury vinyl plank with wood texture',
      'carpet-premium': 'premium wool carpet',
      
      // Walls
      'paint-white': 'clean white painted',
      'wallpaper-modern': 'modern designer wallpaper',
      'wood-paneling': 'natural wood paneled',
      'brick-exposed': 'exposed brick',
      
      // Ceiling
      'gypsum-smooth': 'smooth white gypsum',
      'gypsum-textured': 'textured gypsum',
      'suspended-grid': 'suspended grid tile',
      'wood-planks': 'natural wood plank',
      
      // Lighting
      'led-recessed': 'modern LED recessed',
      'pendant-modern': 'contemporary pendant',
      'chandelier-crystal': 'elegant crystal chandelier',
      'track-lighting': 'adjustable track'
    };

    return materialMap[material.id] || material.name.toLowerCase();
  }

  /**
   * Call Hugging Face Inference API for text-to-image with realistic models
   */
  private async callStableDiffusion(prompt: string): Promise<Blob> {
    console.log('🎨 Calling Realistic Text-to-Image API...');
    console.log('📝 Prompt:', prompt.substring(0, 100) + '...');
    console.log('🔑 Using API key:', this.apiKey ? 'YES' : 'NO');

    if (!this.apiKey) {
      throw new Error('Hugging Face API key is not configured. Please add NEXT_PUBLIC_HUGGINGFACE_API_KEY to your environment variables.');
    }

    // Create realistic prompt with negative prompts
    const realisticPrompt = this.createRealisticPrompt(prompt);
    const negativePrompt = "animated, cartoon, anime, 3d render, cgi, illustration, drawing, painting, sketch, unrealistic, fake, artificial, stylized, non-photographic, low quality, blurry";

    // Try Stable Diffusion 1.5 first (most reliable on free tier, good for realistic results)
    try {
      const response = await fetch(`${this.baseUrl}/runwayml/stable-diffusion-v1-5`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: realisticPrompt,
          parameters: {
            negative_prompt: negativePrompt,
            num_images_per_prompt: 1,
            guidance_scale: 6.5,
            num_inference_steps: 20,
            width: 512,
            height: 512
          }
        })
      });

      console.log('📡 SD 1.5 Response status:', response.status);

      if (response.ok) {
        const blob = await response.blob();
        console.log('✅ SD 1.5 Success! Received blob:', blob.size, 'bytes', 'type:', blob.type);

        if (blob.type.startsWith('image/')) {
          return blob;
        } else {
          const text = await blob.text();
          console.log('⚠️ SD 1.5 returned non-image blob:', text);
          throw new Error(`SD 1.5 returned non-image response: ${text}`);
        }
      } else {
        const errorText = await response.text();
        console.log('❌ SD 1.5 Error response:', errorText);
        throw new Error(`SD 1.5 API error: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      console.log('⚠️ SD 1.5 failed, trying SD 1.4...', error);
    }

    // Fallback to Stable Diffusion 1.4 with realistic settings
    try {
      const response = await fetch(`${this.baseUrl}/CompVis/stable-diffusion-v1-4`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: realisticPrompt,
          parameters: {
            negative_prompt: negativePrompt,
            num_images_per_prompt: 1,
            guidance_scale: 6.0,
            num_inference_steps: 15,
            width: 512,
            height: 512
          }
        })
      });

      console.log('📡 SD 1.4 Response status:', response.status);

      if (response.ok) {
        const blob = await response.blob();
        console.log('✅ SD 1.4 Success! Received blob:', blob.size, 'bytes', 'type:', blob.type);

        if (blob.type.startsWith('image/')) {
          return blob;
        } else {
          const text = await blob.text();
          console.log('⚠️ SD 1.4 returned non-image blob:', text);
          throw new Error(`SD 1.4 returned non-image response: ${text}`);
        }
      } else {
        const errorText = await response.text();
        console.log('⚠️ SD 1.4 failed with status:', response.status, 'Error:', errorText);
        throw new Error(`SD 1.4 failed: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      console.log('⚠️ SD 1.4 failed, trying SDXL...', error);
    }

    // Try original Stable Diffusion 1.4 with realistic settings
    try {
      const response = await fetch(`${this.baseUrl}/CompVis/stable-diffusion-v1-4`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: realisticPrompt,
          parameters: {
            negative_prompt: negativePrompt,
            num_images_per_prompt: 1,
            guidance_scale: 6.0,
            num_inference_steps: 15,
            width: 512,
            height: 512
          }
        })
      });

      console.log('📡 SD 1.4 Response status:', response.status);

      if (response.ok) {
        const blob = await response.blob();
        console.log('✅ SD 1.4 Success! Received blob:', blob.size, 'bytes', 'type:', blob.type);

        if (blob.type.startsWith('image/')) {
          return blob;
        } else {
          const text = await blob.text();
          console.log('⚠️ SD 1.4 returned non-image blob:', text);
          throw new Error(`SD 1.4 returned non-image response: ${text}`);
        }
      } else {
        const errorText = await response.text();
        console.log('⚠️ SD 1.4 failed with status:', response.status, 'Error:', errorText);
        throw new Error(`SD 1.4 failed: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      console.log('⚠️ SD 1.4 failed, trying SDXL fallback...', error);
    }

    // Fallback to Stable Diffusion XL with realistic settings
    try {
      const response = await fetch(`${this.baseUrl}/stabilityai/stable-diffusion-xl-base-1.0`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: realisticPrompt,
          parameters: {
            negative_prompt: negativePrompt,
            num_images_per_prompt: 1,
            guidance_scale: 7.5,
            num_inference_steps: 20,
            width: 768,
            height: 512
          }
        })
      });

      console.log('📡 SDXL Response status:', response.status);

      if (response.ok) {
        const blob = await response.blob();
        console.log('✅ SDXL Success! Received blob:', blob.size, 'bytes', 'type:', blob.type);

        if (blob.type.startsWith('image/')) {
          return blob;
        } else {
          const text = await blob.text();
          console.log('⚠️ SDXL returned non-image blob:', text);
          throw new Error(`SDXL returned non-image response: ${text}`);
        }
      } else {
        const errorText = await response.text();
        console.log('⚠️ SDXL failed with status:', response.status, 'Error:', errorText);
        throw new Error(`SDXL failed: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      console.log('⚠️ SDXL failed, using demo fallback...', error);
    }

    // Final fallback: Generate a demo image using a placeholder service
    console.log('🎨 All AI models failed, generating demo placeholder...');
    return await this.generateDemoImage(prompt);
  }

  /**
   * Generate a demo image using a free placeholder service
   */
  private async generateDemoImage(prompt: string): Promise<Blob> {
    try {
      console.log('🎨 Generating demo image with placeholder service...');

      // Use a free image generation service or create a styled placeholder
      // For now, we'll create a simple colored rectangle with text
      const canvas = document.createElement('canvas');
      canvas.width = 512;
      canvas.height = 512;
      const ctx = canvas.getContext('2d');

      if (ctx) {
        // Create a gradient background
        const gradient = ctx.createLinearGradient(0, 0, 512, 512);
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(1, '#764ba2');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 512, 512);

        // Add text
        ctx.fillStyle = 'white';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('AI Room Design', 256, 200);

        ctx.font = '16px Arial';
        ctx.fillText('Demo Mode', 256, 240);
        ctx.fillText('(API Credits Needed for AI)', 256, 270);

        // Add prompt preview
        ctx.font = '14px Arial';
        const words = prompt.split(' ').slice(0, 8).join(' ');
        ctx.fillText(words + '...', 256, 320);
      }

      // Convert canvas to blob
      return new Promise((resolve) => {
        canvas.toBlob((blob) => {
          if (blob) {
            console.log('✅ Demo image generated successfully');
            resolve(blob);
          } else {
            // Fallback to a simple fetch from a placeholder service
            fetch('https://picsum.photos/512/512')
              .then(response => response.blob())
              .then(resolve)
              .catch(() => {
                // Ultimate fallback - create a simple blob
                const simpleBlob = new Blob(['Demo image'], { type: 'text/plain' });
                resolve(simpleBlob);
              });
          }
        }, 'image/png');
      });

    } catch (error) {
      console.error('❌ Error generating demo image:', error);
      // Ultimate fallback - fetch from a free service
      const response = await fetch('https://picsum.photos/512/512');
      return await response.blob();
    }
  }

  /**
   * Convert blob to base64 data URL
   */
  private async blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Convert base64 data URL to blob
   */
  private async base64ToBlob(base64: string): Promise<Blob> {
    const response = await fetch(base64);
    return await response.blob();
  }

  /**
   * Call Hugging Face Image-to-Image API with realistic models
   */
  private async callImageToImageAPI(imageBlob: Blob, prompt: string): Promise<Blob> {
    console.log('🖼️ Calling Realistic Image-to-Image API...');
    console.log('📝 Prompt:', prompt);
    console.log('🖼️ Image size:', imageBlob.size, 'bytes');

    if (!this.apiKey) {
      throw new Error('Hugging Face API key is not configured.');
    }

    // Create enhanced realistic prompt with negative prompts
    const realisticPrompt = this.createRealisticPrompt(prompt);
    const negativePrompt = "animated, cartoon, anime, 3d render, cgi, illustration, drawing, painting, sketch, unrealistic, fake, artificial, stylized, non-photographic";

    // Try Stable Diffusion 1.5 for image-to-image (most reliable on free tier)
    try {
      const formData = new FormData();
      formData.append('inputs', imageBlob);
      formData.append('parameters', JSON.stringify({
        prompt: realisticPrompt,
        negative_prompt: negativePrompt,
        num_inference_steps: 20,
        guidance_scale: 6.5,
        strength: 0.65, // Lower strength to preserve more of original image
        width: 512,
        height: 512
      }));

      const response = await fetch(`${this.baseUrl}/runwayml/stable-diffusion-v1-5`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: formData
      });

      console.log('📡 SD 1.5 Image-to-Image Response status:', response.status);

      if (response.ok) {
        const blob = await response.blob();
        console.log('✅ SD 1.5 Image-to-Image Success! Received blob:', blob.size, 'bytes');

        if (blob.type.startsWith('image/')) {
          return blob;
        }
      }
    } catch (error) {
      console.log('⚠️ SD 1.5 Image-to-Image failed, trying fallback...', error);
    }

    // Fallback to Stable Diffusion 1.4 with realistic settings
    try {
      const formData = new FormData();
      formData.append('inputs', imageBlob);
      formData.append('parameters', JSON.stringify({
        prompt: realisticPrompt,
        negative_prompt: negativePrompt,
        num_inference_steps: 15,
        guidance_scale: 6.0,
        strength: 0.7,
        width: 512,
        height: 512
      }));

      const response = await fetch(`${this.baseUrl}/CompVis/stable-diffusion-v1-4`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: formData
      });

      console.log('📡 SD 1.4 Image-to-Image Response status:', response.status);

      if (response.ok) {
        const blob = await response.blob();
        console.log('✅ SD 1.4 Image-to-Image Success! Received blob:', blob.size, 'bytes');

        if (blob.type.startsWith('image/')) {
          return blob;
        }
      }
    } catch (error) {
      console.log('⚠️ SD 1.4 Image-to-Image failed, trying InstuctPix2Pix...', error);
    }

    // Try InstuctPix2Pix with realistic settings as third option
    try {
      const formData = new FormData();
      formData.append('inputs', imageBlob);
      formData.append('parameters', JSON.stringify({
        prompt: `Transform this room: ${realisticPrompt}. Keep it photorealistic and natural.`,
        num_inference_steps: 15,
        guidance_scale: 6.0,
        strength: 0.6 // Even lower strength for more conservative changes
      }));

      const response = await fetch(`${this.baseUrl}/timbrooks/instruct-pix2pix`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: formData
      });

      console.log('📡 InstuctPix2Pix Response status:', response.status);

      if (response.ok) {
        const blob = await response.blob();
        console.log('✅ InstuctPix2Pix Success! Received blob:', blob.size, 'bytes');

        if (blob.type.startsWith('image/')) {
          return blob;
        }
      }
    } catch (error) {
      console.log('⚠️ InstuctPix2Pix failed, using text-to-image fallback...', error);
    }

    // Final fallback: use text-to-image with enhanced realistic prompt
    const enhancedPrompt = `Professional interior design photograph of a room similar to uploaded image, ${realisticPrompt}, photorealistic, natural lighting, high resolution photography`;
    return await this.callStableDiffusion(enhancedPrompt);
  }

  /**
   * Alternative: Generate using SDXL (higher quality) with realistic settings
   */
  async generateWithSDXL(roomData: RoomData, materials: MaterialOptions): Promise<string> {
    try {
      const prompt = this.createPrompt(roomData, materials);
      const negativePrompt = "animated, cartoon, anime, 3d render, cgi, illustration, drawing, painting, sketch, unrealistic, fake, artificial, stylized, non-photographic, low quality, blurry";

      const response = await fetch(`${this.baseUrl}/stabilityai/stable-diffusion-xl-base-1.0`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: prompt,
          parameters: {
            negative_prompt: negativePrompt,
            guidance_scale: 7.0,
            num_inference_steps: 30,
            width: 1024,
            height: 768
          }
        })
      });

      if (!response.ok) {
        throw new Error(`SDXL API error: ${response.status}`);
      }

      const blob = await response.blob();
      return await this.blobToBase64(blob);
    } catch (error) {
      console.error('Error with SDXL generation:', error);
      // Fallback to regular Stable Diffusion
      return this.generateCustomizedRoom(roomData, materials);
    }
  }

  /**
   * Check if API key is configured
   */
  isConfigured(): boolean {
    return !!this.apiKey;
  }

  /**
   * Generate a fallback image URL (for demo purposes)
   */
  generateFallbackImage(roomData: RoomData, materials: MaterialOptions): string {
    // Create a unique hash based on selections for consistent fallback
    const hash = btoa(`${roomData.title}-${materials.flooring.id}-${materials.walls.id}-${materials.ceiling.id}-${materials.lighting.id}`);
    
    // Use a different filter/effect on the original image to simulate customization
    const filters = [
      '&sat=1.2&con=1.1&bright=1.1',
      '&sat=0.8&con=1.2&bright=0.9',
      '&sat=1.1&con=0.9&bright=1.2',
      '&sat=0.9&con=1.3&bright=1.0'
    ];
    
    const filterIndex = hash.length % filters.length;
    return `${roomData.originalImage}${filters[filterIndex]}`;
  }
}

// Export singleton instance
export const imageGenerationService = new ImageGenerationService();
