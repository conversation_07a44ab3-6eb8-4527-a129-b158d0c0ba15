.hero {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

.overlay {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
  padding: 40px 20px;
  background: rgba(0, 0, 0, 0.4); /* Optional dark tint */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.cards {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 30px;
}

.card {
  background-color: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  padding: 20px;
  border-radius: 16px;
  max-width: 260px;
  text-align: center;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease;
  color: #fff;
}

.card:hover {
  transform: translateY(-5px);
}

.card h3 {
  margin-bottom: 10px;
  font-size: 20px;
  color: #ffffff;
}

.card p {
  font-size: 14px;
  color: #f0f0f0;
}

.ctaCard {
  background-color: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40px;
  max-width: 460px;
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.25);
  font-size: 30px;
  font-weight: 500;
  font-family:  sans-serif;
  white-space: normal; 
  color: #ffffff; /* White text */


}

.cta {
  background-color: #004d1a;
  color: white;
  border: none;
  padding: 18px 40px;
  font-size: 36px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 900;
  transition: background 0.3s ease;
  width: 100%;
}

.cta:hover {
  background-color: #006622;
}
@media (max-width: 768px) {
  .cards {
    flex-direction: column;
    align-items: center;
  }

  .overlay {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .card {
    width: 100%;
    max-width: 240px;
  }

  .ctaCard {
    margin-top: 0;
    padding: 16px 30px;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.25);
    font-size: 20px;
    font-weight: 600;
    font-family: 'Inter', sans-serif;
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    
    width: auto;
    max-width: 90%;
    text-align: center;
  }

  .cta {
    font-size: 16px;
    padding: 14px 24px;
    width: auto;
    max-width: 100%;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}


