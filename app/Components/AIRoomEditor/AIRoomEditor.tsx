'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import styles from './AIRoomEditor.module.css';

interface SegmentedRegion {
  id: string;
  name: string;
  mask: ImageData | null;
  isSelected: boolean;
  color: string;
  originalColor: string;
}

interface MaterialOption {
  id: string;
  name: string;
  color: string;
  texture: string;
}

interface RoomSettings {
  brightness: number;
  contrast: number;
  saturation: number;
}

// Material options similar to PhotorealisticBedroom
const floorMaterials: MaterialOption[] = [
  { id: 'oak-hardwood', name: 'Oak Hardwood', color: '#D2B48C', texture: 'wood' },
  { id: 'premium-carpet', name: 'Premium Carpet', color: '#8B4513', texture: 'carpet' },
  { id: 'white-marble', name: 'White Marble', color: '#F8F8FF', texture: 'marble' },
  { id: 'luxury-vinyl', name: 'Luxury Vinyl', color: '#CD853F', texture: 'vinyl' },
  { id: 'dark-walnut', name: 'Dark Walnut', color: '#654321', texture: 'wood' },
  { id: 'light-bamboo', name: 'Light Bamboo', color: '#F5DEB3', texture: 'bamboo' }
];

const wallMaterials: MaterialOption[] = [
  { id: 'white-paint', name: 'White Paint', color: '#F8F8FF', texture: 'paint' },
  { id: 'wood-paneling', name: 'Wood Paneling', color: '#DEB887', texture: 'wood' },
  { id: 'modern-wallpaper', name: 'Modern Wallpaper', color: '#E6E6FA', texture: 'wallpaper' },
  { id: 'exposed-brick', name: 'Exposed Brick', color: '#CD853F', texture: 'brick' },
  { id: 'soft-beige', name: 'Soft Beige', color: '#F5F5DC', texture: 'paint' },
  { id: 'accent-blue', name: 'Accent Blue', color: '#B0C4DE', texture: 'paint' }
];

const ceilingMaterials: MaterialOption[] = [
  { id: 'smooth-gypsum', name: 'Smooth Gypsum', color: '#FFFFFF', texture: 'smooth' },
  { id: 'wood-planks', name: 'Wood Planks', color: '#DEB887', texture: 'wood' },
  { id: 'textured-gypsum', name: 'Textured Gypsum', color: '#F8F8FF', texture: 'textured' },
  { id: 'suspended-grid', name: 'Suspended Grid', color: '#F0F0F0', texture: 'grid' }
];

export default function AIRoomEditor() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const overlayCanvasRef = useRef<HTMLCanvasElement>(null);
  const [originalImage, setOriginalImage] = useState<HTMLImageElement | null>(null);
  const [segments, setSegments] = useState<SegmentedRegion[]>([]);
  const [selectedSegment, setSelectedSegment] = useState<string | null>(null);
  const [isSegmenting, setIsSegmenting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const [selectedMaterials, setSelectedMaterials] = useState({
    floor: floorMaterials[0],
    walls: wallMaterials[0],
    ceiling: ceilingMaterials[0]
  });
  const [debugInfo, setDebugInfo] = useState<string>('');

  // Load the executive bedroom image properly
  useEffect(() => {
    console.log('🖼️ Loading executive bedroom image...');
    setIsLoading(true);

    const loadImage = () => {
      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        console.log('✅ Executive bedroom image loaded successfully');
        setOriginalImage(img);
        setIsLoading(false);

        // Draw image to canvas
        drawImageToCanvas(img);

        // Start AI segmentation
        setTimeout(() => {
          performAISegmentation();
        }, 500);
      };

      img.onerror = (error) => {
        console.error('❌ Failed to load executive bedroom image:', error);
        console.log('🔄 Creating fallback bedroom scene...');
        setTimeout(() => createFallbackScene(), 100);
      };

      // Try multiple image paths to ensure loading
      const imagePaths = [
        '/images/rooms/executive-bedroom.jpg',
        './images/rooms/executive-bedroom.jpg',
        'images/rooms/executive-bedroom.jpg'
      ];

      let currentPathIndex = 0;

      const tryLoadImage = () => {
        if (currentPathIndex < imagePaths.length) {
          const path = imagePaths[currentPathIndex];
          console.log(`📂 Trying to load image from: ${path}`);
          img.src = path;
          currentPathIndex++;
        } else {
          console.log('❌ All image paths failed, using fallback');
          setTimeout(() => createFallbackScene(), 100);
        }
      };

      // Override error handler to try next path
      const originalErrorHandler = img.onerror;
      img.onerror = () => {
        console.log(`❌ Failed to load from: ${imagePaths[currentPathIndex - 1]}`);
        if (currentPathIndex < imagePaths.length) {
          tryLoadImage();
        } else {
          // Call original error handler if all paths fail
          if (originalErrorHandler) originalErrorHandler.call(img, new Event('error'));
        }
      };

      // Start loading
      tryLoadImage();
    };

    const createFallbackScene = () => {
      const canvas = canvasRef.current;
      const overlayCanvas = overlayCanvasRef.current;

      if (!canvas || !overlayCanvas) {
        setTimeout(createFallbackScene, 100);
        return;
      }

      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // Set large canvas size
      canvas.width = 1200;
      canvas.height = 800;
      overlayCanvas.width = 1200;
      overlayCanvas.height = 800;

      // Draw realistic bedroom
      // Ceiling
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, 1200, 200);

      // Walls
      ctx.fillStyle = '#F5F5DC';
      ctx.fillRect(0, 200, 1200, 400);

      // Floor
      ctx.fillStyle = '#8B4513';
      ctx.fillRect(0, 600, 1200, 200);

      // Add furniture
      ctx.fillStyle = '#654321';
      ctx.fillRect(400, 500, 400, 150);

      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(420, 480, 360, 40);

      console.log('✅ Fallback bedroom scene created');
      setIsLoading(false);

      setTimeout(() => {
        performAISegmentation();
      }, 500);
    };

    // Wait for canvas to be ready
    if (canvasRef.current) {
      loadImage();
    } else {
      setTimeout(loadImage, 100);
    }
  }, []);





  // Update overlay whenever segments change
  useEffect(() => {
    if (segments.length > 0) {
      updateOverlay();
    }
  }, [segments]);

  const drawImageToCanvas = (img: HTMLImageElement) => {
    const canvas = canvasRef.current;
    const overlayCanvas = overlayCanvasRef.current;

    if (!canvas || !overlayCanvas) {
      console.error('❌ Canvas refs not available');
      return;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('❌ Cannot get canvas context');
      return;
    }

    // Make canvas large for better quality
    const maxWidth = 1200;
    const maxHeight = 800;
    const aspectRatio = img.naturalWidth / img.naturalHeight;

    let width = maxWidth;
    let height = width / aspectRatio;

    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }

    // Ensure minimum size
    width = Math.max(width, 800);
    height = Math.max(height, 600);

    // Set canvas dimensions
    canvas.width = width;
    canvas.height = height;
    overlayCanvas.width = width;
    overlayCanvas.height = height;

    // Set CSS size for proper display
    canvas.style.width = width + 'px';
    canvas.style.height = height + 'px';
    overlayCanvas.style.width = width + 'px';
    overlayCanvas.style.height = height + 'px';

    console.log(`🖼️ Canvas size: ${width} x ${height} (original: ${img.naturalWidth} x ${img.naturalHeight})`);

    // Draw image with high quality
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    ctx.clearRect(0, 0, width, height);
    ctx.drawImage(img, 0, 0, width, height);

    console.log('✅ Executive bedroom image drawn to canvas');
  };

  // AI-powered room segmentation using Hugging Face free tier models
  const performAISegmentation = async () => {
    console.log('🤖 Starting AI segmentation with Hugging Face...');
    setIsSegmenting(true);

    try {
      const canvas = canvasRef.current;
      if (!canvas) {
        console.error('❌ Canvas not available');
        setIsSegmenting(false);
        return;
      }

      console.log('📸 Converting canvas to image data...');

      // Convert canvas to blob for API
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            throw new Error('Failed to convert canvas to blob');
          }
        }, 'image/jpeg', 0.9);
      });

      console.log('📤 Sending image to Hugging Face API...', {
        blobSize: blob.size,
        canvasSize: `${canvas.width}x${canvas.height}`
      });

      // Convert blob to base64
      const imageData = await blobToBase64(blob);

      // Make API call to our Hugging Face segmentation endpoint
      const response = await fetch('/api/huggingface-segment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageData: imageData,
          width: canvas.width,
          height: canvas.height
        })
      });

      console.log('📡 API response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error:', response.status, errorText);
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ AI segmentation result:', result);

      if (result.success && result.segments) {
        console.log('🔄 Processing segments from API...');

        // Convert API response to our segment format
        const detectedSegments: SegmentedRegion[] = await Promise.all(
          result.segments.map(async (seg: any) => {
            console.log(`Processing segment: ${seg.name}`);
            return {
              id: seg.id,
              name: seg.name,
              mask: await base64ToImageData(seg.mask, canvas.width, canvas.height),
              isSelected: false,
              color: selectedMaterials[seg.id as keyof typeof selectedMaterials]?.color || '#FF0000',
              originalColor: selectedMaterials[seg.id as keyof typeof selectedMaterials]?.color || '#FF0000'
            };
          })
        );

        setSegments(detectedSegments);
        console.log('🎉 AI segmentation complete with', detectedSegments.length, 'segments!');
      } else {
        throw new Error('No segments returned from API');
      }

    } catch (error) {
      console.error('❌ AI segmentation failed:', error);
      // Fallback to simple segmentation
      console.log('🔄 Falling back to simple segmentation...');
      performSimpleSegmentation();
    } finally {
      setIsSegmenting(false);
    }
  };

  // Fallback simple segmentation
  const performSimpleSegmentation = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

    const detectedSegments: SegmentedRegion[] = [
      {
        id: 'floor',
        name: 'Floor',
        mask: createFloorMask(imageData),
        isSelected: false,
        color: selectedMaterials.floor.color,
        originalColor: selectedMaterials.floor.color
      },
      {
        id: 'walls',
        name: 'Walls',
        mask: createWallsMask(imageData),
        isSelected: false,
        color: selectedMaterials.walls.color,
        originalColor: selectedMaterials.walls.color
      },
      {
        id: 'ceiling',
        name: 'Ceiling',
        mask: createCeilingMask(imageData),
        isSelected: false,
        color: selectedMaterials.ceiling.color,
        originalColor: selectedMaterials.ceiling.color
      }
    ];

    setSegments(detectedSegments);
  };

  // Helper functions for API communication
  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = (reader.result as string).split(',')[1];
        resolve(base64);
      };
      reader.readAsDataURL(blob);
    });
  };

  const base64ToImageData = async (base64: string, width: number, height: number): Promise<ImageData> => {
    try {
      // Try to parse as JSON first (our new format)
      const maskInfo = JSON.parse(atob(base64));
      if (maskInfo.type && maskInfo.region) {
        console.log('📊 Creating mask from region data:', maskInfo);
        return createMaskFromRegion(maskInfo, width, height);
      }
    } catch (e) {
      // If not JSON, try as image
      console.log('📊 Parsing as image data...');
    }

    // Fallback to image parsing
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d')!;

      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, 0, 0);
        const imageData = ctx.getImageData(0, 0, width, height);
        resolve(imageData);
      };
      img.onerror = () => {
        // If image fails, create a basic mask
        console.log('📊 Creating fallback mask...');
        const imageData = ctx.createImageData(width, height);
        resolve(imageData);
      };
      img.src = `data:image/png;base64,${base64}`;
    });
  };

  const createMaskFromRegion = (maskInfo: any, width: number, height: number): ImageData => {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d')!;

    const imageData = ctx.createImageData(width, height);
    const data = imageData.data;

    const startY = Math.floor(height * maskInfo.region.startY);
    const endY = Math.floor(height * maskInfo.region.endY);

    console.log(`📊 Creating ${maskInfo.type} mask: Y ${startY} to ${endY}`);

    for (let y = startY; y < endY; y++) {
      for (let x = 0; x < width; x++) {
        const i = (y * width + x) * 4;
        data[i] = 255;     // R
        data[i + 1] = 255; // G
        data[i + 2] = 255; // B
        data[i + 3] = 200; // A
      }
    }

    return imageData;
  };



  // Simple floor detection - bottom 25% of image
  const createFloorMask = (imageData: ImageData): ImageData => {
    const mask = new ImageData(imageData.width, imageData.height);
    const maskData = mask.data;
    let matchingPixels = 0;

    console.log('Creating floor mask...');

    for (let y = 0; y < imageData.height; y++) {
      for (let x = 0; x < imageData.width; x++) {
        const i = (y * imageData.width + x) * 4;
        const heightRatio = y / imageData.height;

        // Floor is bottom 25% of image
        if (heightRatio > 0.75) {
          maskData[i] = 255;     // R
          maskData[i + 1] = 255; // G
          maskData[i + 2] = 255; // B
          maskData[i + 3] = 200; // A
          matchingPixels++;
        }
      }
    }

    console.log(`Floor mask: ${matchingPixels} pixels`);
    return mask;
  };

  // Simple walls detection - middle 50% of image
  const createWallsMask = (imageData: ImageData): ImageData => {
    const mask = new ImageData(imageData.width, imageData.height);
    const maskData = mask.data;
    let matchingPixels = 0;

    console.log('Creating walls mask...');

    for (let y = 0; y < imageData.height; y++) {
      for (let x = 0; x < imageData.width; x++) {
        const i = (y * imageData.width + x) * 4;
        const heightRatio = y / imageData.height;

        // Walls are middle 50% of image
        if (heightRatio > 0.25 && heightRatio <= 0.75) {
          maskData[i] = 255;     // R
          maskData[i + 1] = 255; // G
          maskData[i + 2] = 255; // B
          maskData[i + 3] = 150; // A
          matchingPixels++;
        }
      }
    }

    console.log(`Walls mask: ${matchingPixels} pixels`);
    return mask;
  };

  // Simple ceiling detection - top 25% of image
  const createCeilingMask = (imageData: ImageData): ImageData => {
    const mask = new ImageData(imageData.width, imageData.height);
    const maskData = mask.data;
    let matchingPixels = 0;

    console.log('Creating ceiling mask...');

    for (let y = 0; y < imageData.height; y++) {
      for (let x = 0; x < imageData.width; x++) {
        const i = (y * imageData.width + x) * 4;
        const heightRatio = y / imageData.height;

        // Ceiling is top 25% of image
        if (heightRatio <= 0.25) {
          maskData[i] = 255;     // R
          maskData[i + 1] = 255; // G
          maskData[i + 2] = 255; // B
          maskData[i + 3] = 120; // A
          matchingPixels++;
        }
      }
    }

    console.log(`Ceiling mask: ${matchingPixels} pixels`);
    return mask;
  };

  const handleSegmentClick = (segmentId: string) => {
    setSelectedSegment(selectedSegment === segmentId ? null : segmentId);
    // Update the overlay to show all current segment colors
    updateOverlay();
  };

  const handleMaterialChange = (segmentId: string, material: MaterialOption) => {
    setSelectedMaterials(prev => ({
      ...prev,
      [segmentId]: material
    }));
    applyColorToSegment(segmentId, material.color);
  };



  // Update overlay to show all segment modifications on the whole image
  const updateOverlay = () => {
    const overlayCanvas = overlayCanvasRef.current;
    if (!overlayCanvas) return;

    const overlayCtx = overlayCanvas.getContext('2d');
    if (!overlayCtx) return;

    // Clear the overlay
    overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);

    console.log('Updating overlay with', segments.length, 'segments');

    // Apply each segment's color overlay
    segments.forEach((segment) => {
      if (!segment.mask) {
        console.log(`Segment ${segment.name} has no mask`);
        return;
      }

      console.log(`Applying ${segment.name} with color ${segment.color}`);

      // Create a temporary canvas for this segment
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = overlayCanvas.width;
      tempCanvas.height = overlayCanvas.height;
      const tempCtx = tempCanvas.getContext('2d');
      if (!tempCtx) return;

      // Draw the mask
      tempCtx.putImageData(segment.mask, 0, 0);

      // Apply color with different opacity based on segment type
      tempCtx.globalCompositeOperation = 'source-atop';
      let opacity = '60'; // Increased default opacity for better visibility
      if (segment.id === 'floor') opacity = '70'; // Floor more visible
      if (segment.id === 'walls') opacity = '55'; // Walls more visible
      if (segment.id === 'ceiling') opacity = '50'; // Ceiling more visible

      tempCtx.fillStyle = segment.color + opacity;
      tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

      // Blend onto main overlay canvas with source-over blending for better visibility
      overlayCtx.globalCompositeOperation = 'source-over';
      overlayCtx.globalAlpha = 0.8; // Add some transparency to the overall overlay
      overlayCtx.drawImage(tempCanvas, 0, 0);
      overlayCtx.globalAlpha = 1.0; // Reset alpha
    });

    console.log('Overlay update completed');
  };

  const applyColorToSegment = (segmentId: string, color: string) => {
    // Update segment color
    setSegments(prev => prev.map(s =>
      s.id === segmentId ? { ...s, color } : s
    ));

    // Update the overlay to show the change
    setTimeout(() => updateOverlay(), 10); // Small delay to ensure state update
  };

  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading Executive Bedroom...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h1>🤖 AI Room Editor - Intelligent Segmentation</h1>
        <p>Advanced computer vision algorithms for precise room element detection</p>
        {segments.length > 0 && !isSegmenting && (
          <div className={styles.statusMessage}>
            ✅ Segmentation complete! Select materials below to edit the room.
            <button
              onClick={() => performAISegmentation()}
              style={{ marginLeft: '1rem', padding: '0.5rem 1rem', background: '#FFD700', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
            >
              🔄 Re-segment
            </button>
          </div>
        )}
      </div>

      {/* Controls */}
      {showControls && !isLoading && (
        <div className={styles.controlsPanel}>
          {/* Material Selection */}
          <div className={styles.section}>
            <h3>🏗️ Materials</h3>

            <div className={styles.materialGroup}>
              <label>Floor Material:</label>
              <select
                value={selectedMaterials.floor.id}
                onChange={(e) => {
                  const material = floorMaterials.find(m => m.id === e.target.value);
                  if (material) handleMaterialChange('floor', material);
                }}
              >
                {floorMaterials.map(material => (
                  <option key={material.id} value={material.id}>{material.name}</option>
                ))}
              </select>
            </div>

            <div className={styles.materialGroup}>
              <label>Wall Material:</label>
              <select
                value={selectedMaterials.walls.id}
                onChange={(e) => {
                  const material = wallMaterials.find(m => m.id === e.target.value);
                  if (material) handleMaterialChange('walls', material);
                }}
              >
                {wallMaterials.map(material => (
                  <option key={material.id} value={material.id}>{material.name}</option>
                ))}
              </select>
            </div>

            <div className={styles.materialGroup}>
              <label>Ceiling Material:</label>
              <select
                value={selectedMaterials.ceiling.id}
                onChange={(e) => {
                  const material = ceilingMaterials.find(m => m.id === e.target.value);
                  if (material) handleMaterialChange('ceiling', material);
                }}
              >
                {ceilingMaterials.map(material => (
                  <option key={material.id} value={material.id}>{material.name}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Segment Controls */}
          <div className={styles.section}>
            <h3>🎯 AI Detected Segments</h3>
            {segments.map(segment => (
              <div key={segment.id} className={styles.segmentControl}>
                <button
                  className={`${styles.segmentButton} ${selectedSegment === segment.id ? styles.selected : ''}`}
                  onClick={() => handleSegmentClick(segment.id)}
                >
                  <div
                    className={styles.colorPreview}
                    style={{ backgroundColor: segment.color }}
                  ></div>
                  {segment.name}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      <button
        className={styles.toggleControls}
        onClick={() => setShowControls(!showControls)}
      >
        {showControls ? 'Hide Controls' : 'Show Controls'}
      </button>

      {/* Main Canvas Area */}
      <div className={styles.mainContent}>
        <div className={styles.canvasContainer}>
          <canvas
            ref={canvasRef}
            className={styles.canvas}
            width={800}
            height={600}
            style={{
              minWidth: '600px',
              minHeight: '400px',
              background: '#f0f0f0',
              border: '2px solid #ccc'
            }}
          />
          <canvas
            ref={overlayCanvasRef}
            className={styles.overlayCanvas}
            width={800}
            height={600}
          />

          {isSegmenting && (
            <div className={styles.segmentingOverlay}>
              <div className={styles.spinner}></div>
              <p>🤖 AI Segmenting Room...</p>
            </div>
          )}

          {isLoading && (
            <div className={styles.segmentingOverlay}>
              <div className={styles.spinner}></div>
              <p>Loading Room Image...</p>
              <p style={{ fontSize: '0.8rem', opacity: 0.7 }}>
                Canvas: {canvasRef.current?.width || 'N/A'} x {canvasRef.current?.height || 'N/A'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
