'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import styles from './AIRoomEditor.module.css';

interface SegmentedRegion {
  id: string;
  name: string;
  mask: ImageData | null;
  isSelected: boolean;
  color: string;
  originalColor: string;
}

interface MaterialOption {
  id: string;
  name: string;
  color: string;
  texture: string;
}

interface RoomSettings {
  brightness: number;
  contrast: number;
  saturation: number;
}

// Material options similar to PhotorealisticBedroom
const floorMaterials: MaterialOption[] = [
  { id: 'oak-hardwood', name: 'Oak Hardwood', color: '#D2B48C', texture: 'wood' },
  { id: 'premium-carpet', name: 'Premium Carpet', color: '#8B4513', texture: 'carpet' },
  { id: 'white-marble', name: 'White Marble', color: '#F8F8FF', texture: 'marble' },
  { id: 'luxury-vinyl', name: 'Luxury Vinyl', color: '#CD853F', texture: 'vinyl' },
  { id: 'dark-walnut', name: 'Dark Walnut', color: '#654321', texture: 'wood' },
  { id: 'light-bamboo', name: 'Light Bamboo', color: '#F5DEB3', texture: 'bamboo' }
];

const wallMaterials: MaterialOption[] = [
  { id: 'white-paint', name: 'White Paint', color: '#F8F8FF', texture: 'paint' },
  { id: 'wood-paneling', name: 'Wood Paneling', color: '#DEB887', texture: 'wood' },
  { id: 'modern-wallpaper', name: 'Modern Wallpaper', color: '#E6E6FA', texture: 'wallpaper' },
  { id: 'exposed-brick', name: 'Exposed Brick', color: '#CD853F', texture: 'brick' },
  { id: 'soft-beige', name: 'Soft Beige', color: '#F5F5DC', texture: 'paint' },
  { id: 'accent-blue', name: 'Accent Blue', color: '#B0C4DE', texture: 'paint' }
];

const ceilingMaterials: MaterialOption[] = [
  { id: 'smooth-gypsum', name: 'Smooth Gypsum', color: '#FFFFFF', texture: 'smooth' },
  { id: 'wood-planks', name: 'Wood Planks', color: '#DEB887', texture: 'wood' },
  { id: 'textured-gypsum', name: 'Textured Gypsum', color: '#F8F8FF', texture: 'textured' },
  { id: 'suspended-grid', name: 'Suspended Grid', color: '#F0F0F0', texture: 'grid' }
];

export default function AIRoomEditor() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const overlayCanvasRef = useRef<HTMLCanvasElement>(null);
  const [originalImage, setOriginalImage] = useState<HTMLImageElement | null>(null);
  const [segments, setSegments] = useState<SegmentedRegion[]>([]);
  const [selectedSegment, setSelectedSegment] = useState<string | null>(null);
  const [isSegmenting, setIsSegmenting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const [selectedMaterials, setSelectedMaterials] = useState({
    floor: floorMaterials[0],
    walls: wallMaterials[0],
    ceiling: ceilingMaterials[0]
  });

  // Load the executive bedroom image
  useEffect(() => {
    console.log('🖼️ Starting image load process...');
    setIsLoading(true);
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => {
      console.log('✅ Image loaded successfully:', img.width, 'x', img.height);
      setOriginalImage(img);
      setIsLoading(false);

      console.log('🎨 Drawing image to canvas...');
      // Draw image to canvas first
      drawImageToCanvas(img);

      console.log('⏰ Waiting 100ms before starting segmentation...');
      // Wait a bit for canvas to be ready, then segment
      setTimeout(() => {
        console.log('🚀 Starting segmentation process...');
        performAISegmentation(img);
      }, 100);
    };
    img.onerror = (error) => {
      console.error('❌ Failed to load executive bedroom image:', error);
      setIsLoading(false);
    };
    img.src = '/images/rooms/executive-bedroom.jpg';
    console.log('📂 Image src set to:', img.src);
  }, []);

  // Update overlay whenever segments change
  useEffect(() => {
    if (segments.length > 0) {
      updateOverlay();
    }
  }, [segments]);

  const drawImageToCanvas = (img: HTMLImageElement) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match image aspect ratio
    const maxWidth = 800;
    const maxHeight = 600;
    const aspectRatio = img.width / img.height;

    let canvasWidth = maxWidth;
    let canvasHeight = maxWidth / aspectRatio;

    if (canvasHeight > maxHeight) {
      canvasHeight = maxHeight;
      canvasWidth = maxHeight * aspectRatio;
    }

    canvas.width = canvasWidth;
    canvas.height = canvasHeight;

    // Draw the image
    ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);

    // Setup overlay canvas
    const overlayCanvas = overlayCanvasRef.current;
    if (overlayCanvas) {
      overlayCanvas.width = canvasWidth;
      overlayCanvas.height = canvasHeight;
    }
  };

  // AI-powered room segmentation using computer vision techniques
  const performAISegmentation = async (img: HTMLImageElement) => {
    console.log('🚀 Starting AI room segmentation...');
    setIsSegmenting(true);

    try {
      // Wait for canvas to be ready
      await new Promise(resolve => setTimeout(resolve, 50));

      const canvas = canvasRef.current;
      console.log('📊 Canvas ref:', canvas);

      if (!canvas) {
        console.error('❌ Canvas not available in performAISegmentation');
        setIsSegmenting(false);
        return;
      }

      console.log('📐 Canvas dimensions:', canvas.width, 'x', canvas.height);

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.error('❌ Canvas context not available');
        setIsSegmenting(false);
        return;
      }

      console.log('🎨 Canvas context obtained successfully');

      // Get image data for analysis
      console.log('📸 Getting image data...');
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      console.log('✅ Image data obtained:', imageData.width, 'x', imageData.height, 'pixels');

      console.log('🧠 Creating intelligent segments...');

      // Create intelligent room segments using computer vision
      const detectedSegments: SegmentedRegion[] = [
        {
          id: 'floor',
          name: 'Floor',
          mask: createIntelligentFloorMask(imageData),
          isSelected: false,
          color: selectedMaterials.floor.color,
          originalColor: selectedMaterials.floor.color
        },
        {
          id: 'walls',
          name: 'Walls',
          mask: createIntelligentWallsMask(imageData),
          isSelected: false,
          color: selectedMaterials.walls.color,
          originalColor: selectedMaterials.walls.color
        },
        {
          id: 'ceiling',
          name: 'Ceiling',
          mask: createIntelligentCeilingMask(imageData),
          isSelected: false,
          color: selectedMaterials.ceiling.color,
          originalColor: selectedMaterials.ceiling.color
        }
      ];

      console.log('🎯 Setting segments:', detectedSegments);
      setSegments(detectedSegments);
      console.log('✅ AI segmentation completed successfully with', detectedSegments.length, 'segments');

      // Show a brief success message
      setTimeout(() => {
        console.log('🎉 Segmentation ready - user can now edit materials');
      }, 500);

    } catch (error) {
      console.error('💥 AI segmentation failed with error:', error);
      console.error('📋 Error name:', error.name);
      console.error('📝 Error message:', error.message);
      console.error('📚 Error stack:', error.stack);

      // Don't throw, just log and continue
      console.log('🔄 Continuing despite error...');
    } finally {
      console.log('🏁 Setting isSegmenting to false');
      setIsSegmenting(false);
    }
  };



  // Intelligent floor segmentation using computer vision
  const createIntelligentFloorMask = (imageData: ImageData): ImageData => {
    const mask = new ImageData(imageData.width, imageData.height);
    const data = imageData.data;
    const maskData = mask.data;
    let matchingPixels = 0;

    console.log('Creating intelligent floor mask...');

    for (let i = 0; i < data.length; i += 4) {
      const pixelIndex = i / 4;
      const x = pixelIndex % imageData.width;
      const y = Math.floor(pixelIndex / imageData.width);
      const heightRatio = y / imageData.height;
      const widthRatio = x / imageData.width;

      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const brightness = (r + g + b) / 3;

      // Advanced floor detection algorithm
      let isFloor = false;

      // Primary condition: bottom 40% of image
      if (heightRatio > 0.6) {
        isFloor = true;
      }
      // Secondary condition: perspective-aware floor detection
      else if (heightRatio > 0.45) {
        // Check for floor-like colors and textures
        const isWoodTone = (r > g && r > b && r > 80 && r < 200);
        const isCarpetTone = (Math.abs(r - g) < 30 && Math.abs(g - b) < 30 && brightness < 150);
        const isTileTone = (brightness > 120 && brightness < 220 && Math.abs(r - g) < 40);

        if (isWoodTone || isCarpetTone || isTileTone) {
          isFloor = true;
        }
      }

      if (isFloor) {
        maskData[i] = 255;     // R
        maskData[i + 1] = 255; // G
        maskData[i + 2] = 255; // B
        maskData[i + 3] = 180; // A
        matchingPixels++;
      }
    }

    console.log('Floor mask created with', matchingPixels, 'pixels');
    return mask;
  };

  // Intelligent walls segmentation
  const createIntelligentWallsMask = (imageData: ImageData): ImageData => {
    const mask = new ImageData(imageData.width, imageData.height);
    const data = imageData.data;
    const maskData = mask.data;
    let matchingPixels = 0;

    console.log('Creating intelligent walls mask...');

    for (let i = 0; i < data.length; i += 4) {
      const pixelIndex = i / 4;
      const x = pixelIndex % imageData.width;
      const y = Math.floor(pixelIndex / imageData.width);
      const heightRatio = y / imageData.height;

      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const brightness = (r + g + b) / 3;

      // Advanced walls detection
      let isWall = false;

      // Primary condition: middle portion of image
      if (heightRatio > 0.15 && heightRatio < 0.65) {
        // Check for wall-like characteristics
        const isNeutralColor = Math.abs(r - g) < 50 && Math.abs(g - b) < 50;
        const isWallBrightness = brightness > 80 && brightness < 240;
        const isUniformTexture = Math.abs(r - brightness) < 40;

        if (isNeutralColor && isWallBrightness && isUniformTexture) {
          isWall = true;
        }
      }

      if (isWall) {
        maskData[i] = 255;     // R
        maskData[i + 1] = 255; // G
        maskData[i + 2] = 255; // B
        maskData[i + 3] = 160; // A
        matchingPixels++;
      }
    }

    console.log('Walls mask created with', matchingPixels, 'pixels');
    return mask;
  };

  // Intelligent ceiling segmentation
  const createIntelligentCeilingMask = (imageData: ImageData): ImageData => {
    const mask = new ImageData(imageData.width, imageData.height);
    const data = imageData.data;
    const maskData = mask.data;
    let matchingPixels = 0;

    console.log('Creating intelligent ceiling mask...');

    for (let i = 0; i < data.length; i += 4) {
      const pixelIndex = i / 4;
      const x = pixelIndex % imageData.width;
      const y = Math.floor(pixelIndex / imageData.width);
      const heightRatio = y / imageData.height;

      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const brightness = (r + g + b) / 3;

      // Advanced ceiling detection
      let isCeiling = false;

      // Primary condition: top 25% of image
      if (heightRatio < 0.25) {
        // Check for ceiling-like characteristics
        const isLightColor = brightness > 150;
        const isUniformColor = Math.abs(r - g) < 30 && Math.abs(g - b) < 30;
        const isWhitish = r > 180 && g > 180 && b > 180;

        if (isLightColor && (isUniformColor || isWhitish)) {
          isCeiling = true;
        }
      }

      if (isCeiling) {
        maskData[i] = 255;     // R
        maskData[i + 1] = 255; // G
        maskData[i + 2] = 255; // B
        maskData[i + 3] = 140; // A
        matchingPixels++;
      }
    }

    console.log('Ceiling mask created with', matchingPixels, 'pixels');
    return mask;
  };



  const handleSegmentClick = (segmentId: string) => {
    setSelectedSegment(selectedSegment === segmentId ? null : segmentId);
    highlightSegment(segmentId);
  };

  const highlightSegment = (segmentId: string) => {
    // Just update the overlay to show all current segment colors
    updateOverlay();
  };

  const handleMaterialChange = (segmentId: string, material: MaterialOption) => {
    setSelectedMaterials(prev => ({
      ...prev,
      [segmentId]: material
    }));
    applyColorToSegment(segmentId, material.color);
  };



  // Update overlay to show all segment modifications on the whole image
  const updateOverlay = () => {
    const overlayCanvas = overlayCanvasRef.current;
    if (!overlayCanvas) return;

    const overlayCtx = overlayCanvas.getContext('2d');
    if (!overlayCtx) return;

    // Clear the overlay
    overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);

    console.log('Updating overlay with', segments.length, 'segments');

    // Apply each segment's color overlay
    segments.forEach((segment, index) => {
      if (!segment.mask) {
        console.log(`Segment ${segment.name} has no mask`);
        return;
      }

      console.log(`Applying ${segment.name} with color ${segment.color}`);

      // Create a temporary canvas for this segment
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = overlayCanvas.width;
      tempCanvas.height = overlayCanvas.height;
      const tempCtx = tempCanvas.getContext('2d');
      if (!tempCtx) return;

      // Draw the mask
      tempCtx.putImageData(segment.mask, 0, 0);

      // Apply color with different opacity based on segment type
      tempCtx.globalCompositeOperation = 'source-atop';
      let opacity = '40'; // Default opacity
      if (segment.id === 'floor') opacity = '50'; // Floor more visible
      if (segment.id === 'walls') opacity = '35'; // Walls subtle
      if (segment.id === 'ceiling') opacity = '30'; // Ceiling very subtle

      tempCtx.fillStyle = segment.color + opacity;
      tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

      // Blend onto main overlay canvas with normal blending for better visibility
      overlayCtx.globalCompositeOperation = 'normal';
      overlayCtx.drawImage(tempCanvas, 0, 0);
    });

    console.log('Overlay update completed');
  };

  const applyColorToSegment = (segmentId: string, color: string) => {
    // Update segment color
    setSegments(prev => prev.map(s =>
      s.id === segmentId ? { ...s, color } : s
    ));

    // Update the overlay to show the change
    setTimeout(() => updateOverlay(), 10); // Small delay to ensure state update
  };

  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading Executive Bedroom...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h1>🤖 AI Room Editor - Intelligent Segmentation</h1>
        <p>Advanced computer vision algorithms for precise room element detection</p>
        {segments.length > 0 && !isSegmenting && (
          <div className={styles.statusMessage}>
            ✅ Segmentation complete! Select materials below to edit the room.
          </div>
        )}
      </div>

      {/* Controls */}
      {showControls && !isLoading && (
        <div className={styles.controlsPanel}>
          {/* Material Selection */}
          <div className={styles.section}>
            <h3>🏗️ Materials</h3>

            <div className={styles.materialGroup}>
              <label>Floor Material:</label>
              <select
                value={selectedMaterials.floor.id}
                onChange={(e) => {
                  const material = floorMaterials.find(m => m.id === e.target.value);
                  if (material) handleMaterialChange('floor', material);
                }}
              >
                {floorMaterials.map(material => (
                  <option key={material.id} value={material.id}>{material.name}</option>
                ))}
              </select>
            </div>

            <div className={styles.materialGroup}>
              <label>Wall Material:</label>
              <select
                value={selectedMaterials.walls.id}
                onChange={(e) => {
                  const material = wallMaterials.find(m => m.id === e.target.value);
                  if (material) handleMaterialChange('walls', material);
                }}
              >
                {wallMaterials.map(material => (
                  <option key={material.id} value={material.id}>{material.name}</option>
                ))}
              </select>
            </div>

            <div className={styles.materialGroup}>
              <label>Ceiling Material:</label>
              <select
                value={selectedMaterials.ceiling.id}
                onChange={(e) => {
                  const material = ceilingMaterials.find(m => m.id === e.target.value);
                  if (material) handleMaterialChange('ceiling', material);
                }}
              >
                {ceilingMaterials.map(material => (
                  <option key={material.id} value={material.id}>{material.name}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Segment Controls */}
          <div className={styles.section}>
            <h3>🎯 AI Detected Segments</h3>
            {segments.map(segment => (
              <div key={segment.id} className={styles.segmentControl}>
                <button
                  className={`${styles.segmentButton} ${selectedSegment === segment.id ? styles.selected : ''}`}
                  onClick={() => handleSegmentClick(segment.id)}
                >
                  <div
                    className={styles.colorPreview}
                    style={{ backgroundColor: segment.color }}
                  ></div>
                  {segment.name}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      <button
        className={styles.toggleControls}
        onClick={() => setShowControls(!showControls)}
      >
        {showControls ? 'Hide Controls' : 'Show Controls'}
      </button>

      {/* Main Canvas Area */}
      <div className={styles.mainContent}>
        <div className={styles.canvasContainer}>
          <canvas
            ref={canvasRef}
            className={styles.canvas}
          />
          <canvas
            ref={overlayCanvasRef}
            className={styles.overlayCanvas}
          />

          {isSegmenting && (
            <div className={styles.segmentingOverlay}>
              <div className={styles.spinner}></div>
              <p>🤖 AI Segmenting Room...</p>
            </div>
          )}

          {isLoading && (
            <div className={styles.segmentingOverlay}>
              <div className={styles.spinner}></div>
              <p>Loading Room Image...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
