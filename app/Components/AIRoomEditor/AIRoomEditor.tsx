'use client';

import React from 'react';
import styles from './AIRoomEditor.module.css';
import UnityOfficeEditor from '../UnityOfficeEditor/UnityOfficeEditor';

interface MaterialOption {
  id: string;
  name: string;
  color: string;
  texture: string;
}

export default function AIRoomEditor() {
  const handleMaterialChange = (elementType: string, material: MaterialOption) => {
    console.log(`🔄 AI Room Editor - Material changed: ${elementType} -> ${material.name}`);
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h1>🏢 AI Room Editor - Unity Executive Office</h1>
        <p>Professional 3D office environment with AI-powered interior design modifications</p>
      </div>

      {/* Unity Office Editor Component */}
      <UnityOfficeEditor onMaterialChange={handleMaterialChange} />

      {/* Additional Info */}
      <div className={styles.infoSection}>
        <h3>🎯 About This Editor</h3>
        <p>
          This Unity-based room editor provides a realistic 3D executive office environment
          where you can modify materials in real-time. The Unity WebGL build offers superior
          rendering quality and interactive controls compared to traditional canvas-based approaches.
        </p>
        <ul>
          <li>✅ Real-time 3D rendering with Unity WebGL</li>
          <li>✅ Professional executive office model</li>
          <li>✅ Interactive material selection</li>
          <li>✅ High-quality lighting and shadows</li>
          <li>✅ Smooth camera controls</li>
        </ul>
      </div>
    </div>
  );
}