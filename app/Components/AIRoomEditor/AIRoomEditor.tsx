'use client';

import React, { useState, useRef, useEffect } from 'react';
import styles from './AIRoomEditor.module.css';

export default function AIRoomEditor() {
  const unityContainerRef = useRef<HTMLDivElement>(null);
  const [isUnityLoaded, setIsUnityLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadUnityBedroom();
  }, []);

  const loadUnityBedroom = async () => {
    console.log('🛏️ Loading Unity Cozy Bedroom with built-in controls...');
    setIsLoading(true);

    try {
      // Check if Unity loader is available
      if (typeof window !== 'undefined' && (window as any).createUnityInstance) {
        const canvas = document.createElement('canvas');
        canvas.width = 1200;
        canvas.height = 800;
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        canvas.style.border = '2px solid #ddd';
        canvas.style.borderRadius = '8px';

        if (unityContainerRef.current) {
          unityContainerRef.current.appendChild(canvas);
        }

        const unityConfig = {
          dataUrl: "/unity/cozy-bedroom/Build/cozy-bedroom.data",
          frameworkUrl: "/unity/cozy-bedroom/Build/cozy-bedroom.framework.js",
          codeUrl: "/unity/cozy-bedroom/Build/cozy-bedroom.wasm",
          streamingAssetsUrl: "StreamingAssets",
          companyName: "Pionare",
          productName: "Cozy Bedroom Editor",
          productVersion: "1.0",
        };

        console.log('🔧 Creating Unity bedroom instance...');
        const unityInstance = await (window as any).createUnityInstance(canvas, unityConfig, (progress: number) => {
          console.log(`📊 Unity bedroom loading progress: ${Math.round(progress * 100)}%`);
        });

        setIsUnityLoaded(true);
        setIsLoading(false);

        console.log('✅ Unity Cozy Bedroom loaded successfully!');
        console.log('🎮 Built-in Unity UI controls are now active:');
        console.log('   📱 Material panel (left side)');
        console.log('   📷 Camera controls (bottom right)');
        console.log('   ⌨️ Keyboard shortcuts (1-4, R, T, C, Space)');
        console.log('   🖱️ Mouse controls (drag to rotate, scroll to zoom)');

        // Store instance for potential cleanup
        (window as any).currentUnityInstance = unityInstance;

        // Test Unity communication
        setTimeout(() => {
          console.log('🧪 Testing Unity communication...');
          if (unityInstance && unityInstance.SendMessage) {
            // Test message to Unity (optional)
            console.log('📡 Unity communication ready');
          }
        }, 2000);

      } else {
        // Load Unity loader script if not available
        const script = document.createElement('script');
        script.src = '/unity/cozy-bedroom/Build/UnityLoader.js';
        script.onload = () => loadUnityBedroom();
        script.onerror = () => {
          console.error('❌ Failed to load Unity bedroom loader script');
          setIsLoading(false);
        };
        document.head.appendChild(script);
      }
    } catch (error) {
      console.error('❌ Unity bedroom loading error:', error);
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h1>🛏️ AI Room Editor - Unity Cozy Bedroom</h1>
        <p>Realistic 3D bedroom environment with built-in Unity UI controls for interior design</p>
        {isUnityLoaded && (
          <div className={styles.statusMessage}>
            ✅ Unity bedroom loaded! All material and camera controls are available within the 3D environment.
          </div>
        )}
      </div>

      {/* Unity WebGL Container */}
      <div className={styles.unityContainer}>
        <div
          ref={unityContainerRef}
          className={styles.unityCanvas}
          style={{
            width: '100%',
            height: '700px',
            background: '#231F20',
            border: '2px solid #ccc',
            borderRadius: '8px',
            position: 'relative'
          }}
        >
          {isLoading && (
            <div className={styles.loadingOverlay}>
              <div className={styles.spinner}></div>
              <p>🛏️ Loading Unity Cozy Bedroom...</p>
              <p style={{ fontSize: '0.8rem', opacity: 0.7 }}>
                Preparing 3D bedroom with built-in material controls
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Unity Controls Info */}
      <div className={styles.infoSection}>
        <h3>🎮 Built-in Unity Controls</h3>
        <p>
          This Unity bedroom includes all controls built directly into the 3D environment.
          No external interface needed - everything is accessible within the Unity scene!
        </p>

        <div className={styles.controlsGrid}>
          <div className={styles.controlGroup}>
            <h4>🎨 Material Controls</h4>
            <ul>
              <li>Floor material dropdown (left panel)</li>
              <li>Wall material dropdown (left panel)</li>
              <li>Ceiling material dropdown (left panel)</li>
              <li>Reset to defaults button</li>
              <li>Hide/show UI toggle</li>
            </ul>
          </div>

          <div className={styles.controlGroup}>
            <h4>📷 Camera Controls</h4>
            <ul>
              <li>Mouse drag to rotate camera</li>
              <li>Scroll wheel to zoom</li>
              <li>Preset camera angles (1-4 keys)</li>
              <li>Zoom slider (bottom right)</li>
              <li>Reset camera button (R key)</li>
            </ul>
          </div>

          <div className={styles.controlGroup}>
            <h4>⌨️ Keyboard Shortcuts</h4>
            <ul>
              <li><strong>1-4:</strong> Camera presets</li>
              <li><strong>R:</strong> Reset camera</li>
              <li><strong>T:</strong> Toggle UI panels</li>
              <li><strong>ESC:</strong> Show/hide all UI</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}