'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import styles from './AIRoomEditor.module.css';

interface SegmentedRegion {
  id: string;
  name: string;
  mask: ImageData | null;
  isSelected: boolean;
  color: string;
  originalColor: string;
}

interface MaterialOption {
  id: string;
  name: string;
  color: string;
  texture: string;
}

interface RoomSettings {
  brightness: number;
  contrast: number;
  saturation: number;
}

// Material options similar to PhotorealisticBedroom
const floorMaterials: MaterialOption[] = [
  { id: 'oak-hardwood', name: 'Oak Hardwood', color: '#D2B48C', texture: 'wood' },
  { id: 'premium-carpet', name: 'Premium Carpet', color: '#8B4513', texture: 'carpet' },
  { id: 'white-marble', name: 'White Marble', color: '#F8F8FF', texture: 'marble' },
  { id: 'luxury-vinyl', name: 'Luxury Vinyl', color: '#CD853F', texture: 'vinyl' },
  { id: 'dark-walnut', name: 'Dark Walnut', color: '#654321', texture: 'wood' },
  { id: 'light-bamboo', name: 'Light Bamboo', color: '#F5DEB3', texture: 'bamboo' }
];

const wallMaterials: MaterialOption[] = [
  { id: 'white-paint', name: 'White Paint', color: '#F8F8FF', texture: 'paint' },
  { id: 'wood-paneling', name: 'Wood Paneling', color: '#DEB887', texture: 'wood' },
  { id: 'modern-wallpaper', name: 'Modern Wallpaper', color: '#E6E6FA', texture: 'wallpaper' },
  { id: 'exposed-brick', name: 'Exposed Brick', color: '#CD853F', texture: 'brick' },
  { id: 'soft-beige', name: 'Soft Beige', color: '#F5F5DC', texture: 'paint' },
  { id: 'accent-blue', name: 'Accent Blue', color: '#B0C4DE', texture: 'paint' }
];

const ceilingMaterials: MaterialOption[] = [
  { id: 'smooth-gypsum', name: 'Smooth Gypsum', color: '#FFFFFF', texture: 'smooth' },
  { id: 'wood-planks', name: 'Wood Planks', color: '#DEB887', texture: 'wood' },
  { id: 'textured-gypsum', name: 'Textured Gypsum', color: '#F8F8FF', texture: 'textured' },
  { id: 'suspended-grid', name: 'Suspended Grid', color: '#F0F0F0', texture: 'grid' }
];

export default function AIRoomEditor() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const overlayCanvasRef = useRef<HTMLCanvasElement>(null);
  const [originalImage, setOriginalImage] = useState<HTMLImageElement | null>(null);
  const [segments, setSegments] = useState<SegmentedRegion[]>([]);
  const [selectedSegment, setSelectedSegment] = useState<string | null>(null);
  const [isSegmenting, setIsSegmenting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const [selectedMaterials, setSelectedMaterials] = useState({
    floor: floorMaterials[0],
    walls: wallMaterials[0],
    ceiling: ceilingMaterials[0]
  });
  const [debugInfo, setDebugInfo] = useState<string>('');

  // Load the executive bedroom image
  useEffect(() => {
    console.log('🖼️ Starting image load process...');
    console.log('🔍 Canvas ref status:', !!canvasRef.current);
    console.log('🔍 Overlay canvas ref status:', !!overlayCanvasRef.current);

    // Set a timeout to force fallback if image takes too long
    const loadTimeout = setTimeout(() => {
      console.log('⏰ Image loading timeout - creating fallback...');
      createFallbackImage();
    }, 3000); // 3 second timeout

    // Wait for canvas to be mounted
    const checkCanvasAndLoad = () => {
      if (!canvasRef.current) {
        console.log('⏳ Canvas not ready, waiting...');
        setTimeout(checkCanvasAndLoad, 100);
        return;
      }

      console.log('✅ Canvas is ready, starting image load...');
      setIsLoading(true);

      // Skip image loading and go directly to fallback for now
      console.log('🔄 Skipping image load, creating fallback directly...');
      clearTimeout(loadTimeout);
      createFallbackImage();

      /*
      // Original image loading code - commented out for now
      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        console.log('✅ Image loaded successfully:', img.width, 'x', img.height);
        clearTimeout(loadTimeout);
        setOriginalImage(img);
        setIsLoading(false);
        drawImageToCanvas(img);
        setTimeout(() => {
          performAISegmentation(img);
        }, 200);
      };

      img.onerror = (error) => {
        console.error('❌ Failed to load executive bedroom image:', error);
        clearTimeout(loadTimeout);
        createFallbackImage();
      };

      img.src = '/images/rooms/executive-bedroom.jpg';
      */
    };

    // Start checking for canvas
    checkCanvasAndLoad();

    // Cleanup timeout on unmount
    return () => {
      clearTimeout(loadTimeout);
    };
  }, []);

  // Create a fallback image when the real image fails to load
  const createFallbackImage = () => {
    console.log('🎨 Creating fallback bedroom image...');

    // Create a canvas to draw a simple bedroom scene
    const fallbackCanvas = document.createElement('canvas');
    fallbackCanvas.width = 800;
    fallbackCanvas.height = 600;
    const ctx = fallbackCanvas.getContext('2d');

    if (!ctx) {
      console.error('❌ Could not create fallback canvas context');
      setIsLoading(false);
      return;
    }

    // Draw a simple bedroom scene
    // Background (walls)
    ctx.fillStyle = '#F5F5DC'; // Beige walls
    ctx.fillRect(0, 0, 800, 400);

    // Floor
    ctx.fillStyle = '#8B4513'; // Brown floor
    ctx.fillRect(0, 400, 800, 200);

    // Ceiling
    ctx.fillStyle = '#FFFFFF'; // White ceiling
    ctx.fillRect(0, 0, 800, 100);

    // Add some simple furniture outlines
    // Bed
    ctx.fillStyle = '#654321';
    ctx.fillRect(200, 300, 400, 150);
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(220, 280, 360, 40); // Pillows

    // Window
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 3;
    ctx.strokeRect(600, 150, 150, 200);
    ctx.fillStyle = '#87CEEB';
    ctx.fillRect(605, 155, 140, 190);

    // Convert canvas to image
    const fallbackImg = new Image();
    fallbackImg.onload = () => {
      console.log('✅ Fallback image created successfully');
      setOriginalImage(fallbackImg);
      setIsLoading(false);
      drawImageToCanvas(fallbackImg);

      setTimeout(() => {
        performAISegmentation(fallbackImg);
      }, 200);
    };

    fallbackImg.src = fallbackCanvas.toDataURL();
  };

  // Test canvas functionality on mount
  useEffect(() => {
    const testCanvas = () => {
      const canvas = canvasRef.current;
      if (canvas) {
        console.log('🧪 Testing canvas functionality...');
        const ctx = canvas.getContext('2d');
        if (ctx) {
          // Draw a simple test pattern
          ctx.fillStyle = '#FF0000';
          ctx.fillRect(10, 10, 100, 100);
          ctx.fillStyle = '#00FF00';
          ctx.fillRect(120, 10, 100, 100);
          ctx.fillStyle = '#0000FF';
          ctx.fillRect(240, 10, 100, 100);
          console.log('✅ Canvas test pattern drawn');
        }
      }
    };

    // Test canvas after a short delay
    setTimeout(testCanvas, 500);
  }, []);

  // Update overlay whenever segments change
  useEffect(() => {
    if (segments.length > 0) {
      updateOverlay();
    }
  }, [segments]);

  const drawImageToCanvas = (img: HTMLImageElement) => {
    console.log('🎨 Starting drawImageToCanvas...');

    const canvas = canvasRef.current;
    if (!canvas) {
      console.error('❌ Canvas ref is null');
      return;
    }
    console.log('✅ Canvas ref found:', canvas);

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('❌ Canvas context is null');
      return;
    }
    console.log('✅ Canvas context obtained');

    // Validate image
    if (!img || !img.complete || img.naturalWidth === 0) {
      console.error('❌ Invalid image:', {
        img: !!img,
        complete: img?.complete,
        naturalWidth: img?.naturalWidth,
        naturalHeight: img?.naturalHeight
      });
      return;
    }

    // Set canvas size to match image aspect ratio with larger dimensions
    const maxWidth = 1200;  // Increased from 800
    const maxHeight = 900;  // Increased from 600
    const aspectRatio = img.naturalWidth / img.naturalHeight;

    let canvasWidth = maxWidth;
    let canvasHeight = maxWidth / aspectRatio;

    if (canvasHeight > maxHeight) {
      canvasHeight = maxHeight;
      canvasWidth = maxHeight * aspectRatio;
    }

    // Ensure minimum size for better visibility
    const minWidth = 600;
    const minHeight = 400;

    if (canvasWidth < minWidth) {
      canvasWidth = minWidth;
      canvasHeight = minWidth / aspectRatio;
    }

    if (canvasHeight < minHeight) {
      canvasHeight = minHeight;
      canvasWidth = minHeight * aspectRatio;
    }

    console.log(`🖼️ Setting canvas dimensions: ${Math.round(canvasWidth)} x ${Math.round(canvasHeight)} (original: ${img.naturalWidth} x ${img.naturalHeight})`);

    // Round dimensions to avoid sub-pixel issues
    canvasWidth = Math.round(canvasWidth);
    canvasHeight = Math.round(canvasHeight);

    canvas.width = canvasWidth;
    canvas.height = canvasHeight;

    // Set canvas style for proper display
    canvas.style.width = canvasWidth + 'px';
    canvas.style.height = canvasHeight + 'px';

    console.log('🎨 Canvas dimensions set, now drawing image...');

    // Clear canvas first
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // Draw the image with high quality
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    try {
      ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);
      console.log('✅ Image drawn to canvas successfully');
    } catch (error) {
      console.error('❌ Error drawing image to canvas:', error);
      return;
    }

    // Setup overlay canvas
    const overlayCanvas = overlayCanvasRef.current;
    if (overlayCanvas) {
      overlayCanvas.width = canvasWidth;
      overlayCanvas.height = canvasHeight;
      overlayCanvas.style.width = canvasWidth + 'px';
      overlayCanvas.style.height = canvasHeight + 'px';
      console.log('✅ Overlay canvas setup complete');
    } else {
      console.warn('⚠️ Overlay canvas ref is null');
    }

    console.log('🎨 drawImageToCanvas completed successfully');
  };

  // AI-powered room segmentation using computer vision techniques
  const performAISegmentation = async (img: HTMLImageElement) => {
    console.log('🚀 Starting AI room segmentation...');
    setIsSegmenting(true);

    try {
      // Wait for canvas to be ready
      await new Promise(resolve => setTimeout(resolve, 50));

      const canvas = canvasRef.current;
      console.log('📊 Canvas ref:', canvas);

      if (!canvas) {
        console.error('❌ Canvas not available in performAISegmentation');
        setIsSegmenting(false);
        return;
      }

      console.log('📐 Canvas dimensions:', canvas.width, 'x', canvas.height);

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.error('❌ Canvas context not available');
        setIsSegmenting(false);
        return;
      }

      console.log('🎨 Canvas context obtained successfully');

      // Get image data for analysis
      console.log('📸 Getting image data...');
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      console.log('✅ Image data obtained:', imageData.width, 'x', imageData.height, 'pixels');

      console.log('🧠 Creating intelligent segments...');

      // Create intelligent room segments using computer vision
      const detectedSegments: SegmentedRegion[] = [
        {
          id: 'floor',
          name: 'Floor',
          mask: createIntelligentFloorMask(imageData),
          isSelected: false,
          color: selectedMaterials.floor.color,
          originalColor: selectedMaterials.floor.color
        },
        {
          id: 'walls',
          name: 'Walls',
          mask: createIntelligentWallsMask(imageData),
          isSelected: false,
          color: selectedMaterials.walls.color,
          originalColor: selectedMaterials.walls.color
        },
        {
          id: 'ceiling',
          name: 'Ceiling',
          mask: createIntelligentCeilingMask(imageData),
          isSelected: false,
          color: selectedMaterials.ceiling.color,
          originalColor: selectedMaterials.ceiling.color
        }
      ];

      console.log('🎯 Setting segments:', detectedSegments);

      // Log segment statistics
      detectedSegments.forEach(segment => {
        const pixelCount = segment.mask ?
          Array.from(segment.mask.data).filter((_, i) => i % 4 === 3 && segment.mask!.data[i] > 0).length : 0;
        console.log(`📊 ${segment.name}: ${pixelCount} pixels detected`);
      });

      setSegments(detectedSegments);
      console.log('✅ AI segmentation completed successfully with', detectedSegments.length, 'segments');

      // Show a brief success message
      setTimeout(() => {
        console.log('🎉 Segmentation ready - user can now edit materials');
      }, 500);

    } catch (error) {
      console.error('💥 AI segmentation failed with error:', error);
      console.error('📋 Error name:', error.name);
      console.error('📝 Error message:', error.message);
      console.error('📚 Error stack:', error.stack);

      // Don't throw, just log and continue
      console.log('🔄 Continuing despite error...');
    } finally {
      console.log('🏁 Setting isSegmenting to false');
      setIsSegmenting(false);
    }
  };



  // Intelligent floor segmentation using computer vision
  const createIntelligentFloorMask = (imageData: ImageData): ImageData => {
    const mask = new ImageData(imageData.width, imageData.height);
    const data = imageData.data;
    const maskData = mask.data;
    let matchingPixels = 0;

    console.log('Creating intelligent floor mask...');

    for (let i = 0; i < data.length; i += 4) {
      const pixelIndex = i / 4;
      const x = pixelIndex % imageData.width;
      const y = Math.floor(pixelIndex / imageData.width);
      const heightRatio = y / imageData.height;
      const widthRatio = x / imageData.width;

      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const brightness = (r + g + b) / 3;

      // Enhanced floor detection algorithm
      let isFloor = false;

      // Primary condition: bottom portion of image (floors are at the bottom)
      if (heightRatio > 0.55) {
        // Check for floor-like colors and textures
        const isWoodTone = (r > g && r > b && r > 70 && r < 220);
        const isCarpetTone = (Math.abs(r - g) < 40 && Math.abs(g - b) < 40 && brightness < 180);
        const isTileTone = (brightness > 100 && brightness < 240 && Math.abs(r - g) < 50);
        const isMarbleTone = (brightness > 150 && Math.abs(r - g) < 30 && Math.abs(g - b) < 30);
        const isDarkFloor = (brightness < 100 && Math.abs(r - g) < 50);

        // Look for horizontal patterns (floors are typically horizontal)
        const isHorizontalRegion = widthRatio > 0.05 && widthRatio < 0.95;

        if (isHorizontalRegion && (isWoodTone || isCarpetTone || isTileTone || isMarbleTone || isDarkFloor)) {
          isFloor = true;
        }
      }
      // Secondary condition: perspective-aware floor detection for middle area
      else if (heightRatio > 0.4 && heightRatio <= 0.55) {
        // More selective detection in the middle area
        const isWoodTone = (r > g && r > b && r > 90 && r < 180 && g > 60 && b > 40);
        const isCarpetTone = (Math.abs(r - g) < 25 && Math.abs(g - b) < 25 && brightness > 60 && brightness < 140);
        const isTileTone = (brightness > 130 && brightness < 200 && Math.abs(r - g) < 30);

        // Only detect if it's clearly floor-like and in perspective
        if ((isWoodTone || isCarpetTone || isTileTone) && widthRatio > 0.2 && widthRatio < 0.8) {
          isFloor = true;
        }
      }

      if (isFloor) {
        maskData[i] = 255;     // R
        maskData[i + 1] = 255; // G
        maskData[i + 2] = 255; // B
        maskData[i + 3] = 180; // A
        matchingPixels++;
      }
    }

    console.log('Floor mask created with', matchingPixels, 'pixels');
    return mask;
  };

  // Intelligent walls segmentation
  const createIntelligentWallsMask = (imageData: ImageData): ImageData => {
    const mask = new ImageData(imageData.width, imageData.height);
    const data = imageData.data;
    const maskData = mask.data;
    let matchingPixels = 0;

    console.log('Creating intelligent walls mask...');

    for (let i = 0; i < data.length; i += 4) {
      const pixelIndex = i / 4;
      const x = pixelIndex % imageData.width;
      const y = Math.floor(pixelIndex / imageData.width);
      const heightRatio = y / imageData.height;
      const widthRatio = x / imageData.width;

      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const brightness = (r + g + b) / 3;

      // Enhanced walls detection algorithm
      let isWall = false;

      // Primary condition: middle portion of image (walls are typically in the middle vertical area)
      if (heightRatio > 0.1 && heightRatio < 0.7) {
        // Check for wall-like characteristics
        const isNeutralColor = Math.abs(r - g) < 60 && Math.abs(g - b) < 60;
        const isWallBrightness = brightness > 60 && brightness < 250;

        // Look for vertical patterns (walls are typically vertical)
        const isVerticalRegion = widthRatio > 0.1 && widthRatio < 0.9;

        // Check for common wall colors (whites, beiges, grays)
        const isWhitish = r > 150 && g > 150 && b > 150;
        const isBeigeish = r > g && r > b && r > 120 && g > 100 && b > 80;
        const isGrayish = Math.abs(r - g) < 30 && Math.abs(g - b) < 30 && brightness > 80 && brightness < 200;

        // Enhanced detection logic
        if (isVerticalRegion && isWallBrightness && (isNeutralColor || isWhitish || isBeigeish || isGrayish)) {
          // Additional check: avoid floor and ceiling areas
          const notFloorArea = heightRatio < 0.6;
          const notCeilingArea = heightRatio > 0.15;

          if (notFloorArea && notCeilingArea) {
            isWall = true;
          }
        }
      }

      if (isWall) {
        maskData[i] = 255;     // R
        maskData[i + 1] = 255; // G
        maskData[i + 2] = 255; // B
        maskData[i + 3] = 160; // A
        matchingPixels++;
      }
    }

    console.log('Walls mask created with', matchingPixels, 'pixels');
    return mask;
  };

  // Intelligent ceiling segmentation
  const createIntelligentCeilingMask = (imageData: ImageData): ImageData => {
    const mask = new ImageData(imageData.width, imageData.height);
    const data = imageData.data;
    const maskData = mask.data;
    let matchingPixels = 0;

    console.log('Creating intelligent ceiling mask...');

    for (let i = 0; i < data.length; i += 4) {
      const pixelIndex = i / 4;
      const x = pixelIndex % imageData.width;
      const y = Math.floor(pixelIndex / imageData.width);
      const heightRatio = y / imageData.height;
      const widthRatio = x / imageData.width;

      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const brightness = (r + g + b) / 3;

      // Enhanced ceiling detection algorithm
      let isCeiling = false;

      // Primary condition: top portion of image (expanded from 25% to 35%)
      if (heightRatio < 0.35) {
        // Check for ceiling-like characteristics
        const isLightColor = brightness > 120; // Lowered threshold
        const isUniformColor = Math.abs(r - g) < 40 && Math.abs(g - b) < 40; // More tolerant
        const isWhitish = r > 160 && g > 160 && b > 160; // Lowered threshold
        const isCreamish = r > 180 && g > 170 && b > 150; // Cream/beige ceiling
        const isGrayish = Math.abs(r - g) < 25 && Math.abs(g - b) < 25 && brightness > 100;

        // Look for horizontal patterns (ceilings are typically horizontal)
        const isHorizontalRegion = widthRatio > 0.05 && widthRatio < 0.95;

        // Enhanced detection logic
        if (isHorizontalRegion && (isLightColor || brightness > 100)) {
          if (isUniformColor || isWhitish || isCreamish || isGrayish) {
            // Additional check: avoid wall areas
            const notWallArea = heightRatio < 0.3;

            if (notWallArea) {
              isCeiling = true;
            }
          }
        }

        // Special case: very top of image is likely ceiling
        if (heightRatio < 0.15 && brightness > 80) {
          isCeiling = true;
        }
      }

      if (isCeiling) {
        maskData[i] = 255;     // R
        maskData[i + 1] = 255; // G
        maskData[i + 2] = 255; // B
        maskData[i + 3] = 140; // A
        matchingPixels++;
      }
    }

    console.log('Ceiling mask created with', matchingPixels, 'pixels');
    return mask;
  };



  const handleSegmentClick = (segmentId: string) => {
    setSelectedSegment(selectedSegment === segmentId ? null : segmentId);
    highlightSegment(segmentId);
  };

  const highlightSegment = (segmentId: string) => {
    // Just update the overlay to show all current segment colors
    updateOverlay();
  };

  const handleMaterialChange = (segmentId: string, material: MaterialOption) => {
    setSelectedMaterials(prev => ({
      ...prev,
      [segmentId]: material
    }));
    applyColorToSegment(segmentId, material.color);
  };



  // Update overlay to show all segment modifications on the whole image
  const updateOverlay = () => {
    const overlayCanvas = overlayCanvasRef.current;
    if (!overlayCanvas) return;

    const overlayCtx = overlayCanvas.getContext('2d');
    if (!overlayCtx) return;

    // Clear the overlay
    overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);

    console.log('Updating overlay with', segments.length, 'segments');

    // Apply each segment's color overlay
    segments.forEach((segment, index) => {
      if (!segment.mask) {
        console.log(`Segment ${segment.name} has no mask`);
        return;
      }

      console.log(`Applying ${segment.name} with color ${segment.color}`);

      // Create a temporary canvas for this segment
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = overlayCanvas.width;
      tempCanvas.height = overlayCanvas.height;
      const tempCtx = tempCanvas.getContext('2d');
      if (!tempCtx) return;

      // Draw the mask
      tempCtx.putImageData(segment.mask, 0, 0);

      // Apply color with different opacity based on segment type
      tempCtx.globalCompositeOperation = 'source-atop';
      let opacity = '60'; // Increased default opacity for better visibility
      if (segment.id === 'floor') opacity = '70'; // Floor more visible
      if (segment.id === 'walls') opacity = '55'; // Walls more visible
      if (segment.id === 'ceiling') opacity = '50'; // Ceiling more visible

      tempCtx.fillStyle = segment.color + opacity;
      tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

      // Blend onto main overlay canvas with normal blending for better visibility
      overlayCtx.globalCompositeOperation = 'normal';
      overlayCtx.globalAlpha = 0.8; // Add some transparency to the overall overlay
      overlayCtx.drawImage(tempCanvas, 0, 0);
      overlayCtx.globalAlpha = 1.0; // Reset alpha
    });

    console.log('Overlay update completed');
  };

  const applyColorToSegment = (segmentId: string, color: string) => {
    // Update segment color
    setSegments(prev => prev.map(s =>
      s.id === segmentId ? { ...s, color } : s
    ));

    // Update the overlay to show the change
    setTimeout(() => updateOverlay(), 10); // Small delay to ensure state update
  };

  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading Executive Bedroom...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h1>🤖 AI Room Editor - Intelligent Segmentation</h1>
        <p>Advanced computer vision algorithms for precise room element detection</p>
        {segments.length > 0 && !isSegmenting && (
          <div className={styles.statusMessage}>
            ✅ Segmentation complete! Select materials below to edit the room.
            <button
              onClick={() => originalImage && performAISegmentation(originalImage)}
              style={{ marginLeft: '1rem', padding: '0.5rem 1rem', background: '#FFD700', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
            >
              🔄 Re-segment
            </button>
          </div>
        )}
      </div>

      {/* Controls */}
      {showControls && !isLoading && (
        <div className={styles.controlsPanel}>
          {/* Material Selection */}
          <div className={styles.section}>
            <h3>🏗️ Materials</h3>

            <div className={styles.materialGroup}>
              <label>Floor Material:</label>
              <select
                value={selectedMaterials.floor.id}
                onChange={(e) => {
                  const material = floorMaterials.find(m => m.id === e.target.value);
                  if (material) handleMaterialChange('floor', material);
                }}
              >
                {floorMaterials.map(material => (
                  <option key={material.id} value={material.id}>{material.name}</option>
                ))}
              </select>
            </div>

            <div className={styles.materialGroup}>
              <label>Wall Material:</label>
              <select
                value={selectedMaterials.walls.id}
                onChange={(e) => {
                  const material = wallMaterials.find(m => m.id === e.target.value);
                  if (material) handleMaterialChange('walls', material);
                }}
              >
                {wallMaterials.map(material => (
                  <option key={material.id} value={material.id}>{material.name}</option>
                ))}
              </select>
            </div>

            <div className={styles.materialGroup}>
              <label>Ceiling Material:</label>
              <select
                value={selectedMaterials.ceiling.id}
                onChange={(e) => {
                  const material = ceilingMaterials.find(m => m.id === e.target.value);
                  if (material) handleMaterialChange('ceiling', material);
                }}
              >
                {ceilingMaterials.map(material => (
                  <option key={material.id} value={material.id}>{material.name}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Segment Controls */}
          <div className={styles.section}>
            <h3>🎯 AI Detected Segments</h3>
            {segments.map(segment => (
              <div key={segment.id} className={styles.segmentControl}>
                <button
                  className={`${styles.segmentButton} ${selectedSegment === segment.id ? styles.selected : ''}`}
                  onClick={() => handleSegmentClick(segment.id)}
                >
                  <div
                    className={styles.colorPreview}
                    style={{ backgroundColor: segment.color }}
                  ></div>
                  {segment.name}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      <button
        className={styles.toggleControls}
        onClick={() => setShowControls(!showControls)}
      >
        {showControls ? 'Hide Controls' : 'Show Controls'}
      </button>

      {/* Main Canvas Area */}
      <div className={styles.mainContent}>
        <div className={styles.canvasContainer}>
          <canvas
            ref={canvasRef}
            className={styles.canvas}
            width={800}
            height={600}
            style={{
              minWidth: '600px',
              minHeight: '400px',
              background: '#f0f0f0',
              border: '2px solid #ccc'
            }}
          />
          <canvas
            ref={overlayCanvasRef}
            className={styles.overlayCanvas}
            width={800}
            height={600}
          />

          {isSegmenting && (
            <div className={styles.segmentingOverlay}>
              <div className={styles.spinner}></div>
              <p>🤖 AI Segmenting Room...</p>
            </div>
          )}

          {isLoading && (
            <div className={styles.segmentingOverlay}>
              <div className={styles.spinner}></div>
              <p>Loading Room Image...</p>
              <p style={{ fontSize: '0.8rem', opacity: 0.7 }}>
                Canvas: {canvasRef.current?.width || 'N/A'} x {canvasRef.current?.height || 'N/A'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
