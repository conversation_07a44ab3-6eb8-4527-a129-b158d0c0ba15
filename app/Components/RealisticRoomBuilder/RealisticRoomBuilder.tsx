'use client';

import React, { useRef, useEffect, useState } from 'react';
import styles from './RealisticRoomBuilder.module.css';

interface RoomDimensions {
  length: number;
  width: number;
  height: number;
}

interface MaterialOption {
  id: string;
  name: string;
  color: string;
  texture: string;
  normalMap?: string;
  roughness: number;
  metalness: number;
}

interface LightingOption {
  id: string;
  name: string;
  type: 'ambient' | 'directional' | 'point' | 'spot';
  intensity: number;
  color: string;
  position?: [number, number, number];
}

const floorMaterials: MaterialOption[] = [
  { id: 'oak-wood', name: 'Oak Hardwood', color: '#D2B48C', texture: 'wood', roughness: 0.8, metalness: 0.0 },
  { id: 'dark-wood', name: 'Dark Hardwood', color: '#8B4513', texture: 'wood', roughness: 0.7, metalness: 0.0 },
  { id: 'marble-white', name: 'White Marble', color: '#F8F8FF', texture: 'marble', roughness: 0.1, metalness: 0.0 },
  { id: 'concrete', name: 'Polished Concrete', color: '#A9A9A9', texture: 'concrete', roughness: 0.3, metalness: 0.0 },
  { id: 'ceramic-tile', name: 'Ceramic Tile', color: '#E6E6FA', texture: 'tile', roughness: 0.2, metalness: 0.0 },
  { id: 'luxury-carpet', name: 'Luxury Carpet', color: '#DDA0DD', texture: 'fabric', roughness: 0.9, metalness: 0.0 }
];

const wallMaterials: MaterialOption[] = [
  { id: 'white-paint', name: 'White Paint', color: '#FFFFFF', texture: 'smooth', roughness: 0.5, metalness: 0.0 },
  { id: 'beige-paint', name: 'Beige Paint', color: '#F5F5DC', texture: 'smooth', roughness: 0.5, metalness: 0.0 },
  { id: 'gray-paint', name: 'Gray Paint', color: '#808080', texture: 'smooth', roughness: 0.5, metalness: 0.0 },
  { id: 'brick-red', name: 'Red Brick', color: '#B22222', texture: 'brick', roughness: 0.8, metalness: 0.0 },
  { id: 'wood-panel', name: 'Wood Paneling', color: '#8B4513', texture: 'wood', roughness: 0.7, metalness: 0.0 },
  { id: 'stone-wall', name: 'Stone Wall', color: '#696969', texture: 'stone', roughness: 0.9, metalness: 0.0 }
];

const ceilingMaterials: MaterialOption[] = [
  { id: 'smooth-white', name: 'Smooth White', color: '#FFFFFF', texture: 'smooth', roughness: 0.3, metalness: 0.0 },
  { id: 'textured-white', name: 'Textured White', color: '#FAFAFA', texture: 'textured', roughness: 0.6, metalness: 0.0 },
  { id: 'wood-beams', name: 'Wood Beams', color: '#8B4513', texture: 'wood', roughness: 0.7, metalness: 0.0 },
  { id: 'coffered', name: 'Coffered Ceiling', color: '#F5F5F5', texture: 'coffered', roughness: 0.4, metalness: 0.0 }
];

const lightingOptions: LightingOption[] = [
  { id: 'natural', name: 'Natural Light', type: 'directional', intensity: 1.0, color: '#FFFFFF', position: [5, 10, 5] },
  { id: 'warm', name: 'Warm Ambient', type: 'ambient', intensity: 0.6, color: '#FFF8DC' },
  { id: 'cool', name: 'Cool White', type: 'ambient', intensity: 0.8, color: '#F0F8FF' },
  { id: 'dramatic', name: 'Dramatic Spot', type: 'spot', intensity: 1.5, color: '#FFFFFF', position: [0, 8, 0] }
];

export default function RealisticRoomBuilder() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [dimensions, setDimensions] = useState<RoomDimensions>({ length: 12, width: 10, height: 9 });
  const [selectedFloor, setSelectedFloor] = useState(floorMaterials[0]);
  const [selectedWall, setSelectedWall] = useState(wallMaterials[0]);
  const [selectedCeiling, setSelectedCeiling] = useState(ceilingMaterials[0]);
  const [selectedLighting, setSelectedLighting] = useState(lightingOptions[0]);
  const [showDimensionInput, setShowDimensionInput] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [cameraAngle, setCameraAngle] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);

  useEffect(() => {
    generateRoom();
  }, [dimensions, selectedFloor, selectedWall, selectedCeiling, selectedLighting]);

  const generateRoom = async () => {
    setIsGenerating(true);

    // Simulate generation time for realistic effect
    await new Promise(resolve => setTimeout(resolve, 500));

    if (canvasRef.current) {
      renderRealisticRoom();
    }

    setIsGenerating(false);
  };

  const renderRealisticRoom = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Calculate room perspective based on dimensions
    const scale = Math.min(canvas.width / 20, canvas.height / 15);
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    // Apply camera rotation
    const rotatedDimensions = applyPerspective(dimensions, cameraAngle);

    // Draw realistic room with proper lighting and shadows
    drawRealisticRoom(ctx, centerX, centerY, scale, rotatedDimensions);
  };

  const applyPerspective = (dims: RoomDimensions, angle: { x: number, y: number }) => {
    // Apply basic perspective transformation
    const cosY = Math.cos(angle.y * 0.01);
    const sinY = Math.sin(angle.y * 0.01);

    return {
      length: dims.length * cosY,
      width: dims.width,
      height: dims.height,
      depth: dims.length * sinY
    };
  };

  const drawRealisticRoom = (ctx: CanvasRenderingContext2D, centerX: number, centerY: number, scale: number, dims: any) => {
    const roomWidth = dims.width * scale;
    const roomHeight = dims.height * scale;
    const roomDepth = dims.depth * scale;

    // Draw floor with realistic material
    drawFloor(ctx, centerX, centerY, roomWidth, roomHeight, roomDepth);

    // Draw walls with realistic materials and lighting
    drawWalls(ctx, centerX, centerY, roomWidth, roomHeight, roomDepth);

    // Draw ceiling with selected material
    drawCeiling(ctx, centerX, centerY, roomWidth, roomHeight, roomDepth);

    // Apply realistic lighting effects
    applyLighting(ctx, centerX, centerY, roomWidth, roomHeight);

    // Add room details and furniture
    addRoomDetails(ctx, centerX, centerY, roomWidth, roomHeight, scale);
  };

  const drawFloor = (ctx: CanvasRenderingContext2D, centerX: number, centerY: number, width: number, height: number, depth: number) => {
    // Create realistic floor gradient based on material
    const floorGradient = ctx.createLinearGradient(
      centerX - width/2, centerY + height/3,
      centerX + width/2, centerY + height/2
    );

    floorGradient.addColorStop(0, selectedFloor.color);
    floorGradient.addColorStop(0.5, adjustBrightness(selectedFloor.color, -15));
    floorGradient.addColorStop(1, adjustBrightness(selectedFloor.color, -30));

    ctx.fillStyle = floorGradient;

    // Draw floor with perspective
    ctx.beginPath();
    ctx.moveTo(centerX - width/2, centerY + height/3);
    ctx.lineTo(centerX + width/2, centerY + height/3);
    ctx.lineTo(centerX + width/2 - depth/2, centerY + height/2);
    ctx.lineTo(centerX - width/2 - depth/2, centerY + height/2);
    ctx.closePath();
    ctx.fill();

    // Add material texture pattern
    addFloorTexture(ctx, centerX, centerY, width, height, depth);
  };

  const drawWalls = (ctx: CanvasRenderingContext2D, centerX: number, centerY: number, width: number, height: number, depth: number) => {
    // Back wall
    const backWallGradient = ctx.createLinearGradient(
      centerX - width/2 - depth/2, centerY - height/3,
      centerX + width/2 - depth/2, centerY + height/3
    );
    backWallGradient.addColorStop(0, adjustBrightness(selectedWall.color, -10));
    backWallGradient.addColorStop(0.5, selectedWall.color);
    backWallGradient.addColorStop(1, adjustBrightness(selectedWall.color, -10));

    ctx.fillStyle = backWallGradient;
    ctx.fillRect(centerX - width/2 - depth/2, centerY - height/3, width, height * 2/3);

    // Left wall
    const leftWallGradient = ctx.createLinearGradient(
      centerX - width/2, centerY - height/3,
      centerX - width/2 - depth/2, centerY - height/3
    );
    leftWallGradient.addColorStop(0, adjustBrightness(selectedWall.color, -20));
    leftWallGradient.addColorStop(1, adjustBrightness(selectedWall.color, -35));

    ctx.fillStyle = leftWallGradient;
    ctx.beginPath();
    ctx.moveTo(centerX - width/2, centerY - height/3);
    ctx.lineTo(centerX - width/2 - depth/2, centerY - height/3);
    ctx.lineTo(centerX - width/2 - depth/2, centerY + height/3);
    ctx.lineTo(centerX - width/2, centerY + height/3);
    ctx.closePath();
    ctx.fill();

    // Right wall
    const rightWallGradient = ctx.createLinearGradient(
      centerX + width/2, centerY - height/3,
      centerX + width/2 - depth/2, centerY - height/3
    );
    rightWallGradient.addColorStop(0, adjustBrightness(selectedWall.color, -15));
    rightWallGradient.addColorStop(1, adjustBrightness(selectedWall.color, -25));

    ctx.fillStyle = rightWallGradient;
    ctx.beginPath();
    ctx.moveTo(centerX + width/2, centerY - height/3);
    ctx.lineTo(centerX + width/2 - depth/2, centerY - height/3);
    ctx.lineTo(centerX + width/2 - depth/2, centerY + height/3);
    ctx.lineTo(centerX + width/2, centerY + height/3);
    ctx.closePath();
    ctx.fill();
  };

  const drawCeiling = (ctx: CanvasRenderingContext2D, centerX: number, centerY: number, width: number, height: number, depth: number) => {
    const ceilingGradient = ctx.createLinearGradient(
      centerX - width/2, centerY - height/3,
      centerX + width/2, centerY - height/3
    );
    ceilingGradient.addColorStop(0, adjustBrightness(selectedCeiling.color, -5));
    ceilingGradient.addColorStop(0.5, selectedCeiling.color);
    ceilingGradient.addColorStop(1, adjustBrightness(selectedCeiling.color, -5));

    ctx.fillStyle = ceilingGradient;
    ctx.beginPath();
    ctx.moveTo(centerX - width/2, centerY - height/3);
    ctx.lineTo(centerX + width/2, centerY - height/3);
    ctx.lineTo(centerX + width/2 - depth/2, centerY - height/3 - depth/4);
    ctx.lineTo(centerX - width/2 - depth/2, centerY - height/3 - depth/4);
    ctx.closePath();
    ctx.fill();
  };

  const addFloorTexture = (ctx: CanvasRenderingContext2D, centerX: number, centerY: number, width: number, height: number, depth: number) => {
    ctx.strokeStyle = adjustBrightness(selectedFloor.color, -40);
    ctx.lineWidth = 1;

    if (selectedFloor.texture === 'wood') {
      // Wood plank pattern
      for (let i = 0; i < 12; i++) {
        const x = centerX - width/2 + (width / 12) * i;
        ctx.beginPath();
        ctx.moveTo(x, centerY + height/3);
        ctx.lineTo(x - depth/24, centerY + height/2);
        ctx.stroke();
      }
    } else if (selectedFloor.texture === 'tile' || selectedFloor.texture === 'marble') {
      // Tile grid pattern
      for (let i = 0; i < 10; i++) {
        const x = centerX - width/2 + (width / 10) * i;
        ctx.beginPath();
        ctx.moveTo(x, centerY + height/3);
        ctx.lineTo(x - depth/20, centerY + height/2);
        ctx.stroke();
      }
      for (let i = 0; i < 8; i++) {
        const y = centerY + height/3 + (height/6 / 8) * i;
        ctx.beginPath();
        ctx.moveTo(centerX - width/2, y);
        ctx.lineTo(centerX + width/2 - depth/2, y + depth/16);
        ctx.stroke();
      }
    }
  };

  const applyLighting = (ctx: CanvasRenderingContext2D, centerX: number, centerY: number, width: number, height: number) => {
    // Apply lighting based on selected lighting option
    const lightingGradient = ctx.createRadialGradient(
      centerX, centerY - height/4, 0,
      centerX, centerY, Math.max(width, height)
    );

    const lightColor = selectedLighting.color;
    const intensity = selectedLighting.intensity;

    lightingGradient.addColorStop(0, `rgba(255, 255, 255, ${0.1 * intensity})`);
    lightingGradient.addColorStop(0.5, `rgba(255, 255, 255, ${0.05 * intensity})`);
    lightingGradient.addColorStop(1, `rgba(0, 0, 0, ${0.1})`);

    ctx.fillStyle = lightingGradient;
    ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);
  };

  const addRoomDetails = (ctx: CanvasRenderingContext2D, centerX: number, centerY: number, width: number, height: number, scale: number) => {
    // Add basic furniture based on room size
    const furnitureScale = scale * 0.8;

    // Add a simple table
    ctx.fillStyle = '#8B4513';
    ctx.fillRect(centerX - 30, centerY + 20, 60, 40);

    // Add chairs
    ctx.fillStyle = '#654321';
    ctx.fillRect(centerX - 40, centerY + 10, 20, 20);
    ctx.fillRect(centerX + 20, centerY + 10, 20, 20);

    // Add room dimensions text
    ctx.fillStyle = '#333';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`${dimensions.length}' × ${dimensions.width}' × ${dimensions.height}'`, centerX, centerY - height/2 - 20);
  };

  const adjustBrightness = (color: string, amount: number): string => {
    const hex = color.replace('#', '');
    const r = Math.max(0, Math.min(255, parseInt(hex.substr(0, 2), 16) + amount));
    const g = Math.max(0, Math.min(255, parseInt(hex.substr(2, 2), 16) + amount));
    const b = Math.max(0, Math.min(255, parseInt(hex.substr(4, 2), 16) + amount));

    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  };

  const handleDimensionChange = (field: keyof RoomDimensions, value: number) => {
    setDimensions(prev => ({ ...prev, [field]: value }));
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setCameraAngle(prev => ({
        x: prev.x + e.movementY * 0.5,
        y: prev.y + e.movementX * 0.5
      }));
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h1>🏗️ Realistic Room Builder</h1>
        <p>Design your perfect room with custom dimensions and realistic materials</p>
      </div>

      {/* Controls Panel */}
      <div className={styles.controlsPanel}>
        {/* Dimension Controls */}
        <div className={styles.dimensionControls}>
          <h3>📏 Room Dimensions</h3>
          <div className={styles.dimensionInputs}>
            <div className={styles.inputGroup}>
              <label>Length (ft)</label>
              <input
                type="number"
                value={dimensions.length}
                onChange={(e) => handleDimensionChange('length', Number(e.target.value))}
                min="6"
                max="30"
              />
            </div>
            <div className={styles.inputGroup}>
              <label>Width (ft)</label>
              <input
                type="number"
                value={dimensions.width}
                onChange={(e) => handleDimensionChange('width', Number(e.target.value))}
                min="6"
                max="30"
              />
            </div>
            <div className={styles.inputGroup}>
              <label>Height (ft)</label>
              <input
                type="number"
                value={dimensions.height}
                onChange={(e) => handleDimensionChange('height', Number(e.target.value))}
                min="7"
                max="15"
              />
            </div>
          </div>
        </div>

        {/* Material Controls */}
        <div className={styles.materialControls}>
          <div className={styles.materialSection}>
            <h4>🏢 Floor Materials</h4>
            <div className={styles.materialGrid}>
              {floorMaterials.map(material => (
                <button
                  key={material.id}
                  className={`${styles.materialBtn} ${selectedFloor.id === material.id ? styles.selected : ''}`}
                  onClick={() => setSelectedFloor(material)}
                >
                  <div
                    className={styles.materialPreview}
                    style={{ backgroundColor: material.color }}
                  ></div>
                  <span>{material.name}</span>
                </button>
              ))}
            </div>
          </div>

          <div className={styles.materialSection}>
            <h4>🧱 Wall Materials</h4>
            <div className={styles.materialGrid}>
              {wallMaterials.map(material => (
                <button
                  key={material.id}
                  className={`${styles.materialBtn} ${selectedWall.id === material.id ? styles.selected : ''}`}
                  onClick={() => setSelectedWall(material)}
                >
                  <div
                    className={styles.materialPreview}
                    style={{ backgroundColor: material.color }}
                  ></div>
                  <span>{material.name}</span>
                </button>
              ))}
            </div>
          </div>

          <div className={styles.materialSection}>
            <h4>🏠 Ceiling Materials</h4>
            <div className={styles.materialGrid}>
              {ceilingMaterials.map(material => (
                <button
                  key={material.id}
                  className={`${styles.materialBtn} ${selectedCeiling.id === material.id ? styles.selected : ''}`}
                  onClick={() => setSelectedCeiling(material)}
                >
                  <div
                    className={styles.materialPreview}
                    style={{ backgroundColor: material.color }}
                  ></div>
                  <span>{material.name}</span>
                </button>
              ))}
            </div>
          </div>

          <div className={styles.materialSection}>
            <h4>💡 Lighting Options</h4>
            <div className={styles.materialGrid}>
              {lightingOptions.map(lighting => (
                <button
                  key={lighting.id}
                  className={`${styles.materialBtn} ${selectedLighting.id === lighting.id ? styles.selected : ''}`}
                  onClick={() => setSelectedLighting(lighting)}
                >
                  <div
                    className={styles.materialPreview}
                    style={{ backgroundColor: lighting.color }}
                  ></div>
                  <span>{lighting.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 3D Viewer */}
      <div className={styles.viewerContainer}>
        <canvas
          ref={canvasRef}
          className={styles.canvas}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        />

        {isGenerating && (
          <div className={styles.loadingOverlay}>
            <div className={styles.loadingSpinner}></div>
            <p>Generating realistic room...</p>
          </div>
        )}

        <div className={styles.viewerControls}>
          <span>🖱️ Click and drag to rotate view</span>
        </div>
      </div>
    </div>
  );
}