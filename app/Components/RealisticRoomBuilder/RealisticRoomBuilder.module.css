.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header {
  text-align: center;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.controlsPanel {
  display: flex;
  gap: 2rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  overflow-x: auto;
}

.dimensionControls {
  min-width: 300px;
}

.dimensionControls h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.dimensionInputs {
  display: flex;
  gap: 1rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.inputGroup label {
  font-size: 0.9rem;
  font-weight: 500;
  opacity: 0.9;
}

.inputGroup input {
  padding: 0.75rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  width: 80px;
  text-align: center;
}

.inputGroup input:focus {
  outline: none;
  border-color: #28a745;
  background: rgba(255, 255, 255, 0.2);
}

.materialControls {
  flex: 1;
  display: flex;
  gap: 2rem;
  overflow-x: auto;
}

.materialSection {
  min-width: 200px;
}

.materialSection h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.materialGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.materialBtn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  font-size: 0.8rem;
}

.materialBtn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.materialBtn.selected {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.3);
}

.materialPreview {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.viewerContainer {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  min-height: 500px;
}

.canvas {
  width: 100%;
  height: 100%;
  max-width: 1000px;
  max-height: 600px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: grab;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.canvas:active {
  cursor: grabbing;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.loadingSpinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingOverlay p {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.viewerControls {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.viewerControls span {
  font-size: 0.9rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .controlsPanel {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .materialControls {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .dimensionInputs {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .header h1 {
    font-size: 2rem;
  }
  
  .controlsPanel {
    padding: 1rem;
  }
  
  .dimensionControls {
    min-width: auto;
  }
  
  .dimensionInputs {
    flex-direction: column;
    align-items: center;
  }
  
  .inputGroup {
    width: 100%;
    max-width: 150px;
  }
  
  .inputGroup input {
    width: 100%;
  }
  
  .materialSection {
    min-width: auto;
  }
  
  .materialGrid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .viewerContainer {
    padding: 1rem;
    min-height: 400px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 1.5rem;
  }
  
  .materialGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .materialBtn {
    padding: 0.5rem;
    font-size: 0.7rem;
  }
  
  .materialPreview {
    width: 25px;
    height: 25px;
  }
  
  .viewerControls {
    padding: 0.5rem 1rem;
  }
  
  .viewerControls span {
    font-size: 0.8rem;
  }
}
