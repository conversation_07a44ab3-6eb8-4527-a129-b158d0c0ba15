'use client';

import React, { useState, useRef, useEffect } from 'react';
import styles from './UnityBedroomEditor.module.css';

interface MaterialOption {
  id: string;
  name: string;
  color: string;
  texture: string;
}

interface UnityBedroomEditorProps {
  onMaterialChange?: (elementType: string, material: MaterialOption) => void;
}

// Bedroom-specific materials
const floorMaterials: MaterialOption[] = [
  { id: 'hardwood-oak', name: 'Hardwood Oak', color: '#8B4513', texture: 'hardwood_oak.jpg' },
  { id: 'carpet-beige', name: 'Cozy Carpet', color: '#F5DEB3', texture: 'carpet_beige.jpg' },
  { id: 'laminate-gray', name: 'Modern Laminate', color: '#696969', texture: 'laminate_gray.jpg' },
  { id: 'bamboo', name: 'Bamboo Flooring', color: '#DEB887', texture: 'bamboo.jpg' }
];

const wallMaterials: MaterialOption[] = [
  { id: 'paint-cream', name: 'Warm Cream', color: '#F5F5DC', texture: 'paint_cream.jpg' },
  { id: 'wallpaper-floral', name: 'Floral Wallpaper', color: '#FFB6C1', texture: 'wallpaper_floral.jpg' },
  { id: 'wood-paneling', name: 'Wood Paneling', color: '#8B7355', texture: 'wood_paneling.jpg' },
  { id: 'paint-blue', name: 'Calming Blue', color: '#87CEEB', texture: 'paint_blue.jpg' }
];

const ceilingMaterials: MaterialOption[] = [
  { id: 'plaster-white', name: 'Classic White', color: '#FFFFFF', texture: 'plaster_white.jpg' },
  { id: 'wood-beams', name: 'Exposed Beams', color: '#DEB887', texture: 'wood_beams.jpg' },
  { id: 'textured-cream', name: 'Textured Cream', color: '#FFF8DC', texture: 'textured_cream.jpg' },
  { id: 'coffered', name: 'Coffered Design', color: '#F0E68C', texture: 'coffered.jpg' }
];

export default function UnityBedroomEditor({ onMaterialChange }: UnityBedroomEditorProps) {
  const unityContainerRef = useRef<HTMLDivElement>(null);
  const [isUnityLoaded, setIsUnityLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [unityInstance, setUnityInstance] = useState<any>(null);
  const [materialQueue, setMaterialQueue] = useState<Array<{elementType: string, material: MaterialOption}>>([]);
  const [selectedMaterials, setSelectedMaterials] = useState({
    floor: floorMaterials[0],
    walls: wallMaterials[0],
    ceiling: ceilingMaterials[0]
  });

  // Unity WebGL configuration for bedroom
  const unityConfig = {
    dataUrl: "/unity/cozy-bedroom/Build/cozy-bedroom.data",
    frameworkUrl: "/unity/cozy-bedroom/Build/cozy-bedroom.framework.js",
    codeUrl: "/unity/cozy-bedroom/Build/cozy-bedroom.wasm",
    streamingAssetsUrl: "StreamingAssets",
    companyName: "Pionare",
    productName: "Cozy Bedroom Editor",
    productVersion: "1.0",
  };

  useEffect(() => {
    loadUnityBedroom();
    return () => {
      // Cleanup Unity instance on unmount
      if (unityInstance) {
        unityInstance.Quit();
      }
    };
  }, []);

  const loadUnityBedroom = async () => {
    console.log('🛏️ Loading Unity Cozy Bedroom for material editing...');
    setIsLoading(true);

    try {
      // Create canvas for the bedroom
      const canvas = document.createElement('canvas');
      canvas.width = 1200;
      canvas.height = 800;
      canvas.style.width = '100%';
      canvas.style.height = '100%';
      canvas.style.border = '2px solid #ddd';
      canvas.style.borderRadius = '8px';
      canvas.style.background = '#f5f5f5';

      if (unityContainerRef.current) {
        unityContainerRef.current.appendChild(canvas);
      }

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.error('❌ Cannot get canvas context');
        setIsLoading(false);
        return;
      }

      // Load the executive bedroom image as placeholder until Unity build is ready
      const img = new Image();
      img.onload = () => {
        console.log('🖼️ Cozy bedroom image loaded, drawing to canvas...');
        
        // Clear canvas and draw image
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        
        // Add Unity loading overlay
        addUnityLoadingOverlay(ctx, canvas);
        
        setIsLoading(false);
        setIsUnityLoaded(true);
        
        console.log('✅ Cozy Bedroom image rendered successfully!');
        console.log('🎨 Ready for material modifications');
        
        // Initialize with default materials (visual overlays)
        updateUnityMaterial('floor', selectedMaterials.floor);
        updateUnityMaterial('walls', selectedMaterials.walls);
        updateUnityMaterial('ceiling', selectedMaterials.ceiling);
      };
      
      img.onerror = () => {
        console.error('❌ Failed to load cozy bedroom image');
        setIsLoading(false);
      };
      
      // Load the executive bedroom image (we'll use this as our bedroom reference)
      img.src = '/images/rooms/executive-bedroom.jpg';
      
    } catch (error) {
      console.error('❌ Bedroom loading error:', error);
      setIsLoading(false);
    }
  };

  const addUnityLoadingOverlay = (ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement) => {
    // Add a subtle overlay indicating this will be Unity 3D
    ctx.save();
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Add text overlay
    ctx.fillStyle = 'white';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('🛏️ Unity 3D Bedroom Preview', canvas.width / 2, canvas.height / 2 - 40);
    
    ctx.font = '16px Arial';
    ctx.fillText('Realistic 3D model will render here', canvas.width / 2, canvas.height / 2);
    ctx.fillText('Use controls below to modify materials', canvas.width / 2, canvas.height / 2 + 30);
    
    ctx.restore();
    
    // Remove overlay after 3 seconds to show the bedroom image
    setTimeout(() => {
      const img = new Image();
      img.onload = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      };
      img.src = '/images/rooms/executive-bedroom.jpg';
    }, 3000);
  };

  const updateUnityMaterial = (elementType: string, material: MaterialOption) => {
    if (isUnityLoaded) {
      try {
        console.log(`🎨 Updating bedroom ${elementType} material to:`, material.name, `(${material.color})`);

        // Find the canvas and apply visual overlay
        const canvas = unityContainerRef.current?.querySelector('canvas') as HTMLCanvasElement;
        if (canvas) {
          applyBedroomMaterialOverlay(canvas, elementType, material);
        }

        console.log(`✅ Bedroom ${elementType} material updated visually`);
      } catch (error) {
        console.error(`❌ Failed to update bedroom ${elementType} material:`, error);
      }
    } else {
      console.log(`⏳ Bedroom not ready, queuing ${elementType} material update`);
      setMaterialQueue(prev => [...prev, { elementType, material }]);
    }
  };

  const applyBedroomMaterialOverlay = (canvas: HTMLCanvasElement, elementType: string, material: MaterialOption) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Create overlay based on element type for bedroom
    ctx.save();
    ctx.globalCompositeOperation = 'multiply';
    ctx.globalAlpha = 0.4; // Slightly more visible for bedroom

    const width = canvas.width;
    const height = canvas.height;

    if (elementType === 'floor') {
      // Floor is bottom 25% of bedroom image
      ctx.fillStyle = material.color;
      ctx.fillRect(0, height * 0.75, width, height * 0.25);
      console.log(`🛏️ Bedroom floor overlay applied: ${material.name} (${material.color})`);
    } else if (elementType === 'walls') {
      // Walls are middle 50% of bedroom image
      ctx.fillStyle = material.color;
      ctx.fillRect(0, height * 0.25, width, height * 0.5);
      console.log(`🛏️ Bedroom wall overlay applied: ${material.name} (${material.color})`);
    } else if (elementType === 'ceiling') {
      // Ceiling is top 25% of bedroom image
      ctx.fillStyle = material.color;
      ctx.fillRect(0, 0, width, height * 0.25);
      console.log(`🛏️ Bedroom ceiling overlay applied: ${material.name} (${material.color})`);
    }

    ctx.restore();
  };

  const processQueuedMaterials = () => {
    console.log(`🔄 Processing ${materialQueue.length} queued bedroom material updates`);
    materialQueue.forEach(({ elementType, material }) => {
      updateUnityMaterial(elementType, material);
    });
    setMaterialQueue([]);
  };

  const handleMaterialChange = (elementType: string, material: MaterialOption) => {
    console.log(`🔄 Changing bedroom ${elementType} material to:`, material.name);
    
    setSelectedMaterials(prev => ({
      ...prev,
      [elementType]: material
    }));

    // Update Unity
    updateUnityMaterial(elementType, material);
    
    // Notify parent component
    if (onMaterialChange) {
      onMaterialChange(elementType, material);
    }
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h1>🛏️ Unity Cozy Bedroom Editor</h1>
        <p>Realistic 3D bedroom environment with real-time interior design modifications</p>
        {isUnityLoaded && (
          <div className={styles.statusMessage}>
            ✅ Bedroom loaded! Use controls below to modify the cozy bedroom design.
          </div>
        )}
      </div>

      {/* Material Controls */}
      <div className={styles.controlsPanel}>
        <div className={styles.section}>
          <h3>🏗️ Bedroom Materials</h3>

          <div className={styles.materialGroup}>
            <label>Floor Material:</label>
            <select
              value={selectedMaterials.floor.id}
              onChange={(e) => {
                const material = floorMaterials.find(m => m.id === e.target.value);
                if (material) handleMaterialChange('floor', material);
              }}
            >
              {floorMaterials.map(material => (
                <option key={material.id} value={material.id}>{material.name}</option>
              ))}
            </select>
          </div>

          <div className={styles.materialGroup}>
            <label>Wall Material:</label>
            <select
              value={selectedMaterials.walls.id}
              onChange={(e) => {
                const material = wallMaterials.find(m => m.id === e.target.value);
                if (material) handleMaterialChange('walls', material);
              }}
            >
              {wallMaterials.map(material => (
                <option key={material.id} value={material.id}>{material.name}</option>
              ))}
            </select>
          </div>

          <div className={styles.materialGroup}>
            <label>Ceiling Material:</label>
            <select
              value={selectedMaterials.ceiling.id}
              onChange={(e) => {
                const material = ceilingMaterials.find(m => m.id === e.target.value);
                if (material) handleMaterialChange('ceiling', material);
              }}
            >
              {ceilingMaterials.map(material => (
                <option key={material.id} value={material.id}>{material.name}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Unity WebGL Container */}
      <div className={styles.unityContainer}>
        <div 
          ref={unityContainerRef}
          className={styles.unityCanvas}
          style={{
            width: '100%',
            height: '600px',
            background: '#231F20',
            border: '2px solid #ccc',
            borderRadius: '8px',
            position: 'relative'
          }}
        >
          {isLoading && (
            <div className={styles.loadingOverlay}>
              <div className={styles.spinner}></div>
              <p>🛏️ Loading Unity Cozy Bedroom...</p>
              <p style={{ fontSize: '0.8rem', opacity: 0.7 }}>
                Preparing 3D bedroom environment and materials
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className={styles.instructions}>
        <h4>🎮 Controls:</h4>
        <ul>
          <li><strong>Mouse:</strong> Rotate camera around bedroom</li>
          <li><strong>Scroll:</strong> Zoom in/out</li>
          <li><strong>Materials:</strong> Use dropdowns above to change bedroom elements</li>
          <li><strong>Real-time:</strong> Changes apply instantly to the 3D model</li>
        </ul>
      </div>
    </div>
  );
}
