/* Unity Bedroom Editor Styles */
.container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

/* Header */
.header {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header h1 {
  margin: 0 0 1rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.statusMessage {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.5);
  border-radius: 8px;
  font-weight: 500;
}

/* Controls Panel */
.controlsPanel {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.section {
  margin-bottom: 2rem;
}

.section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #FFD700;
  border-bottom: 2px solid rgba(255, 215, 0, 0.3);
  padding-bottom: 0.5rem;
}

.materialGroup {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.materialGroup label {
  min-width: 120px;
  font-weight: 500;
  color: #FFD700;
}

.materialGroup select {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.materialGroup select:focus {
  outline: none;
  border-color: #FFD700;
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3);
}

.materialGroup select option {
  background: #2c3e50;
  color: white;
  padding: 0.5rem;
}

/* Unity Container */
.unityContainer {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.unityCanvas {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Loading Overlay */
.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  z-index: 10;
  backdrop-filter: blur(5px);
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 215, 0, 0.3);
  border-top: 4px solid #FFD700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingOverlay p {
  margin: 0.5rem 0;
  text-align: center;
  font-weight: 500;
}

/* Instructions */
.instructions {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.instructions h4 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  color: #FFD700;
  font-weight: 600;
}

.instructions ul {
  margin: 0;
  padding-left: 1.5rem;
  line-height: 1.8;
}

.instructions li {
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.instructions strong {
  color: #FFD700;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 1rem;
    gap: 1.5rem;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .materialGroup {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .materialGroup label {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .header h1 {
    font-size: 1.8rem;
  }
  
  .header p {
    font-size: 1rem;
  }
  
  .controlsPanel,
  .unityContainer,
  .instructions {
    padding: 1rem;
  }
  
  .unityCanvas {
    height: 400px !important;
  }
}

@media (max-width: 480px) {
  .header h1 {
    font-size: 1.5rem;
  }
  
  .header p {
    font-size: 0.9rem;
  }
  
  .unityCanvas {
    height: 300px !important;
  }
  
  .materialGroup select {
    font-size: 0.9rem;
    padding: 0.5rem;
  }
}
