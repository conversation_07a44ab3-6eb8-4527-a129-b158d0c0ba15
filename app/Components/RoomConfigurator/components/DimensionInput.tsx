'use client';

import React, { useState, useCallback } from 'react';
import { FloorPlan, RoomDimensions } from '../types';
import styles from './DimensionInput.module.css';

interface DimensionInputProps {
  floorPlan: FloorPlan | null;
  selectedRoom: string | null;
  onCreateRoom: (dimensions: Omit<RoomDimensions, 'id'>) => void;
  onUpdateRoom: (roomId: string, updates: Partial<RoomDimensions>) => void;
  onDeleteRoom: (roomId: string) => void;
  onSelectRoom: (roomId: string | null) => void;
}

export default function DimensionInput({
  floorPlan,
  selectedRoom,
  onCreateRoom,
  onUpdateRoom,
  onDeleteRoom,
  onSelectRoom
}: DimensionInputProps) {
  const [newRoomForm, setNewRoomForm] = useState({
    name: '',
    length: 4,
    width: 3,
    height: 2.7,
    shape: 'rectangle' as const
  });

  const [isCreating, setIsCreating] = useState(false);

  const selectedRoomData = floorPlan?.rooms.find(room => room.id === selectedRoom);

  const handleCreateRoom = useCallback(() => {
    if (!newRoomForm.name.trim()) {
      alert('Please enter a room name');
      return;
    }

    onCreateRoom({
      name: newRoomForm.name,
      type: 'room',
      version: '1.0.0',
      length: newRoomForm.length,
      width: newRoomForm.width,
      height: newRoomForm.height,
      shape: newRoomForm.shape,
      position: { x: 0, y: 0, z: 0 }
    });

    // Reset form
    setNewRoomForm({
      name: '',
      length: 4,
      width: 3,
      height: 2.7,
      shape: 'rectangle'
    });
    setIsCreating(false);
  }, [newRoomForm, onCreateRoom]);

  const handleUpdateRoom = useCallback((field: keyof RoomDimensions, value: any) => {
    if (!selectedRoom) return;
    onUpdateRoom(selectedRoom, { [field]: value });
  }, [selectedRoom, onUpdateRoom]);

  const calculateArea = (room: RoomDimensions) => {
    return (room.length * room.width).toFixed(2);
  };

  const calculateVolume = (room: RoomDimensions) => {
    return (room.length * room.width * room.height).toFixed(2);
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>Room Dimensions</h2>
        <button
          className={styles.addButton}
          onClick={() => setIsCreating(!isCreating)}
        >
          {isCreating ? 'Cancel' : '+ Add Room'}
        </button>
      </div>

      {/* Create New Room Form */}
      {isCreating && (
        <div className={styles.createForm}>
          <h3>Create New Room</h3>

          <div className={styles.formGroup}>
            <label>Room Name</label>
            <input
              type="text"
              value={newRoomForm.name}
              onChange={(e) => setNewRoomForm(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., Living Room"
              className={styles.input}
            />
          </div>

          <div className={styles.formGroup}>
            <label>Shape</label>
            <select
              value={newRoomForm.shape}
              onChange={(e) => setNewRoomForm(prev => ({ ...prev, shape: e.target.value as any }))}
              className={styles.select}
            >
              <option value="rectangle">Rectangle</option>
              <option value="square">Square</option>
              <option value="l-shape">L-Shape</option>
              <option value="custom">Custom</option>
            </select>
          </div>

          <div className={styles.dimensionGrid}>
            <div className={styles.formGroup}>
              <label>Length (m)</label>
              <input
                type="number"
                value={newRoomForm.length}
                onChange={(e) => setNewRoomForm(prev => ({ ...prev, length: parseFloat(e.target.value) || 0 }))}
                min="0.1"
                step="0.1"
                className={styles.input}
              />
            </div>

            <div className={styles.formGroup}>
              <label>Width (m)</label>
              <input
                type="number"
                value={newRoomForm.width}
                onChange={(e) => setNewRoomForm(prev => ({ ...prev, width: parseFloat(e.target.value) || 0 }))}
                min="0.1"
                step="0.1"
                className={styles.input}
              />
            </div>

            <div className={styles.formGroup}>
              <label>Height (m)</label>
              <input
                type="number"
                value={newRoomForm.height}
                onChange={(e) => setNewRoomForm(prev => ({ ...prev, height: parseFloat(e.target.value) || 0 }))}
                min="0.1"
                step="0.1"
                className={styles.input}
              />
            </div>
          </div>

          <div className={styles.formActions}>
            <button onClick={handleCreateRoom} className={styles.createButton}>
              Create Room
            </button>
          </div>
        </div>
      )}

      {/* Room List */}
      <div className={styles.roomList}>
        <h3>Rooms ({floorPlan?.rooms.length || 0})</h3>

        {floorPlan?.rooms.map((room) => (
          <div
            key={room.id}
            className={`${styles.roomItem} ${selectedRoom === room.id ? styles.selected : ''}`}
            onClick={() => onSelectRoom(room.id)}
          >
            <div className={styles.roomHeader}>
              <h4>{room.name}</h4>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteRoom(room.id);
                }}
                className={styles.deleteButton}
              >
                ×
              </button>
            </div>

            <div className={styles.roomInfo}>
              <span>Area: {calculateArea(room)} m²</span>
              <span>Volume: {calculateVolume(room)} m³</span>
            </div>

            <div className={styles.roomDimensions}>
              <span>{room.length}m × {room.width}m × {room.height}m</span>
            </div>
          </div>
        ))}

        {(!floorPlan?.rooms || floorPlan.rooms.length === 0) && (
          <div className={styles.emptyState}>
            <p>No rooms created yet</p>
            <p>Click "Add Room" to get started</p>
          </div>
        )}
      </div>

      {/* Selected Room Editor */}
      {selectedRoomData && (
        <div className={styles.roomEditor}>
          <h3>Edit: {selectedRoomData.name}</h3>

          <div className={styles.formGroup}>
            <label>Room Name</label>
            <input
              type="text"
              value={selectedRoomData.name}
              onChange={(e) => handleUpdateRoom('name', e.target.value)}
              className={styles.input}
            />
          </div>

          <div className={styles.formGroup}>
            <label>Shape</label>
            <select
              value={selectedRoomData.shape}
              onChange={(e) => handleUpdateRoom('shape', e.target.value)}
              className={styles.select}
            >
              <option value="rectangle">Rectangle</option>
              <option value="square">Square</option>
              <option value="l-shape">L-Shape</option>
              <option value="custom">Custom</option>
            </select>
          </div>

          <div className={styles.dimensionGrid}>
            <div className={styles.formGroup}>
              <label>Length (m)</label>
              <input
                type="number"
                value={selectedRoomData.length}
                onChange={(e) => handleUpdateRoom('length', parseFloat(e.target.value) || 0)}
                min="0.1"
                step="0.1"
                className={styles.input}
              />
            </div>

            <div className={styles.formGroup}>
              <label>Width (m)</label>
              <input
                type="number"
                value={selectedRoomData.width}
                onChange={(e) => handleUpdateRoom('width', parseFloat(e.target.value) || 0)}
                min="0.1"
                step="0.1"
                className={styles.input}
              />
            </div>

            <div className={styles.formGroup}>
              <label>Height (m)</label>
              <input
                type="number"
                value={selectedRoomData.height}
                onChange={(e) => handleUpdateRoom('height', parseFloat(e.target.value) || 0)}
                min="0.1"
                step="0.1"
                className={styles.input}
              />
            </div>
          </div>

          <div className={styles.calculations}>
            <div className={styles.calcItem}>
              <span>Floor Area:</span>
              <strong>{calculateArea(selectedRoomData)} m²</strong>
            </div>
            <div className={styles.calcItem}>
              <span>Volume:</span>
              <strong>{calculateVolume(selectedRoomData)} m³</strong>
            </div>
            <div className={styles.calcItem}>
              <span>Wall Area:</span>
              <strong>{(2 * (selectedRoomData.length + selectedRoomData.width) * selectedRoomData.height).toFixed(2)} m²</strong>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
