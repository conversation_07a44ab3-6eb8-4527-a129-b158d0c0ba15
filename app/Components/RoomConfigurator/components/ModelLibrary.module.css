.container {
  padding: 1.5rem;
  background: white;
  height: 100%;
  overflow-y: auto;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.headerLeft h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.modelCount {
  color: #6c757d;
  font-size: 0.9rem;
  background: #f8f9fa;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.viewToggle {
  display: flex;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.25rem;
}

.viewToggle button {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.viewToggle button.active {
  background: white;
  color: #667eea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.uploadButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.uploadButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
}

/* Controls */
.controls {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.searchBar {
  margin-bottom: 1rem;
}

.searchInput {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.filterSelect {
  padding: 0.5rem 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  background: white;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filterSelect:focus {
  outline: none;
  border-color: #667eea;
}

.filterToggles {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filterToggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.9rem;
  cursor: pointer;
}

.filterToggle input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #667eea;
}

/* Model Grid */
.modelGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1.5rem;
}

.modelGrid.listView {
  grid-template-columns: 1fr;
}

.modelCard {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.modelCard:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.modelCard.selected {
  border-color: #28a745;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.modelThumbnail {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.listView .modelCard {
  display: flex;
  align-items: center;
  padding: 1rem;
}

.listView .modelThumbnail {
  width: 120px;
  height: 90px;
  flex-shrink: 0;
  border-radius: 8px;
  margin-right: 1rem;
}

.modelThumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modelOverlay {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.modelFormat {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
}

.modelBadge {
  background: #28a745;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
}

.modelInfo {
  padding: 1rem;
}

.listView .modelInfo {
  flex: 1;
  padding: 0;
}

.modelName {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.3;
}

.modelCategory {
  margin: 0 0 0.5rem 0;
  color: #6c757d;
  font-size: 0.85rem;
  text-transform: capitalize;
}

.modelDescription {
  margin: 0 0 0.75rem 0;
  color: #495057;
  font-size: 0.85rem;
  line-height: 1.4;
}

.modelMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
}

.modelSize {
  color: #6c757d;
}

.modelPolys {
  color: #6c757d;
}

.modelTags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.modelTag {
  background: #e9ecef;
  color: #495057;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 4rem 2rem;
  color: #6c757d;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
}

.emptyState h3 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.5rem;
}

.emptyState p {
  margin: 0 0 2rem 0;
  font-size: 1rem;
  line-height: 1.6;
}

/* Modal */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modalContent {
  background: white;
  border-radius: 16px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
}

.modalHeader h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.modalClose {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.modalClose:hover {
  background: #f8f9fa;
  color: #495057;
}

.modalBody {
  padding: 2rem;
}

.modalImage {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.modalDetails p {
  margin: 0.5rem 0;
  color: #495057;
  line-height: 1.6;
}

.modalDetails strong {
  color: #2c3e50;
}

.modalTags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.modalTag {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .headerRight {
    justify-content: space-between;
  }
  
  .filters {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .filterSelect {
    width: 100%;
  }
  
  .modelGrid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
  }
  
  .modalOverlay {
    padding: 1rem;
  }
  
  .modalBody {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.75rem;
  }
  
  .modelGrid {
    grid-template-columns: 1fr;
  }
  
  .modelCard {
    display: flex;
    align-items: center;
  }
  
  .modelThumbnail {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
    margin-right: 1rem;
    border-radius: 8px;
  }
  
  .modelInfo {
    flex: 1;
    padding: 0;
  }
  
  .filterToggles {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .emptyState {
    padding: 2rem 1rem;
  }
  
  .emptyIcon {
    font-size: 3rem;
  }
}

/* Focus States */
.uploadButton:focus,
.viewToggle button:focus,
.filterSelect:focus,
.modelCard:focus,
.modalClose:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .modelCard {
    border-color: #000;
  }
  
  .modelCard.selected {
    background: #e6f3ff;
    border-color: #0066cc;
  }
  
  .searchInput,
  .filterSelect {
    border-color: #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .modelCard,
  .uploadButton,
  .modalClose {
    transition: none;
  }
  
  .modelCard:hover,
  .uploadButton:hover {
    transform: none;
  }
}
