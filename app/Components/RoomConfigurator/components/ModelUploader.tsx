'use client';

import React, { useState, useCallback, useRef } from 'react';
import { ModelAsset, modelManager } from '../core/ModelManager';
import { Vector3D } from '../types';
import styles from './ModelUploader.module.css';

interface ModelUploaderProps {
  onModelUploaded?: (modelId: string) => void;
  onClose?: () => void;
}

export default function ModelUploader({ onModelUploaded, onClose }: ModelUploaderProps) {
  const [uploadState, setUploadState] = useState<'idle' | 'uploading' | 'processing' | 'success' | 'error'>('idle');
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedModelId, setUploadedModelId] = useState<string | null>(null);
  
  const [modelData, setModelData] = useState<Partial<ModelAsset>>({
    name: '',
    description: '',
    category: 'furniture',
    subcategory: '',
    tags: [],
    placement: {
      defaultSurface: 'floor',
      allowedSurfaces: ['floor'],
      snapToGrid: true,
      snapDistance: 0.1
    },
    defaultScale: { x: 1, y: 1, z: 1 },
    defaultRotation: { x: 0, y: 0, z: 0 },
    scaleConstraints: {
      min: { x: 0.1, y: 0.1, z: 0.1 },
      max: { x: 10, y: 10, z: 10 },
      uniform: false
    },
    interactive: false,
    configurable: false,
    animatable: false
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [additionalFiles, setAdditionalFiles] = useState<{
    textures: File[];
    materials: File[];
    animations: File[];
  }>({
    textures: [],
    materials: [],
    animations: []
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const textureInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const validExtensions = ['.gltf', '.glb', '.fbx', '.obj', '.dae', '.ply', '.stl'];
    const isValid = validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
    
    if (!isValid) {
      setError('Please select a valid 3D model file (.gltf, .glb, .fbx, .obj, .dae, .ply, .stl)');
      return;
    }

    setSelectedFile(file);
    setError(null);
    
    // Auto-fill name if empty
    if (!modelData.name) {
      const nameWithoutExt = file.name.replace(/\.[^/.]+$/, '');
      setModelData(prev => ({ ...prev, name: nameWithoutExt }));
    }
  }, [modelData.name]);

  const handleAdditionalFiles = useCallback((type: 'textures' | 'materials' | 'animations', files: FileList | null) => {
    if (!files) return;
    
    const fileArray = Array.from(files);
    setAdditionalFiles(prev => ({
      ...prev,
      [type]: [...prev[type], ...fileArray]
    }));
  }, []);

  const handleUpload = useCallback(async () => {
    if (!selectedFile) {
      setError('Please select a model file');
      return;
    }

    if (!modelData.name?.trim()) {
      setError('Please enter a model name');
      return;
    }

    setUploadState('uploading');
    setError(null);
    setUploadProgress(0);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      // Upload the model
      const modelId = await modelManager.uploadModel(selectedFile, {
        ...modelData,
        files: {
          model: '', // Will be set by uploadModel
          thumbnail: '', // Will be generated
          textures: additionalFiles.textures.map(f => f.name),
          materials: additionalFiles.materials.map(f => f.name),
          animations: additionalFiles.animations.map(f => f.name)
        }
      });

      clearInterval(progressInterval);
      setUploadProgress(100);
      setUploadState('success');
      setUploadedModelId(modelId);
      
      onModelUploaded?.(modelId);
    } catch (err) {
      setUploadState('error');
      setError(err instanceof Error ? err.message : 'Upload failed');
    }
  }, [selectedFile, modelData, additionalFiles, onModelUploaded]);

  const handleTagAdd = useCallback((tag: string) => {
    if (!tag.trim() || modelData.tags?.includes(tag)) return;
    
    setModelData(prev => ({
      ...prev,
      tags: [...(prev.tags || []), tag.trim()]
    }));
  }, [modelData.tags]);

  const handleTagRemove = useCallback((tagToRemove: string) => {
    setModelData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }));
  }, []);

  const resetForm = useCallback(() => {
    setSelectedFile(null);
    setAdditionalFiles({ textures: [], materials: [], animations: [] });
    setUploadState('idle');
    setError(null);
    setUploadProgress(0);
    setUploadedModelId(null);
    setModelData({
      name: '',
      description: '',
      category: 'furniture',
      subcategory: '',
      tags: [],
      placement: {
        defaultSurface: 'floor',
        allowedSurfaces: ['floor'],
        snapToGrid: true,
        snapDistance: 0.1
      },
      defaultScale: { x: 1, y: 1, z: 1 },
      defaultRotation: { x: 0, y: 0, z: 0 },
      scaleConstraints: {
        min: { x: 0.1, y: 0.1, z: 0.1 },
        max: { x: 10, y: 10, z: 10 },
        uniform: false
      },
      interactive: false,
      configurable: false,
      animatable: false
    });
  }, []);

  if (uploadState === 'success') {
    return (
      <div className={styles.container}>
        <div className={styles.success}>
          <div className={styles.successIcon}>✓</div>
          <h3>Model Uploaded Successfully!</h3>
          <p>Your model "{modelData.name}" has been added to the library.</p>
          <p>Model ID: {uploadedModelId}</p>
          <div className={styles.successActions}>
            <button onClick={resetForm} className={styles.button}>
              Upload Another
            </button>
            <button onClick={onClose} className={styles.buttonSecondary}>
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>Upload 3D Model</h2>
        {onClose && (
          <button onClick={onClose} className={styles.closeButton}>×</button>
        )}
      </div>

      {error && (
        <div className={styles.error}>
          {error}
        </div>
      )}

      {/* File Selection */}
      <div className={styles.section}>
        <h3>Model File</h3>
        <div className={styles.fileUpload}>
          <input
            ref={fileInputRef}
            type="file"
            accept=".gltf,.glb,.fbx,.obj,.dae,.ply,.stl"
            onChange={handleFileSelect}
            className={styles.fileInput}
          />
          <div className={styles.fileDropZone} onClick={() => fileInputRef.current?.click()}>
            {selectedFile ? (
              <div className={styles.selectedFile}>
                <span className={styles.fileName}>{selectedFile.name}</span>
                <span className={styles.fileSize}>
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </span>
              </div>
            ) : (
              <div className={styles.dropZoneContent}>
                <div className={styles.uploadIcon}>📁</div>
                <p>Click to select or drag & drop your 3D model</p>
                <p className={styles.supportedFormats}>
                  Supported: .gltf, .glb, .fbx, .obj, .dae, .ply, .stl
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Model Information */}
      <div className={styles.section}>
        <h3>Model Information</h3>
        
        <div className={styles.formGroup}>
          <label>Name *</label>
          <input
            type="text"
            value={modelData.name || ''}
            onChange={(e) => setModelData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Enter model name"
            className={styles.input}
          />
        </div>

        <div className={styles.formGroup}>
          <label>Description</label>
          <textarea
            value={modelData.description || ''}
            onChange={(e) => setModelData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Describe your model..."
            className={styles.textarea}
            rows={3}
          />
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label>Category</label>
            <select
              value={modelData.category || 'furniture'}
              onChange={(e) => setModelData(prev => ({ ...prev, category: e.target.value }))}
              className={styles.select}
            >
              <option value="furniture">Furniture</option>
              <option value="lighting">Lighting</option>
              <option value="fixtures">Fixtures</option>
              <option value="decorative">Decorative</option>
              <option value="appliances">Appliances</option>
              <option value="electronics">Electronics</option>
              <option value="plants">Plants</option>
              <option value="architectural">Architectural</option>
              <option value="custom">Custom</option>
            </select>
          </div>

          <div className={styles.formGroup}>
            <label>Subcategory</label>
            <input
              type="text"
              value={modelData.subcategory || ''}
              onChange={(e) => setModelData(prev => ({ ...prev, subcategory: e.target.value }))}
              placeholder="e.g., chairs, tables"
              className={styles.input}
            />
          </div>
        </div>

        {/* Tags */}
        <div className={styles.formGroup}>
          <label>Tags</label>
          <div className={styles.tagContainer}>
            {modelData.tags?.map((tag, index) => (
              <span key={index} className={styles.tag}>
                {tag}
                <button
                  type="button"
                  onClick={() => handleTagRemove(tag)}
                  className={styles.tagRemove}
                >
                  ×
                </button>
              </span>
            ))}
            <input
              type="text"
              placeholder="Add tag..."
              className={styles.tagInput}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleTagAdd(e.currentTarget.value);
                  e.currentTarget.value = '';
                }
              }}
            />
          </div>
        </div>
      </div>

      {/* Placement Settings */}
      <div className={styles.section}>
        <h3>Placement Settings</h3>
        
        <div className={styles.formGroup}>
          <label>Default Surface</label>
          <select
            value={modelData.placement?.defaultSurface || 'floor'}
            onChange={(e) => setModelData(prev => ({
              ...prev,
              placement: { ...prev.placement!, defaultSurface: e.target.value as any }
            }))}
            className={styles.select}
          >
            <option value="floor">Floor</option>
            <option value="ceiling">Ceiling</option>
            <option value="wall">Wall</option>
            <option value="corner">Corner</option>
            <option value="center">Center</option>
          </select>
        </div>

        <div className={styles.checkboxGroup}>
          <label className={styles.checkbox}>
            <input
              type="checkbox"
              checked={modelData.placement?.snapToGrid || false}
              onChange={(e) => setModelData(prev => ({
                ...prev,
                placement: { ...prev.placement!, snapToGrid: e.target.checked }
              }))}
            />
            Snap to Grid
          </label>

          <label className={styles.checkbox}>
            <input
              type="checkbox"
              checked={modelData.interactive || false}
              onChange={(e) => setModelData(prev => ({ ...prev, interactive: e.target.checked }))}
            />
            Interactive
          </label>

          <label className={styles.checkbox}>
            <input
              type="checkbox"
              checked={modelData.configurable || false}
              onChange={(e) => setModelData(prev => ({ ...prev, configurable: e.target.checked }))}
            />
            Configurable
          </label>

          <label className={styles.checkbox}>
            <input
              type="checkbox"
              checked={modelData.animatable || false}
              onChange={(e) => setModelData(prev => ({ ...prev, animatable: e.target.checked }))}
            />
            Animatable
          </label>
        </div>
      </div>

      {/* Upload Progress */}
      {uploadState === 'uploading' && (
        <div className={styles.progress}>
          <div className={styles.progressBar}>
            <div 
              className={styles.progressFill}
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
          <p>Uploading... {uploadProgress}%</p>
        </div>
      )}

      {/* Actions */}
      <div className={styles.actions}>
        <button
          onClick={handleUpload}
          disabled={!selectedFile || !modelData.name?.trim() || uploadState === 'uploading'}
          className={styles.uploadButton}
        >
          {uploadState === 'uploading' ? 'Uploading...' : 'Upload Model'}
        </button>
        
        {onClose && (
          <button onClick={onClose} className={styles.cancelButton}>
            Cancel
          </button>
        )}
      </div>
    </div>
  );
}
