.container {
  padding: 1.5rem;
  background: white;
  height: 100%;
  overflow-y: auto;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.addButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.addButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
}

/* Create Form */
.createForm {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.createForm h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Form Elements */
.formGroup {
  margin-bottom: 1rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: #495057;
  font-weight: 500;
  font-size: 0.9rem;
}

.input, .select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  background: white;
}

.input:focus, .select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input:hover, .select:hover {
  border-color: #ced4da;
}

.dimensionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.formActions {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.createButton {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.createButton:hover {
  background: #218838;
  transform: translateY(-1px);
}

/* Room List */
.roomList {
  margin-bottom: 1.5rem;
}

.roomList h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.roomItem {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.roomItem:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
  transform: translateY(-1px);
}

.roomItem.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.roomHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.roomHeader h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.deleteButton {
  background: #dc3545;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.deleteButton:hover {
  background: #c82333;
  transform: scale(1.1);
}

.roomInfo {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.roomInfo span {
  color: #6c757d;
  font-size: 0.85rem;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.roomDimensions {
  color: #495057;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #dee2e6;
}

.emptyState p {
  margin: 0.5rem 0;
}

/* Room Editor */
.roomEditor {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  animation: slideDown 0.3s ease;
}

.roomEditor h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.calculations {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 2px solid #dee2e6;
}

.calcItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
}

.calcItem:last-child {
  border-bottom: none;
}

.calcItem span {
  color: #6c757d;
  font-size: 0.9rem;
}

.calcItem strong {
  color: #2c3e50;
  font-weight: 600;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .addButton {
    width: 100%;
    text-align: center;
  }
  
  .dimensionGrid {
    grid-template-columns: 1fr;
  }
  
  .roomInfo {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .calcItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.75rem;
  }
  
  .createForm, .roomEditor {
    padding: 1rem;
  }
  
  .roomItem {
    padding: 0.75rem;
  }
  
  .header h2 {
    font-size: 1.25rem;
  }
  
  .createForm h3, .roomEditor h3 {
    font-size: 1.1rem;
  }
}

/* Focus States */
.addButton:focus,
.createButton:focus,
.deleteButton:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.roomItem:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .roomItem {
    border-color: #000;
  }
  
  .roomItem.selected {
    background: #e6f3ff;
    border-color: #0066cc;
  }
  
  .input, .select {
    border-color: #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .createForm,
  .roomEditor {
    animation: none;
  }
  
  .roomItem,
  .addButton,
  .createButton,
  .deleteButton {
    transition: none;
  }
}
