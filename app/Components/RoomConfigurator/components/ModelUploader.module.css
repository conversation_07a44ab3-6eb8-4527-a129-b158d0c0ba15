.container {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Error */
.error {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  margin: 1rem 2rem;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
}

/* Sections */
.section {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
}

.section:last-child {
  border-bottom: none;
}

.section h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

/* File Upload */
.fileUpload {
  position: relative;
}

.fileInput {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.fileDropZone {
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f8f9fa;
}

.fileDropZone:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.selectedFile {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.fileName {
  font-weight: 600;
  color: #2c3e50;
}

.fileSize {
  color: #6c757d;
  font-size: 0.9rem;
}

.dropZoneContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.uploadIcon {
  font-size: 3rem;
  color: #6c757d;
}

.dropZoneContent p {
  margin: 0;
  color: #495057;
}

.supportedFormats {
  font-size: 0.85rem;
  color: #6c757d;
}

/* Form Elements */
.formGroup {
  margin-bottom: 1rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: #495057;
  font-weight: 500;
  font-size: 0.9rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.input, .select, .textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  font-family: inherit;
}

.input:focus, .select:focus, .textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

/* Tags */
.tagContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
  padding: 0.5rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  min-height: 44px;
}

.tag {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.tagRemove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  padding: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tagRemove:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tagInput {
  border: none;
  outline: none;
  flex: 1;
  min-width: 100px;
  padding: 0.25rem;
  font-size: 0.9rem;
}

/* Checkboxes */
.checkboxGroup {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #667eea;
}

/* Progress */
.progress {
  padding: 1rem 2rem;
  background: #f8f9fa;
  text-align: center;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.progress p {
  margin: 0;
  color: #6c757d;
  font-weight: 500;
}

/* Actions */
.actions {
  padding: 1.5rem 2rem;
  background: #f8f9fa;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.uploadButton {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.uploadButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.uploadButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cancelButton {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancelButton:hover {
  background: #5a6268;
}

/* Success State */
.success {
  padding: 3rem 2rem;
  text-align: center;
}

.successIcon {
  width: 80px;
  height: 80px;
  background: #28a745;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  margin: 0 auto 1.5rem;
}

.success h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.success p {
  margin: 0.5rem 0;
  color: #6c757d;
}

.successActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.button {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.button:hover {
  background: #5a6fd8;
}

.buttonSecondary {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.buttonSecondary:hover {
  background: #5a6268;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    margin: 1rem;
    max-width: none;
  }
  
  .header,
  .section,
  .actions {
    padding: 1rem;
  }
  
  .formRow {
    grid-template-columns: 1fr;
  }
  
  .checkboxGroup {
    grid-template-columns: 1fr;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .uploadButton,
  .cancelButton {
    width: 100%;
  }
  
  .successActions {
    flex-direction: column;
    align-items: center;
  }
  
  .button,
  .buttonSecondary {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .header h2 {
    font-size: 1.25rem;
  }
  
  .section h3 {
    font-size: 1.1rem;
  }
  
  .fileDropZone {
    padding: 1.5rem;
  }
  
  .uploadIcon {
    font-size: 2rem;
  }
  
  .success {
    padding: 2rem 1rem;
  }
  
  .successIcon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}

/* Focus States */
.uploadButton:focus,
.cancelButton:focus,
.button:focus,
.buttonSecondary:focus,
.closeButton:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .fileDropZone {
    border-color: #000;
  }
  
  .input, .select, .textarea {
    border-color: #000;
  }
  
  .tagContainer {
    border-color: #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .uploadButton,
  .fileDropZone,
  .input,
  .select,
  .textarea {
    transition: none;
  }
  
  .uploadButton:hover {
    transform: none;
  }
}
