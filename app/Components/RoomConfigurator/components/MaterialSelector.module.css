.container {
  padding: 1.5rem;
  background: white;
  height: 100%;
  overflow-y: auto;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.viewToggle {
  display: flex;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.25rem;
}

.viewToggle button {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.viewToggle button.active {
  background: white;
  color: #667eea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Surface Selector */
.surfaceSelector {
  margin-bottom: 1.5rem;
}

.surfaceSelector h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.surfaceButtons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.surfaceButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.surfaceButton:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.15);
}

.surfaceButton.active {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  color: #667eea;
}

.surfaceIcon {
  font-size: 1.5rem;
}

/* Current Material */
.currentMaterial {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px solid #e9ecef;
}

.currentMaterial h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.currentMaterialCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.currentMaterialImage {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  border: 2px solid #e9ecef;
}

.currentMaterialInfo h4 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 600;
}

.currentMaterialInfo p {
  margin: 0;
  color: #6c757d;
  font-size: 0.8rem;
}

/* Controls */
.controls {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.searchBar {
  margin-bottom: 1rem;
}

.searchInput {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.priceFilter label {
  display: block;
  margin-bottom: 0.5rem;
  color: #495057;
  font-weight: 500;
  font-size: 0.9rem;
}

.priceSliders {
  display: flex;
  gap: 1rem;
}

.priceSlider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e9ecef;
  outline: none;
  cursor: pointer;
}

.priceSlider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #667eea;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.priceSlider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #667eea;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Materials Container */
.materialsContainer {
  flex: 1;
}

.materialsHeader {
  margin-bottom: 1rem;
}

.materialsHeader h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.materialsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.materialsGrid.listView {
  grid-template-columns: 1fr;
}

.materialCard {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.materialCard:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.materialCard.selected {
  border-color: #28a745;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.materialImage {
  position: relative;
  width: 100%;
  height: 120px;
  overflow: hidden;
}

.listView .materialImage {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
  border-radius: 8px;
  margin-right: 1rem;
}

.materialImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.selectedBadge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #28a745;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
}

.materialInfo {
  padding: 1rem;
}

.listView .materialCard {
  display: flex;
  align-items: center;
  padding: 1rem;
}

.listView .materialInfo {
  flex: 1;
  padding: 0;
}

.materialName {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1.3;
}

.materialCategory {
  margin: 0 0 0.5rem 0;
  color: #6c757d;
  font-size: 0.8rem;
  text-transform: capitalize;
}

.materialDescription {
  margin: 0 0 0.5rem 0;
  color: #495057;
  font-size: 0.8rem;
  line-height: 1.4;
}

.materialMeta {
  margin-bottom: 0.5rem;
}

.materialPrice {
  color: #28a745;
  font-weight: 600;
  font-size: 0.9rem;
}

.materialProperties {
  margin-top: 0.25rem;
}

.materialProperties span {
  color: #6c757d;
  font-size: 0.75rem;
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  margin-right: 0.5rem;
}

.materialTags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.materialTag {
  background: #e9ecef;
  color: #495057;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.emptyIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.emptyState h3 {
  margin: 0 0 0.5rem 0;
  color: #495057;
  font-size: 1.2rem;
}

.emptyState p {
  margin: 0;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .surfaceButtons {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .materialsGrid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .currentMaterialCard {
    flex-direction: column;
    text-align: center;
  }
  
  .priceSliders {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.75rem;
  }
  
  .surfaceButtons {
    grid-template-columns: 1fr;
  }
  
  .materialsGrid {
    grid-template-columns: 1fr;
  }
  
  .materialCard {
    display: flex;
    align-items: center;
  }
  
  .materialImage {
    width: 60px;
    height: 60px;
    flex-shrink: 0;
    margin-right: 1rem;
    border-radius: 8px;
  }
  
  .materialInfo {
    flex: 1;
    padding: 0;
  }
}

/* Focus States */
.surfaceButton:focus,
.materialCard:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.searchInput:focus {
  outline: none;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .materialCard {
    border-color: #000;
  }
  
  .materialCard.selected {
    background: #e6f3ff;
    border-color: #0066cc;
  }
  
  .surfaceButton {
    border-color: #000;
  }
  
  .surfaceButton.active {
    background: #e6f3ff;
    border-color: #0066cc;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .materialCard,
  .surfaceButton,
  .viewToggle button {
    transition: none;
  }
}
