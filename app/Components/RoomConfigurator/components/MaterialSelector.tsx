'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { MaterialCatalog, Material, RoomConfiguration } from '../types';
import styles from './MaterialSelector.module.css';

interface MaterialSelectorProps {
  catalog: MaterialCatalog;
  selectedRoom: string | null;
  selectedCategory: string | null;
  currentConfiguration: RoomConfiguration | undefined;
  onSelectCategory: (category: string | null) => void;
  onApplyMaterial: (roomId: string, surface: string, materialId: string) => void;
}

export default function MaterialSelector({
  catalog,
  selectedRoom,
  selectedCategory,
  currentConfiguration,
  onSelectCategory,
  onApplyMaterial
}: MaterialSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSurface, setSelectedSurface] = useState<string>('floor');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [priceFilter, setPriceFilter] = useState<{ min: number; max: number }>({ min: 0, max: 1000 });

  // Get available surfaces for the selected room
  const availableSurfaces = useMemo(() => [
    { id: 'floor', name: 'Floor', icon: '🏠' },
    { id: 'ceiling', name: 'Ceiling', icon: '⬆️' },
    { id: 'walls', name: 'Walls', icon: '🧱' },
    { id: 'trim', name: 'Trim', icon: '📐' }
  ], []);

  // Filter materials based on search, category, and surface
  const filteredMaterials = useMemo(() => {
    let materials = catalog.materials;

    // Filter by surface/category
    if (selectedSurface) {
      materials = materials.filter(material => {
        // Map surface to category
        const surfaceToCategory: Record<string, string[]> = {
          floor: ['flooring-vinyl', 'flooring-hardwood', 'flooring-tile', 'flooring-carpet'],
          ceiling: ['ceiling-gypsum', 'ceiling-suspended', 'ceiling-wood'],
          walls: ['walls-paint', 'walls-wallpaper', 'walls-tile'],
          trim: ['trim']
        };
        
        const allowedCategories = surfaceToCategory[selectedSurface] || [];
        return allowedCategories.includes(material.category) || material.category.startsWith(selectedSurface);
      });
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      materials = materials.filter(material =>
        material.name.toLowerCase().includes(query) ||
        material.category.toLowerCase().includes(query) ||
        material.subcategory?.toLowerCase().includes(query) ||
        material.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Filter by price
    materials = materials.filter(material => {
      if (!material.pricing) return true;
      return material.pricing.basePrice >= priceFilter.min && 
             material.pricing.basePrice <= priceFilter.max;
    });

    return materials;
  }, [catalog.materials, selectedSurface, searchQuery, priceFilter]);

  // Get current material for selected surface
  const currentMaterial = useMemo(() => {
    if (!currentConfiguration || !selectedSurface) return null;
    const materialId = currentConfiguration.appliedMaterials[selectedSurface];
    return catalog.materials.find(m => m.id === materialId) || null;
  }, [currentConfiguration, selectedSurface, catalog.materials]);

  const handleApplyMaterial = useCallback((material: Material) => {
    if (!selectedRoom || !selectedSurface) return;
    onApplyMaterial(selectedRoom, selectedSurface, material.id);
  }, [selectedRoom, selectedSurface, onApplyMaterial]);

  const formatPrice = (material: Material): string => {
    if (!material.pricing) return 'Price on request';
    return `$${material.pricing.basePrice}/${material.pricing.unit}`;
  };

  if (!selectedRoom) {
    return (
      <div className={styles.container}>
        <div className={styles.emptyState}>
          <div className={styles.emptyIcon}>🎨</div>
          <h3>Select a Room</h3>
          <p>Choose a room to start customizing materials</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h2>Materials</h2>
        <div className={styles.viewToggle}>
          <button
            className={viewMode === 'grid' ? styles.active : ''}
            onClick={() => setViewMode('grid')}
          >
            Grid
          </button>
          <button
            className={viewMode === 'list' ? styles.active : ''}
            onClick={() => setViewMode('list')}
          >
            List
          </button>
        </div>
      </div>

      {/* Surface Selector */}
      <div className={styles.surfaceSelector}>
        <h3>Surface</h3>
        <div className={styles.surfaceButtons}>
          {availableSurfaces.map((surface) => (
            <button
              key={surface.id}
              className={`${styles.surfaceButton} ${selectedSurface === surface.id ? styles.active : ''}`}
              onClick={() => setSelectedSurface(surface.id)}
            >
              <span className={styles.surfaceIcon}>{surface.icon}</span>
              <span>{surface.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Current Material */}
      {currentMaterial && (
        <div className={styles.currentMaterial}>
          <h3>Current {availableSurfaces.find(s => s.id === selectedSurface)?.name} Material</h3>
          <div className={styles.currentMaterialCard}>
            <img
              src={currentMaterial.assets.preview}
              alt={currentMaterial.name}
              className={styles.currentMaterialImage}
              onError={(e) => {
                e.currentTarget.src = '/images/material-placeholder.jpg';
              }}
            />
            <div className={styles.currentMaterialInfo}>
              <h4>{currentMaterial.name}</h4>
              <p>{formatPrice(currentMaterial)}</p>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className={styles.controls}>
        <div className={styles.searchBar}>
          <input
            type="text"
            placeholder="Search materials..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={styles.searchInput}
          />
        </div>

        <div className={styles.priceFilter}>
          <label>Price Range: ${priceFilter.min} - ${priceFilter.max}</label>
          <div className={styles.priceSliders}>
            <input
              type="range"
              min="0"
              max="1000"
              value={priceFilter.min}
              onChange={(e) => setPriceFilter(prev => ({ ...prev, min: parseInt(e.target.value) }))}
              className={styles.priceSlider}
            />
            <input
              type="range"
              min="0"
              max="1000"
              value={priceFilter.max}
              onChange={(e) => setPriceFilter(prev => ({ ...prev, max: parseInt(e.target.value) }))}
              className={styles.priceSlider}
            />
          </div>
        </div>
      </div>

      {/* Materials Grid/List */}
      <div className={styles.materialsContainer}>
        <div className={styles.materialsHeader}>
          <h3>Available Materials ({filteredMaterials.length})</h3>
        </div>

        <div className={`${styles.materialsGrid} ${viewMode === 'list' ? styles.listView : ''}`}>
          {filteredMaterials.map((material) => (
            <div
              key={material.id}
              className={`${styles.materialCard} ${currentMaterial?.id === material.id ? styles.selected : ''}`}
              onClick={() => handleApplyMaterial(material)}
            >
              <div className={styles.materialImage}>
                <img
                  src={material.assets.preview}
                  alt={material.name}
                  onError={(e) => {
                    e.currentTarget.src = '/images/material-placeholder.jpg';
                  }}
                />
                
                {currentMaterial?.id === material.id && (
                  <div className={styles.selectedBadge}>✓</div>
                )}
              </div>

              <div className={styles.materialInfo}>
                <h4 className={styles.materialName}>{material.name}</h4>
                <p className={styles.materialCategory}>
                  {material.subcategory || material.category}
                </p>
                
                {viewMode === 'list' && material.description && (
                  <p className={styles.materialDescription}>{material.description}</p>
                )}
                
                <div className={styles.materialMeta}>
                  <span className={styles.materialPrice}>{formatPrice(material)}</span>
                  {material.properties && (
                    <div className={styles.materialProperties}>
                      <span>Roughness: {material.properties.roughness}</span>
                    </div>
                  )}
                </div>

                {material.tags && material.tags.length > 0 && (
                  <div className={styles.materialTags}>
                    {material.tags.slice(0, 3).map((tag, index) => (
                      <span key={index} className={styles.materialTag}>
                        {tag}
                      </span>
                    ))}
                    {material.tags.length > 3 && (
                      <span className={styles.materialTag}>+{material.tags.length - 3}</span>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredMaterials.length === 0 && (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>🔍</div>
            <h3>No materials found</h3>
            <p>Try adjusting your search or filters</p>
          </div>
        )}
      </div>
    </div>
  );
}
