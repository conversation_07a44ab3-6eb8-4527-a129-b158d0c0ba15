'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { FloorPlan, MaterialCatalog, ViewerSettings } from '../types';
import styles from './ThreeViewer.module.css';

interface ThreeViewerProps {
  floorPlan: FloorPlan | null;
  selectedRoom: string | null;
  materialCatalog: MaterialCatalog;
  viewerSettings: ViewerSettings;
  onRoomSelect: (roomId: string | null) => void;
}

export default function ThreeViewer({
  floorPlan,
  selectedRoom,
  materialCatalog,
  viewerSettings,
  onRoomSelect
}: ThreeViewerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'3d' | 'top' | 'side'>('3d');
  const [showControls, setShowControls] = useState(true);

  // Initialize 3D scene
  useEffect(() => {
    if (!canvasRef.current) return;

    const initializeScene = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // In a real implementation, this would initialize Three.js
        // For now, we'll create a placeholder visualization
        await initializePlaceholderScene();
        setIsLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to initialize 3D viewer');
        setIsLoading(false);
      }
    };

    initializeScene();
  }, []);

  // Update scene when floor plan changes
  useEffect(() => {
    if (!floorPlan || !canvasRef.current) return;
    updateScene();
  }, [floorPlan, selectedRoom, materialCatalog]);

  const initializePlaceholderScene = async () => {
    // Placeholder for Three.js initialization
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const container = containerRef.current;
    if (container) {
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;
    }

    // Draw initial placeholder
    drawPlaceholder(ctx, canvas.width, canvas.height);
  };

  const updateScene = () => {
    const canvas = canvasRef.current;
    if (!canvas || !floorPlan) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (viewMode === '3d') {
      draw3DView(ctx, canvas.width, canvas.height);
    } else if (viewMode === 'top') {
      drawTopView(ctx, canvas.width, canvas.height);
    } else {
      drawSideView(ctx, canvas.width, canvas.height);
    }
  };

  const drawPlaceholder = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // Background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);

    // Grid
    ctx.strokeStyle = '#e9ecef';
    ctx.lineWidth = 1;
    const gridSize = 20;
    
    for (let x = 0; x <= width; x += gridSize) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }
    
    for (let y = 0; y <= height; y += gridSize) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }

    // Placeholder text
    ctx.fillStyle = '#6c757d';
    ctx.font = '24px Inter, sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('3D Viewer', width / 2, height / 2 - 20);
    
    ctx.font = '16px Inter, sans-serif';
    ctx.fillText('Three.js integration coming soon', width / 2, height / 2 + 10);
  };

  const draw3DView = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // Background
    ctx.fillStyle = '#f0f8ff';
    ctx.fillRect(0, 0, width, height);

    if (!floorPlan?.rooms.length) {
      drawPlaceholder(ctx, width, height);
      return;
    }

    // Draw rooms in 3D perspective
    const centerX = width / 2;
    const centerY = height / 2;
    const scale = 30;

    floorPlan.rooms.forEach((room, index) => {
      const x = centerX + (index * 100) - (floorPlan.rooms.length * 50);
      const y = centerY;

      // Draw room as 3D box
      const roomWidth = room.width * scale;
      const roomLength = room.length * scale;
      const roomHeight = room.height * scale * 0.5; // Perspective scaling

      // Floor
      ctx.fillStyle = selectedRoom === room.id ? '#667eea' : '#e9ecef';
      ctx.fillRect(x - roomWidth/2, y - roomLength/2, roomWidth, roomLength);

      // Walls (simplified 3D effect)
      ctx.fillStyle = selectedRoom === room.id ? '#5a6fd8' : '#dee2e6';
      
      // Front wall
      ctx.fillRect(x - roomWidth/2, y + roomLength/2, roomWidth, -roomHeight);
      
      // Side wall
      ctx.fillRect(x + roomWidth/2, y - roomLength/2, -roomHeight * 0.5, roomLength);

      // Room label
      ctx.fillStyle = '#2c3e50';
      ctx.font = '14px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(room.name, x, y + roomLength/2 + 20);
      ctx.fillText(`${room.length}m × ${room.width}m`, x, y + roomLength/2 + 35);
    });
  };

  const drawTopView = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // Background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, width, height);

    if (!floorPlan?.rooms.length) {
      drawPlaceholder(ctx, width, height);
      return;
    }

    // Draw grid
    ctx.strokeStyle = '#f0f0f0';
    ctx.lineWidth = 1;
    const gridSize = 20;
    
    for (let x = 0; x <= width; x += gridSize) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }
    
    for (let y = 0; y <= height; y += gridSize) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }

    // Draw rooms from top view
    const centerX = width / 2;
    const centerY = height / 2;
    const scale = 40;

    floorPlan.rooms.forEach((room, index) => {
      const x = centerX + (index * 120) - (floorPlan.rooms.length * 60);
      const y = centerY;

      const roomWidth = room.width * scale;
      const roomLength = room.length * scale;

      // Room outline
      ctx.strokeStyle = selectedRoom === room.id ? '#667eea' : '#dee2e6';
      ctx.lineWidth = selectedRoom === room.id ? 3 : 2;
      ctx.strokeRect(x - roomWidth/2, y - roomLength/2, roomWidth, roomLength);

      // Room fill
      ctx.fillStyle = selectedRoom === room.id ? 'rgba(102, 126, 234, 0.1)' : 'rgba(233, 236, 239, 0.3)';
      ctx.fillRect(x - roomWidth/2, y - roomLength/2, roomWidth, roomLength);

      // Room label
      ctx.fillStyle = '#2c3e50';
      ctx.font = '12px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(room.name, x, y - 5);
      ctx.fillText(`${room.length}m × ${room.width}m`, x, y + 8);
    });
  };

  const drawSideView = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // Background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);

    if (!floorPlan?.rooms.length) {
      drawPlaceholder(ctx, width, height);
      return;
    }

    // Draw rooms from side view
    const baseY = height - 100;
    const scale = 40;

    floorPlan.rooms.forEach((room, index) => {
      const x = 100 + (index * 150);
      const roomWidth = room.width * scale;
      const roomHeight = room.height * scale;

      // Floor
      ctx.fillStyle = '#8d6e63';
      ctx.fillRect(x, baseY, roomWidth, 10);

      // Walls
      ctx.fillStyle = selectedRoom === room.id ? '#667eea' : '#e9ecef';
      ctx.fillRect(x, baseY - roomHeight, 5, roomHeight);
      ctx.fillRect(x + roomWidth - 5, baseY - roomHeight, 5, roomHeight);

      // Ceiling
      ctx.fillStyle = '#f5f5f5';
      ctx.fillRect(x, baseY - roomHeight, roomWidth, 5);

      // Room label
      ctx.fillStyle = '#2c3e50';
      ctx.font = '12px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(room.name, x + roomWidth/2, baseY + 30);
      ctx.fillText(`H: ${room.height}m`, x + roomWidth/2, baseY + 45);
    });
  };

  const handleCanvasClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!floorPlan?.rooms.length) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Simple hit detection for room selection
    // In a real 3D implementation, this would use proper ray casting
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const scale = viewMode === 'top' ? 40 : 30;

    for (let i = 0; i < floorPlan.rooms.length; i++) {
      const room = floorPlan.rooms[i];
      const roomX = centerX + (i * (viewMode === 'top' ? 120 : 100)) - (floorPlan.rooms.length * (viewMode === 'top' ? 60 : 50));
      const roomY = centerY;
      const roomWidth = room.width * scale;
      const roomLength = room.length * scale;

      if (x >= roomX - roomWidth/2 && x <= roomX + roomWidth/2 &&
          y >= roomY - roomLength/2 && y <= roomY + roomLength/2) {
        onRoomSelect(room.id);
        return;
      }
    }

    onRoomSelect(null);
  }, [floorPlan, viewMode, onRoomSelect]);

  const handleResize = useCallback(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container) return;

    canvas.width = container.clientWidth;
    canvas.height = container.clientHeight;
    updateScene();
  }, [updateScene]);

  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>
          <h3>Viewer Error</h3>
          <p>{error}</p>
          <button onClick={() => setError(null)}>Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className={styles.container}>
      {/* Controls */}
      {showControls && (
        <div className={styles.controls}>
          <div className={styles.viewModes}>
            <button
              className={viewMode === '3d' ? styles.active : ''}
              onClick={() => setViewMode('3d')}
            >
              3D
            </button>
            <button
              className={viewMode === 'top' ? styles.active : ''}
              onClick={() => setViewMode('top')}
            >
              Top
            </button>
            <button
              className={viewMode === 'side' ? styles.active : ''}
              onClick={() => setViewMode('side')}
            >
              Side
            </button>
          </div>

          <div className={styles.viewerOptions}>
            <label className={styles.checkbox}>
              <input
                type="checkbox"
                checked={viewerSettings.showGrid}
                onChange={() => {/* Update settings */}}
              />
              Grid
            </label>
            <label className={styles.checkbox}>
              <input
                type="checkbox"
                checked={viewerSettings.showDimensions}
                onChange={() => {/* Update settings */}}
              />
              Dimensions
            </label>
          </div>

          <button
            className={styles.toggleControls}
            onClick={() => setShowControls(false)}
          >
            Hide
          </button>
        </div>
      )}

      {!showControls && (
        <button
          className={styles.showControls}
          onClick={() => setShowControls(true)}
        >
          Show Controls
        </button>
      )}

      {/* Canvas */}
      <canvas
        ref={canvasRef}
        className={styles.canvas}
        onClick={handleCanvasClick}
      />

      {/* Loading Overlay */}
      {isLoading && (
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading 3D scene...</p>
        </div>
      )}

      {/* Info Panel */}
      {selectedRoom && floorPlan && (
        <div className={styles.infoPanel}>
          {(() => {
            const room = floorPlan.rooms.find(r => r.id === selectedRoom);
            if (!room) return null;
            
            return (
              <div>
                <h4>{room.name}</h4>
                <p>Dimensions: {room.length}m × {room.width}m × {room.height}m</p>
                <p>Area: {(room.length * room.width).toFixed(2)} m²</p>
                <p>Volume: {(room.length * room.width * room.height).toFixed(2)} m³</p>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
}
