.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Canvas */
.canvas {
  width: 100%;
  height: 100%;
  cursor: pointer;
  display: block;
}

/* Controls */
.controls {
  position: absolute;
  top: 1rem;
  left: 1rem;
  right: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.viewModes {
  display: flex;
  gap: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.25rem;
}

.viewModes button {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  font-size: 0.9rem;
}

.viewModes button:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.viewModes button.active {
  background: #667eea;
  color: white;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.viewerOptions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
}

.checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #667eea;
  cursor: pointer;
}

.toggleControls {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.toggleControls:hover {
  background: #5a6268;
}

.showControls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  z-index: 10;
}

.showControls:hover {
  background: white;
  color: #667eea;
  transform: translateY(-1px);
}

/* Loading Overlay */
.loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 249, 250, 0.9);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  color: #6c757d;
  font-size: 1rem;
  margin: 0;
}

/* Error State */
.error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border: 2px solid #dc3545;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
  z-index: 20;
}

.error h3 {
  margin: 0 0 1rem 0;
  color: #dc3545;
  font-size: 1.2rem;
}

.error p {
  margin: 0 0 1.5rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.error button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s ease;
}

.error button:hover {
  background: #c82333;
}

/* Info Panel */
.infoPanel {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 200px;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.infoPanel h4 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.infoPanel p {
  margin: 0.25rem 0;
  color: #6c757d;
  font-size: 0.85rem;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .controls {
    flex-direction: column;
    gap: 1rem;
    padding: 0.75rem;
  }
  
  .viewModes {
    width: 100%;
    justify-content: center;
  }
  
  .viewerOptions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .infoPanel {
    left: 0.5rem;
    right: 0.5rem;
    bottom: 0.5rem;
    min-width: auto;
  }
  
  .showControls {
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.5rem 1rem;
  }
}

@media (max-width: 480px) {
  .controls {
    padding: 0.5rem;
    top: 0.5rem;
    left: 0.5rem;
    right: 0.5rem;
  }
  
  .viewModes button {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
  
  .checkbox {
    font-size: 0.8rem;
  }
  
  .toggleControls {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
  
  .infoPanel {
    padding: 0.75rem;
  }
  
  .infoPanel h4 {
    font-size: 0.9rem;
  }
  
  .infoPanel p {
    font-size: 0.8rem;
  }
}

/* Focus States */
.viewModes button:focus,
.toggleControls:focus,
.showControls:focus,
.error button:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.checkbox input:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .controls,
  .infoPanel,
  .showControls {
    background: white;
    border: 2px solid #000;
  }
  
  .viewModes button.active {
    background: #000;
    color: white;
  }
  
  .canvas {
    border: 2px solid #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .controls,
  .infoPanel {
    animation: none;
  }
  
  .spinner {
    animation: none;
  }
  
  .viewModes button,
  .toggleControls,
  .showControls {
    transition: none;
  }
}

/* Print Styles */
@media print {
  .controls,
  .infoPanel,
  .showControls {
    display: none;
  }
  
  .container {
    background: white;
  }
}
