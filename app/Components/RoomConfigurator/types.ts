// Type definitions for 3D Room Configurator - Extensible Architecture

// Base interfaces for extensibility
export interface BaseModel {
  id: string;
  name: string;
  type: string;
  version: string;
  metadata?: Record<string, any>;
}

export interface BaseComponent {
  id: string;
  name: string;
  description?: string;
  category: string;
  subcategory?: string;
  tags?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Room and Space definitions
export interface RoomDimensions extends BaseModel {
  type: 'room';
  length: number;    // in meters
  width: number;     // in meters
  height: number;    // in meters
  shape: 'rectangle' | 'square' | 'l-shape' | 'custom';
  position?: Vector3D;
  rotation?: Vector3D;
  customVertices?: Vector2D[]; // for custom shapes
}

export interface Vector3D {
  x: number;
  y: number;
  z: number;
}

export interface Vector2D {
  x: number;
  y: number;
}

// Extensible Material System
export interface Material extends BaseComponent {
  category: string; // Now extensible - not limited to specific types
  assets: {
    diffuse: string;        // Main texture
    normal?: string;        // Normal map
    roughness?: string;     // Roughness map
    metalness?: string;     // Metalness map
    displacement?: string;  // Height map
    ao?: string;           // Ambient occlusion
    preview: string;       // Preview thumbnail
  };
  properties: {
    color: string;
    roughness: number;
    metalness: number;
    repeat: [number, number];
    scale: number;
    opacity?: number;
    emissive?: string;
    [key: string]: any; // Allow custom properties
  };
  pricing?: {
    basePrice: number;
    unit: 'sqm' | 'sqft' | 'linear' | 'piece';
    currency: string;
  };
  specifications?: Record<string, any>;
  compatibility?: string[]; // Compatible surface types
}

// Extensible Component System
export interface RoomComponent extends BaseComponent {
  modelType: 'primitive' | 'mesh' | 'gltf' | 'fbx' | 'custom';
  assets?: {
    model?: string;        // 3D model file
    textures?: string[];   // Additional textures
    materials?: string[];  // Material references
  };
  dimensions?: {
    width?: number;
    height?: number;
    depth?: number;
  };
  placement: {
    surface: 'floor' | 'ceiling' | 'wall' | 'corner' | 'center' | 'custom';
    constraints?: PlacementConstraint[];
  };
  configuration?: Record<string, any>; // Component-specific config
}

export interface PlacementConstraint {
  type: 'minDistance' | 'maxDistance' | 'alignment' | 'surface' | 'custom';
  value: any;
  target?: string;
}

// Configuration and State Management
export interface RoomConfiguration {
  roomId: string;
  appliedMaterials: Record<string, string>; // surface -> materialId
  components: ComponentInstance[];
  customizations?: Record<string, any>;
}

export interface ComponentInstance {
  id: string;
  componentId: string;
  position: Vector3D;
  rotation: Vector3D;
  scale: Vector3D;
  materialOverrides?: Record<string, string>;
  properties?: Record<string, any>;
}

export interface FloorPlan extends BaseModel {
  type: 'floorplan';
  rooms: RoomDimensions[];
  configurations: RoomConfiguration[];
  connections?: RoomConnection[];
  unit: 'meters' | 'feet';
  totalArea?: number;
  buildingType?: 'residential' | 'commercial' | 'industrial' | 'mixed';
}

export interface RoomConnection {
  roomA: string;
  roomB: string;
  connectionType: 'door' | 'opening' | 'window' | 'arch';
  position: Vector2D;
  width: number;
  height?: number;
}

// Plugin and Extension System
export interface ConfiguratorPlugin {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  dependencies?: string[];

  // Plugin lifecycle hooks
  onLoad?: () => Promise<void>;
  onUnload?: () => Promise<void>;

  // Extension points
  materials?: Material[];
  components?: RoomComponent[];
  tools?: ConfiguratorTool[];
  renderers?: CustomRenderer[];
}

export interface ConfiguratorTool {
  id: string;
  name: string;
  icon: string;
  category: string;
  action: (context: ConfiguratorContext) => void;
}

export interface CustomRenderer {
  id: string;
  name: string;
  supportedFormats: string[];
  render: (scene: any, options: any) => Promise<any>;
}

export interface ConfiguratorContext {
  floorPlan: FloorPlan | null;
  selectedRoom: string | null;
  selectedComponent: string | null;
  viewerSettings: ViewerSettings;
  materialCatalog: MaterialCatalog;
  componentCatalog: ComponentCatalog;
}

// Catalog System
export interface MaterialCatalog {
  categories: MaterialCategory[];
  materials: Material[];
  filters: CatalogFilter[];
}

export interface ComponentCatalog {
  categories: ComponentCategory[];
  components: RoomComponent[];
  filters: CatalogFilter[];
}

export interface MaterialCategory {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  icon?: string;
  sortOrder: number;
}

export interface ComponentCategory {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  icon?: string;
  sortOrder: number;
}

export interface CatalogFilter {
  id: string;
  name: string;
  type: 'range' | 'select' | 'multiselect' | 'boolean' | 'text';
  options?: FilterOption[];
  defaultValue?: any;
}

export interface FilterOption {
  value: any;
  label: string;
  icon?: string;
}

// Viewer and Rendering
export interface ViewerSettings {
  showGrid: boolean;
  showDimensions: boolean;
  showComponents: boolean;
  cameraMode: 'orbit' | 'walkthrough' | 'top' | 'custom';
  lighting: 'natural' | 'artificial' | 'mixed' | 'custom';
  quality: 'low' | 'medium' | 'high' | 'ultra';
  shadows: boolean;
  reflections: boolean;
  postProcessing: boolean;
  customSettings?: Record<string, any>;
}

// State Management
export interface ConfiguratorState {
  currentFloorPlan: FloorPlan | null;
  selectedRoom: string | null;
  selectedComponent: string | null;
  selectedMaterialCategory: string | null;
  activeMode: 'design' | 'material' | 'component' | 'view';
  viewerSettings: ViewerSettings;
  materialCatalog: MaterialCatalog;
  componentCatalog: ComponentCatalog;
  loadedPlugins: ConfiguratorPlugin[];
  isLoading: boolean;
  error: string | null;
  history: ConfiguratorAction[];
  undoStack: ConfiguratorAction[];
  redoStack: ConfiguratorAction[];
}

export interface ConfiguratorAction {
  id: string;
  type: string;
  timestamp: Date;
  data: any;
  undo?: () => void;
  redo?: () => void;
}
