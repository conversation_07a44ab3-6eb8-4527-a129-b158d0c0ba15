// Sample materials for the room configurator
// This demonstrates how to add new materials to the system

import { Material } from '../types';

export const sampleFlooringMaterials: Material[] = [
  {
    id: 'vinyl-oak-luxury',
    name: 'Luxury Vinyl Oak Plank',
    category: 'flooring-vinyl',
    subcategory: 'luxury-vinyl',
    tags: ['waterproof', 'commercial', 'modern'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    assets: {
      diffuse: '/textures/flooring/vinyl-oak-diffuse.jpg',
      normal: '/textures/flooring/vinyl-oak-normal.jpg',
      roughness: '/textures/flooring/vinyl-oak-roughness.jpg',
      preview: '/textures/flooring/vinyl-oak-preview.jpg'
    },
    properties: {
      color: '#8B7355',
      roughness: 0.8,
      metalness: 0.0,
      repeat: [4, 4],
      scale: 1,
      opacity: 1
    },
    pricing: {
      basePrice: 45,
      unit: 'sqm',
      currency: 'USD'
    },
    specifications: {
      thickness: '6mm',
      wearLayer: '0.5mm',
      installation: 'click-lock',
      warranty: '25 years',
      fireRating: 'Class 1'
    },
    compatibility: ['floor', 'subfloor']
  },
  {
    id: 'engineered-walnut',
    name: 'Engineered American Walnut',
    category: 'flooring-hardwood',
    subcategory: 'engineered',
    tags: ['luxury', 'eco-friendly', 'traditional'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    assets: {
      diffuse: '/textures/flooring/walnut-diffuse.jpg',
      normal: '/textures/flooring/walnut-normal.jpg',
      roughness: '/textures/flooring/walnut-roughness.jpg',
      preview: '/textures/flooring/walnut-preview.jpg'
    },
    properties: {
      color: '#5D4037',
      roughness: 0.7,
      metalness: 0.0,
      repeat: [6, 6],
      scale: 1,
      opacity: 1
    },
    pricing: {
      basePrice: 85,
      unit: 'sqm',
      currency: 'USD'
    },
    specifications: {
      thickness: '14mm',
      topLayer: '3mm solid walnut',
      installation: 'nail-down or glue-down',
      warranty: '50 years',
      janka: '1010 lbf'
    },
    compatibility: ['floor', 'subfloor']
  },
  {
    id: 'bamboo-natural',
    name: 'Natural Strand Bamboo',
    category: 'flooring-hardwood',
    subcategory: 'bamboo',
    tags: ['eco-friendly', 'budget', 'modern'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    assets: {
      diffuse: '/textures/flooring/bamboo-diffuse.jpg',
      normal: '/textures/flooring/bamboo-normal.jpg',
      preview: '/textures/flooring/bamboo-preview.jpg'
    },
    properties: {
      color: '#DEB887',
      roughness: 0.6,
      metalness: 0.0,
      repeat: [8, 8],
      scale: 1,
      opacity: 1
    },
    pricing: {
      basePrice: 65,
      unit: 'sqm',
      currency: 'USD'
    },
    specifications: {
      thickness: '12mm',
      installation: 'floating or nail-down',
      warranty: '25 years',
      carbonFootprint: 'negative'
    },
    compatibility: ['floor', 'subfloor']
  }
];

export const sampleCeilingMaterials: Material[] = [
  {
    id: 'gypsum-smooth-white',
    name: 'Smooth White Gypsum',
    category: 'ceiling-gypsum',
    subcategory: 'smooth',
    tags: ['budget', 'commercial', 'modern'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    assets: {
      diffuse: '/textures/ceiling/gypsum-smooth-diffuse.jpg',
      preview: '/textures/ceiling/gypsum-smooth-preview.jpg'
    },
    properties: {
      color: '#FFFFFF',
      roughness: 0.9,
      metalness: 0.0,
      repeat: [1, 1],
      scale: 1,
      opacity: 1
    },
    pricing: {
      basePrice: 25,
      unit: 'sqm',
      currency: 'USD'
    },
    specifications: {
      thickness: '12.5mm',
      fireRating: 'Class A',
      installation: 'screw-fix',
      acoustics: 'standard'
    },
    compatibility: ['ceiling']
  },
  {
    id: 'gypsum-textured',
    name: 'Textured Gypsum Ceiling',
    category: 'ceiling-gypsum',
    subcategory: 'textured',
    tags: ['luxury', 'traditional'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    assets: {
      diffuse: '/textures/ceiling/gypsum-textured-diffuse.jpg',
      normal: '/textures/ceiling/gypsum-textured-normal.jpg',
      preview: '/textures/ceiling/gypsum-textured-preview.jpg'
    },
    properties: {
      color: '#F8F8F8',
      roughness: 0.8,
      metalness: 0.0,
      repeat: [2, 2],
      scale: 1,
      opacity: 1
    },
    pricing: {
      basePrice: 35,
      unit: 'sqm',
      currency: 'USD'
    },
    specifications: {
      thickness: '15mm',
      texture: 'orange peel',
      fireRating: 'Class A',
      installation: 'screw-fix'
    },
    compatibility: ['ceiling']
  },
  {
    id: 'suspended-grid-commercial',
    name: 'Commercial Suspended Grid',
    category: 'ceiling-suspended',
    subcategory: 'grid',
    tags: ['commercial', 'modern', 'budget'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    assets: {
      diffuse: '/textures/ceiling/suspended-grid-diffuse.jpg',
      preview: '/textures/ceiling/suspended-grid-preview.jpg'
    },
    properties: {
      color: '#E0E0E0',
      roughness: 0.7,
      metalness: 0.1,
      repeat: [4, 4],
      scale: 1,
      opacity: 1
    },
    pricing: {
      basePrice: 45,
      unit: 'sqm',
      currency: 'USD'
    },
    specifications: {
      gridSize: '600x600mm',
      material: 'mineral fiber',
      fireRating: 'Class A',
      acoustics: 'NRC 0.70'
    },
    compatibility: ['ceiling']
  }
];

export const sampleWallMaterials: Material[] = [
  {
    id: 'paint-premium-white',
    name: 'Premium White Paint',
    category: 'walls-paint',
    subcategory: 'premium',
    tags: ['budget', 'modern', 'eco-friendly'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    assets: {
      diffuse: '/textures/walls/paint-white-diffuse.jpg',
      preview: '/textures/walls/paint-white-preview.jpg'
    },
    properties: {
      color: '#FFFFFF',
      roughness: 0.9,
      metalness: 0.0,
      repeat: [1, 1],
      scale: 1,
      opacity: 1
    },
    pricing: {
      basePrice: 15,
      unit: 'sqm',
      currency: 'USD'
    },
    specifications: {
      finish: 'eggshell',
      coverage: '12 sqm/liter',
      voc: 'low',
      washability: 'scrub-resistant'
    },
    compatibility: ['wall', 'ceiling']
  },
  {
    id: 'wallpaper-modern-geometric',
    name: 'Modern Geometric Wallpaper',
    category: 'walls-wallpaper',
    subcategory: 'geometric',
    tags: ['luxury', 'modern'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    assets: {
      diffuse: '/textures/walls/wallpaper-geometric-diffuse.jpg',
      normal: '/textures/walls/wallpaper-geometric-normal.jpg',
      preview: '/textures/walls/wallpaper-geometric-preview.jpg'
    },
    properties: {
      color: '#E8E8E8',
      roughness: 0.8,
      metalness: 0.0,
      repeat: [2, 2],
      scale: 1,
      opacity: 1
    },
    pricing: {
      basePrice: 35,
      unit: 'sqm',
      currency: 'USD'
    },
    specifications: {
      material: 'non-woven',
      pattern: 'geometric',
      installation: 'paste-the-wall',
      washability: 'wipeable'
    },
    compatibility: ['wall']
  }
];

// Combined export for easy importing
export const allSampleMaterials = [
  ...sampleFlooringMaterials,
  ...sampleCeilingMaterials,
  ...sampleWallMaterials
];
