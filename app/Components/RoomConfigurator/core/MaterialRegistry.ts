// Extensible Material Registry System

import { Material, MaterialCategory, MaterialCatalog, CatalogFilter } from '../types';

export class MaterialRegistry {
  private materials: Map<string, Material> = new Map();
  private categories: Map<string, MaterialCategory> = new Map();
  private filters: Map<string, CatalogFilter> = new Map();
  private listeners: Set<(catalog: MaterialCatalog) => void> = new Set();

  constructor() {
    this.initializeDefaultCategories();
    this.initializeDefaultFilters();
  }

  // Material Management
  registerMaterial(material: Material): void {
    this.materials.set(material.id, material);
    this.notifyListeners();
  }

  registerMaterials(materials: Material[]): void {
    materials.forEach(material => this.materials.set(material.id, material));
    this.notifyListeners();
  }

  unregisterMaterial(materialId: string): boolean {
    const result = this.materials.delete(materialId);
    if (result) this.notifyListeners();
    return result;
  }

  getMaterial(materialId: string): Material | undefined {
    return this.materials.get(materialId);
  }

  getMaterialsByCategory(categoryId: string): Material[] {
    return Array.from(this.materials.values())
      .filter(material => material.category === categoryId);
  }

  getAllMaterials(): Material[] {
    return Array.from(this.materials.values());
  }

  // Category Management
  registerCategory(category: MaterialCategory): void {
    this.categories.set(category.id, category);
    this.notifyListeners();
  }

  unregisterCategory(categoryId: string): boolean {
    const result = this.categories.delete(categoryId);
    if (result) this.notifyListeners();
    return result;
  }

  getCategory(categoryId: string): MaterialCategory | undefined {
    return this.categories.get(categoryId);
  }

  getAllCategories(): MaterialCategory[] {
    return Array.from(this.categories.values())
      .sort((a, b) => a.sortOrder - b.sortOrder);
  }

  getCategoryHierarchy(): MaterialCategory[] {
    const categories = this.getAllCategories();
    const rootCategories = categories.filter(cat => !cat.parentId);
    
    const buildHierarchy = (parentId?: string): MaterialCategory[] => {
      return categories
        .filter(cat => cat.parentId === parentId)
        .sort((a, b) => a.sortOrder - b.sortOrder);
    };

    return rootCategories.map(root => ({
      ...root,
      children: buildHierarchy(root.id)
    })) as MaterialCategory[];
  }

  // Filter Management
  registerFilter(filter: CatalogFilter): void {
    this.filters.set(filter.id, filter);
    this.notifyListeners();
  }

  getFilter(filterId: string): CatalogFilter | undefined {
    return this.filters.get(filterId);
  }

  getAllFilters(): CatalogFilter[] {
    return Array.from(this.filters.values());
  }

  // Search and Filter
  searchMaterials(query: string, filters?: Record<string, any>): Material[] {
    let results = Array.from(this.materials.values());

    // Text search
    if (query.trim()) {
      const searchTerm = query.toLowerCase();
      results = results.filter(material => 
        material.name.toLowerCase().includes(searchTerm) ||
        material.category.toLowerCase().includes(searchTerm) ||
        material.subcategory?.toLowerCase().includes(searchTerm) ||
        material.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Apply filters
    if (filters) {
      results = this.applyFilters(results, filters);
    }

    return results;
  }

  private applyFilters(materials: Material[], filters: Record<string, any>): Material[] {
    return materials.filter(material => {
      return Object.entries(filters).every(([filterId, value]) => {
        const filter = this.getFilter(filterId);
        if (!filter || value === undefined || value === null) return true;

        switch (filter.type) {
          case 'select':
            return this.getFilterValue(material, filterId) === value;
          
          case 'multiselect':
            const materialValue = this.getFilterValue(material, filterId);
            return Array.isArray(value) ? value.includes(materialValue) : materialValue === value;
          
          case 'range':
            const numValue = Number(this.getFilterValue(material, filterId));
            return numValue >= value.min && numValue <= value.max;
          
          case 'boolean':
            return Boolean(this.getFilterValue(material, filterId)) === Boolean(value);
          
          case 'text':
            const textValue = String(this.getFilterValue(material, filterId)).toLowerCase();
            return textValue.includes(String(value).toLowerCase());
          
          default:
            return true;
        }
      });
    });
  }

  private getFilterValue(material: Material, filterId: string): any {
    // Map filter IDs to material properties
    switch (filterId) {
      case 'category':
        return material.category;
      case 'subcategory':
        return material.subcategory;
      case 'price':
        return material.pricing?.basePrice;
      case 'roughness':
        return material.properties.roughness;
      case 'metalness':
        return material.properties.metalness;
      case 'tags':
        return material.tags;
      default:
        // Check custom properties
        return material.specifications?.[filterId] || material.properties[filterId];
    }
  }

  // Catalog Generation
  getCatalog(): MaterialCatalog {
    return {
      categories: this.getAllCategories(),
      materials: this.getAllMaterials(),
      filters: this.getAllFilters()
    };
  }

  // Event System
  subscribe(listener: (catalog: MaterialCatalog) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    const catalog = this.getCatalog();
    this.listeners.forEach(listener => listener(catalog));
  }

  // Bulk Operations
  loadMaterialPack(pack: {
    materials: Material[];
    categories?: MaterialCategory[];
    filters?: CatalogFilter[];
  }): void {
    if (pack.categories) {
      pack.categories.forEach(cat => this.registerCategory(cat));
    }
    
    if (pack.filters) {
      pack.filters.forEach(filter => this.registerFilter(filter));
    }
    
    this.registerMaterials(pack.materials);
  }

  exportCatalog(): MaterialCatalog {
    return this.getCatalog();
  }

  importCatalog(catalog: MaterialCatalog): void {
    catalog.categories.forEach(cat => this.registerCategory(cat));
    catalog.filters.forEach(filter => this.registerFilter(filter));
    this.registerMaterials(catalog.materials);
  }

  // Default Setup
  private initializeDefaultCategories(): void {
    const defaultCategories: MaterialCategory[] = [
      { id: 'flooring', name: 'Flooring', sortOrder: 1 },
      { id: 'flooring-vinyl', name: 'Vinyl Flooring', parentId: 'flooring', sortOrder: 1 },
      { id: 'flooring-hardwood', name: 'Hardwood', parentId: 'flooring', sortOrder: 2 },
      { id: 'flooring-tile', name: 'Tile', parentId: 'flooring', sortOrder: 3 },
      { id: 'flooring-carpet', name: 'Carpet', parentId: 'flooring', sortOrder: 4 },
      
      { id: 'ceiling', name: 'Ceiling', sortOrder: 2 },
      { id: 'ceiling-gypsum', name: 'Gypsum', parentId: 'ceiling', sortOrder: 1 },
      { id: 'ceiling-suspended', name: 'Suspended', parentId: 'ceiling', sortOrder: 2 },
      { id: 'ceiling-wood', name: 'Wood', parentId: 'ceiling', sortOrder: 3 },
      
      { id: 'walls', name: 'Walls', sortOrder: 3 },
      { id: 'walls-paint', name: 'Paint', parentId: 'walls', sortOrder: 1 },
      { id: 'walls-wallpaper', name: 'Wallpaper', parentId: 'walls', sortOrder: 2 },
      { id: 'walls-tile', name: 'Wall Tiles', parentId: 'walls', sortOrder: 3 },
      
      { id: 'trim', name: 'Trim & Molding', sortOrder: 4 }
    ];

    defaultCategories.forEach(cat => this.registerCategory(cat));
  }

  private initializeDefaultFilters(): void {
    const defaultFilters: CatalogFilter[] = [
      {
        id: 'category',
        name: 'Category',
        type: 'select',
        options: []
      },
      {
        id: 'price',
        name: 'Price Range',
        type: 'range',
        defaultValue: { min: 0, max: 1000 }
      },
      {
        id: 'roughness',
        name: 'Surface Finish',
        type: 'range',
        defaultValue: { min: 0, max: 1 }
      },
      {
        id: 'tags',
        name: 'Features',
        type: 'multiselect',
        options: [
          { value: 'waterproof', label: 'Waterproof' },
          { value: 'eco-friendly', label: 'Eco-Friendly' },
          { value: 'luxury', label: 'Luxury' },
          { value: 'budget', label: 'Budget-Friendly' },
          { value: 'commercial', label: 'Commercial Grade' }
        ]
      }
    ];

    defaultFilters.forEach(filter => this.registerFilter(filter));
  }
}

// Singleton instance
export const materialRegistry = new MaterialRegistry();
