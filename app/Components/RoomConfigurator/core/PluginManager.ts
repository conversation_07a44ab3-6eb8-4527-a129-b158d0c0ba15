// Plugin Manager for Extensible Room Configurator

import { ConfiguratorPlugin, ConfiguratorContext, ConfiguratorTool, CustomRenderer } from '../types';
import { materialRegistry } from './MaterialRegistry';
import { componentRegistry } from './ComponentRegistry';

export class PluginManager {
  private plugins: Map<string, ConfiguratorPlugin> = new Map();
  private loadedPlugins: Set<string> = new Set();
  private tools: Map<string, ConfiguratorTool> = new Map();
  private renderers: Map<string, CustomRenderer> = new Map();
  private context: ConfiguratorContext | null = null;

  constructor() {
    this.initializeDefaultTools();
  }

  // Plugin Lifecycle
  async registerPlugin(plugin: ConfiguratorPlugin): Promise<void> {
    // Validate plugin
    if (!this.validatePlugin(plugin)) {
      throw new Error(`Invalid plugin: ${plugin.id}`);
    }

    // Check dependencies
    if (plugin.dependencies) {
      const missingDeps = plugin.dependencies.filter(dep => !this.loadedPlugins.has(dep));
      if (missingDeps.length > 0) {
        throw new Error(`Missing dependencies for plugin ${plugin.id}: ${missingDeps.join(', ')}`);
      }
    }

    this.plugins.set(plugin.id, plugin);
  }

  async loadPlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      throw new Error(`Plugin not found: ${pluginId}`);
    }

    if (this.loadedPlugins.has(pluginId)) {
      console.warn(`Plugin ${pluginId} is already loaded`);
      return;
    }

    try {
      // Load plugin resources
      await this.loadPluginResources(plugin);

      // Call plugin onLoad hook
      if (plugin.onLoad) {
        await plugin.onLoad();
      }

      this.loadedPlugins.add(pluginId);
      console.log(`Plugin ${pluginId} loaded successfully`);
    } catch (error) {
      console.error(`Failed to load plugin ${pluginId}:`, error);
      throw error;
    }
  }

  async unloadPlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      throw new Error(`Plugin not found: ${pluginId}`);
    }

    if (!this.loadedPlugins.has(pluginId)) {
      console.warn(`Plugin ${pluginId} is not loaded`);
      return;
    }

    try {
      // Call plugin onUnload hook
      if (plugin.onUnload) {
        await plugin.onUnload();
      }

      // Unload plugin resources
      await this.unloadPluginResources(plugin);

      this.loadedPlugins.delete(pluginId);
      console.log(`Plugin ${pluginId} unloaded successfully`);
    } catch (error) {
      console.error(`Failed to unload plugin ${pluginId}:`, error);
      throw error;
    }
  }

  private async loadPluginResources(plugin: ConfiguratorPlugin): Promise<void> {
    // Load materials
    if (plugin.materials) {
      materialRegistry.registerMaterials(plugin.materials);
    }

    // Load components
    if (plugin.components) {
      componentRegistry.registerComponents(plugin.components);
    }

    // Load tools
    if (plugin.tools) {
      plugin.tools.forEach(tool => this.tools.set(tool.id, tool));
    }

    // Load renderers
    if (plugin.renderers) {
      plugin.renderers.forEach(renderer => this.renderers.set(renderer.id, renderer));
    }
  }

  private async unloadPluginResources(plugin: ConfiguratorPlugin): Promise<void> {
    // Unload materials
    if (plugin.materials) {
      plugin.materials.forEach(material => materialRegistry.unregisterMaterial(material.id));
    }

    // Unload components
    if (plugin.components) {
      plugin.components.forEach(component => componentRegistry.unregisterComponent(component.id));
    }

    // Unload tools
    if (plugin.tools) {
      plugin.tools.forEach(tool => this.tools.delete(tool.id));
    }

    // Unload renderers
    if (plugin.renderers) {
      plugin.renderers.forEach(renderer => this.renderers.delete(renderer.id));
    }
  }

  // Plugin Management
  getPlugin(pluginId: string): ConfiguratorPlugin | undefined {
    return this.plugins.get(pluginId);
  }

  getAllPlugins(): ConfiguratorPlugin[] {
    return Array.from(this.plugins.values());
  }

  getLoadedPlugins(): ConfiguratorPlugin[] {
    return Array.from(this.plugins.values())
      .filter(plugin => this.loadedPlugins.has(plugin.id));
  }

  isPluginLoaded(pluginId: string): boolean {
    return this.loadedPlugins.has(pluginId);
  }

  // Tool Management
  getTool(toolId: string): ConfiguratorTool | undefined {
    return this.tools.get(toolId);
  }

  getToolsByCategory(category: string): ConfiguratorTool[] {
    return Array.from(this.tools.values())
      .filter(tool => tool.category === category);
  }

  getAllTools(): ConfiguratorTool[] {
    return Array.from(this.tools.values());
  }

  executeTool(toolId: string): void {
    const tool = this.tools.get(toolId);
    if (!tool) {
      throw new Error(`Tool not found: ${toolId}`);
    }

    if (!this.context) {
      throw new Error('Configurator context not set');
    }

    tool.action(this.context);
  }

  // Renderer Management
  getRenderer(rendererId: string): CustomRenderer | undefined {
    return this.renderers.get(rendererId);
  }

  getRenderersByFormat(format: string): CustomRenderer[] {
    return Array.from(this.renderers.values())
      .filter(renderer => renderer.supportedFormats.includes(format));
  }

  getAllRenderers(): CustomRenderer[] {
    return Array.from(this.renderers.values());
  }

  async render(rendererId: string, scene: any, options: any): Promise<any> {
    const renderer = this.renderers.get(rendererId);
    if (!renderer) {
      throw new Error(`Renderer not found: ${rendererId}`);
    }

    return await renderer.render(scene, options);
  }

  // Context Management
  setContext(context: ConfiguratorContext): void {
    this.context = context;
  }

  getContext(): ConfiguratorContext | null {
    return this.context;
  }

  // Plugin Validation
  private validatePlugin(plugin: ConfiguratorPlugin): boolean {
    if (!plugin.id || !plugin.name || !plugin.version) {
      return false;
    }

    // Check for duplicate plugin ID
    if (this.plugins.has(plugin.id)) {
      console.warn(`Plugin with ID ${plugin.id} already exists`);
      return false;
    }

    return true;
  }

  // Bulk Operations
  async loadPluginPack(plugins: ConfiguratorPlugin[]): Promise<void> {
    // Sort plugins by dependencies
    const sortedPlugins = this.sortPluginsByDependencies(plugins);
    
    for (const plugin of sortedPlugins) {
      await this.registerPlugin(plugin);
      await this.loadPlugin(plugin.id);
    }
  }

  private sortPluginsByDependencies(plugins: ConfiguratorPlugin[]): ConfiguratorPlugin[] {
    const sorted: ConfiguratorPlugin[] = [];
    const visited = new Set<string>();
    const visiting = new Set<string>();

    const visit = (plugin: ConfiguratorPlugin) => {
      if (visiting.has(plugin.id)) {
        throw new Error(`Circular dependency detected: ${plugin.id}`);
      }
      
      if (visited.has(plugin.id)) {
        return;
      }

      visiting.add(plugin.id);

      if (plugin.dependencies) {
        for (const depId of plugin.dependencies) {
          const depPlugin = plugins.find(p => p.id === depId);
          if (depPlugin) {
            visit(depPlugin);
          }
        }
      }

      visiting.delete(plugin.id);
      visited.add(plugin.id);
      sorted.push(plugin);
    };

    plugins.forEach(visit);
    return sorted;
  }

  // Default Tools
  private initializeDefaultTools(): void {
    const defaultTools: ConfiguratorTool[] = [
      {
        id: 'select',
        name: 'Select',
        icon: 'cursor-pointer',
        category: 'navigation',
        action: (context) => {
          console.log('Select tool activated', context);
        }
      },
      {
        id: 'pan',
        name: 'Pan',
        icon: 'hand',
        category: 'navigation',
        action: (context) => {
          console.log('Pan tool activated', context);
        }
      },
      {
        id: 'zoom',
        name: 'Zoom',
        icon: 'magnifying-glass',
        category: 'navigation',
        action: (context) => {
          console.log('Zoom tool activated', context);
        }
      },
      {
        id: 'measure',
        name: 'Measure',
        icon: 'ruler',
        category: 'utility',
        action: (context) => {
          console.log('Measure tool activated', context);
        }
      },
      {
        id: 'screenshot',
        name: 'Screenshot',
        icon: 'camera',
        category: 'export',
        action: (context) => {
          console.log('Screenshot tool activated', context);
        }
      }
    ];

    defaultTools.forEach(tool => this.tools.set(tool.id, tool));
  }

  // Plugin Discovery and Loading
  async discoverPlugins(pluginDirectory?: string): Promise<ConfiguratorPlugin[]> {
    // In a real implementation, this would scan a directory or registry
    // For now, return empty array
    return [];
  }

  async loadPluginFromUrl(url: string): Promise<void> {
    try {
      const response = await fetch(url);
      const pluginData = await response.json();
      
      await this.registerPlugin(pluginData);
      await this.loadPlugin(pluginData.id);
    } catch (error) {
      console.error(`Failed to load plugin from URL ${url}:`, error);
      throw error;
    }
  }
}

// Singleton instance
export const pluginManager = new PluginManager();
