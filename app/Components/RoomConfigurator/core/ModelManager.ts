// Model Manager for Dynamic 3D Model Loading and Management

import { RoomComponent, Vector3D } from '../types';

export interface ModelAsset {
  id: string;
  name: string;
  description?: string;
  category: string;
  subcategory?: string;
  tags: string[];
  
  // File information
  files: {
    model: string;           // Main model file (.gltf, .glb, .fbx, .obj)
    thumbnail: string;       // Preview image
    textures?: string[];     // Additional texture files
    materials?: string[];    // Material definition files
    animations?: string[];   // Animation files
  };
  
  // Model metadata
  format: 'gltf' | 'glb' | 'fbx' | 'obj' | 'dae' | 'ply' | 'stl';
  size: number;             // File size in bytes
  polyCount?: number;       // Triangle/polygon count
  boundingBox: {
    min: Vector3D;
    max: Vector3D;
  };
  
  // Usage information
  placement: {
    defaultSurface: 'floor' | 'ceiling' | 'wall' | 'corner' | 'center';
    allowedSurfaces: string[];
    snapToGrid: boolean;
    snapDistance: number;
  };
  
  // Scaling and positioning
  defaultScale: Vector3D;
  defaultRotation: Vector3D;
  scaleConstraints: {
    min: Vector3D;
    max: Vector3D;
    uniform: boolean;       // Whether scaling must be uniform
  };
  
  // Performance settings
  lodLevels?: {
    high: string;           // High detail model
    medium: string;         // Medium detail model
    low: string;            // Low detail model
  };
  
  // Interaction
  interactive: boolean;
  configurable: boolean;
  animatable: boolean;
  
  // Metadata
  author?: string;
  license?: string;
  version: string;
  createdAt: Date;
  updatedAt: Date;
  downloadCount?: number;
  rating?: number;
}

export interface ModelPack {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  models: ModelAsset[];
  dependencies?: string[];
  installSize: number;
  thumbnail?: string;
}

export interface ModelLoadOptions {
  quality: 'low' | 'medium' | 'high';
  enableAnimations: boolean;
  enableInteractions: boolean;
  castShadows: boolean;
  receiveShadows: boolean;
  customMaterials?: Record<string, any>;
}

export class ModelManager {
  private models: Map<string, ModelAsset> = new Map();
  private loadedModels: Map<string, any> = new Map(); // Cached 3D objects
  private modelPacks: Map<string, ModelPack> = new Map();
  private loadingPromises: Map<string, Promise<any>> = new Map();
  private listeners: Set<(models: ModelAsset[]) => void> = new Set();

  constructor() {
    this.initializeDefaultModels();
  }

  // Model Registration
  async registerModel(model: ModelAsset): Promise<void> {
    // Validate model data
    if (!this.validateModel(model)) {
      throw new Error(`Invalid model data for ${model.id}`);
    }

    // Check if files exist
    await this.validateModelFiles(model);

    this.models.set(model.id, model);
    this.notifyListeners();
  }

  async registerModels(models: ModelAsset[]): Promise<void> {
    for (const model of models) {
      await this.registerModel(model);
    }
  }

  unregisterModel(modelId: string): boolean {
    const result = this.models.delete(modelId);
    
    // Clean up loaded model from cache
    if (this.loadedModels.has(modelId)) {
      this.unloadModel(modelId);
    }
    
    if (result) this.notifyListeners();
    return result;
  }

  // Model Loading
  async loadModel(modelId: string, options: ModelLoadOptions = this.getDefaultLoadOptions()): Promise<any> {
    const model = this.models.get(modelId);
    if (!model) {
      throw new Error(`Model not found: ${modelId}`);
    }

    // Check if already loading
    if (this.loadingPromises.has(modelId)) {
      return this.loadingPromises.get(modelId);
    }

    // Check if already loaded
    const cacheKey = this.getCacheKey(modelId, options);
    if (this.loadedModels.has(cacheKey)) {
      return this.loadedModels.get(cacheKey);
    }

    // Start loading
    const loadPromise = this.loadModelInternal(model, options);
    this.loadingPromises.set(modelId, loadPromise);

    try {
      const loadedModel = await loadPromise;
      this.loadedModels.set(cacheKey, loadedModel);
      return loadedModel;
    } finally {
      this.loadingPromises.delete(modelId);
    }
  }

  private async loadModelInternal(model: ModelAsset, options: ModelLoadOptions): Promise<any> {
    const modelFile = this.selectModelFile(model, options.quality);
    
    switch (model.format) {
      case 'gltf':
      case 'glb':
        return this.loadGLTFModel(modelFile, model, options);
      case 'fbx':
        return this.loadFBXModel(modelFile, model, options);
      case 'obj':
        return this.loadOBJModel(modelFile, model, options);
      default:
        throw new Error(`Unsupported model format: ${model.format}`);
    }
  }

  private selectModelFile(model: ModelAsset, quality: 'low' | 'medium' | 'high'): string {
    if (model.lodLevels) {
      switch (quality) {
        case 'high':
          return model.lodLevels.high || model.files.model;
        case 'medium':
          return model.lodLevels.medium || model.files.model;
        case 'low':
          return model.lodLevels.low || model.files.model;
      }
    }
    return model.files.model;
  }

  private async loadGLTFModel(file: string, model: ModelAsset, options: ModelLoadOptions): Promise<any> {
    // This would use Three.js GLTFLoader
    // For now, return a placeholder
    return {
      type: 'gltf',
      file,
      model,
      options,
      loaded: true
    };
  }

  private async loadFBXModel(file: string, model: ModelAsset, options: ModelLoadOptions): Promise<any> {
    // This would use Three.js FBXLoader
    return {
      type: 'fbx',
      file,
      model,
      options,
      loaded: true
    };
  }

  private async loadOBJModel(file: string, model: ModelAsset, options: ModelLoadOptions): Promise<any> {
    // This would use Three.js OBJLoader
    return {
      type: 'obj',
      file,
      model,
      options,
      loaded: true
    };
  }

  unloadModel(modelId: string): void {
    // Remove from cache
    const keysToRemove = Array.from(this.loadedModels.keys())
      .filter(key => key.startsWith(modelId));
    
    keysToRemove.forEach(key => {
      const model = this.loadedModels.get(key);
      if (model && model.dispose) {
        model.dispose(); // Clean up Three.js resources
      }
      this.loadedModels.delete(key);
    });
  }

  // Model Pack Management
  async installModelPack(pack: ModelPack): Promise<void> {
    // Validate pack
    if (!this.validateModelPack(pack)) {
      throw new Error(`Invalid model pack: ${pack.id}`);
    }

    // Check dependencies
    if (pack.dependencies) {
      const missingDeps = pack.dependencies.filter(dep => !this.modelPacks.has(dep));
      if (missingDeps.length > 0) {
        throw new Error(`Missing dependencies: ${missingDeps.join(', ')}`);
      }
    }

    // Install models
    await this.registerModels(pack.models);
    this.modelPacks.set(pack.id, pack);
  }

  uninstallModelPack(packId: string): void {
    const pack = this.modelPacks.get(packId);
    if (!pack) return;

    // Remove all models from this pack
    pack.models.forEach(model => this.unregisterModel(model.id));
    this.modelPacks.delete(packId);
  }

  // File Upload and Processing
  async uploadModel(file: File, metadata: Partial<ModelAsset>): Promise<string> {
    // Validate file
    if (!this.isValidModelFile(file)) {
      throw new Error('Invalid model file format');
    }

    // Generate unique ID
    const modelId = `uploaded-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Upload file (this would integrate with your file storage)
    const uploadedUrl = await this.uploadFile(file);
    
    // Extract model information
    const modelInfo = await this.extractModelInfo(file);
    
    // Create model asset
    const model: ModelAsset = {
      id: modelId,
      name: metadata.name || file.name,
      description: metadata.description || '',
      category: metadata.category || 'custom',
      subcategory: metadata.subcategory,
      tags: metadata.tags || ['uploaded'],
      files: {
        model: uploadedUrl,
        thumbnail: metadata.files?.thumbnail || await this.generateThumbnail(file),
        textures: metadata.files?.textures || [],
        materials: metadata.files?.materials || [],
        animations: metadata.files?.animations || []
      },
      format: this.detectFormat(file),
      size: file.size,
      polyCount: modelInfo.polyCount,
      boundingBox: modelInfo.boundingBox,
      placement: metadata.placement || {
        defaultSurface: 'floor',
        allowedSurfaces: ['floor'],
        snapToGrid: true,
        snapDistance: 0.1
      },
      defaultScale: metadata.defaultScale || { x: 1, y: 1, z: 1 },
      defaultRotation: metadata.defaultRotation || { x: 0, y: 0, z: 0 },
      scaleConstraints: metadata.scaleConstraints || {
        min: { x: 0.1, y: 0.1, z: 0.1 },
        max: { x: 10, y: 10, z: 10 },
        uniform: false
      },
      interactive: metadata.interactive || false,
      configurable: metadata.configurable || false,
      animatable: metadata.animatable || false,
      version: '1.0.0',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.registerModel(model);
    return modelId;
  }

  // Utility Methods
  getModel(modelId: string): ModelAsset | undefined {
    return this.models.get(modelId);
  }

  getAllModels(): ModelAsset[] {
    return Array.from(this.models.values());
  }

  getModelsByCategory(category: string): ModelAsset[] {
    return Array.from(this.models.values())
      .filter(model => model.category === category);
  }

  searchModels(query: string, filters?: Record<string, any>): ModelAsset[] {
    let results = Array.from(this.models.values());

    // Text search
    if (query.trim()) {
      const searchTerm = query.toLowerCase();
      results = results.filter(model =>
        model.name.toLowerCase().includes(searchTerm) ||
        model.description?.toLowerCase().includes(searchTerm) ||
        model.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Apply filters
    if (filters) {
      results = this.applyModelFilters(results, filters);
    }

    return results;
  }

  private applyModelFilters(models: ModelAsset[], filters: Record<string, any>): ModelAsset[] {
    return models.filter(model => {
      return Object.entries(filters).every(([key, value]) => {
        switch (key) {
          case 'category':
            return model.category === value;
          case 'format':
            return model.format === value;
          case 'interactive':
            return model.interactive === value;
          case 'maxPolyCount':
            return !model.polyCount || model.polyCount <= value;
          case 'maxSize':
            return model.size <= value;
          default:
            return true;
        }
      });
    });
  }

  // Event System
  subscribe(listener: (models: ModelAsset[]) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    const models = this.getAllModels();
    this.listeners.forEach(listener => listener(models));
  }

  // Validation and Utility
  private validateModel(model: ModelAsset): boolean {
    return !!(model.id && model.name && model.files.model && model.format);
  }

  private validateModelPack(pack: ModelPack): boolean {
    return !!(pack.id && pack.name && pack.models && pack.models.length > 0);
  }

  private async validateModelFiles(model: ModelAsset): Promise<void> {
    // In a real implementation, this would check if files exist
    // For now, just validate URLs
    if (!model.files.model.startsWith('http') && !model.files.model.startsWith('/')) {
      throw new Error(`Invalid model file path: ${model.files.model}`);
    }
  }

  private isValidModelFile(file: File): boolean {
    const validExtensions = ['.gltf', '.glb', '.fbx', '.obj', '.dae', '.ply', '.stl'];
    return validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
  }

  private detectFormat(file: File): ModelAsset['format'] {
    const extension = file.name.toLowerCase().split('.').pop();
    switch (extension) {
      case 'gltf': return 'gltf';
      case 'glb': return 'glb';
      case 'fbx': return 'fbx';
      case 'obj': return 'obj';
      case 'dae': return 'dae';
      case 'ply': return 'ply';
      case 'stl': return 'stl';
      default: return 'gltf';
    }
  }

  private async uploadFile(file: File): Promise<string> {
    // This would integrate with your file storage service
    // For now, return a placeholder URL
    return `/uploads/models/${file.name}`;
  }

  private async extractModelInfo(file: File): Promise<{ polyCount?: number; boundingBox: any }> {
    // This would analyze the model file to extract metadata
    return {
      polyCount: undefined,
      boundingBox: {
        min: { x: -1, y: -1, z: -1 },
        max: { x: 1, y: 1, z: 1 }
      }
    };
  }

  private async generateThumbnail(file: File): Promise<string> {
    // This would generate a thumbnail image of the model
    return '/images/model-placeholder.jpg';
  }

  private getCacheKey(modelId: string, options: ModelLoadOptions): string {
    return `${modelId}-${JSON.stringify(options)}`;
  }

  private getDefaultLoadOptions(): ModelLoadOptions {
    return {
      quality: 'medium',
      enableAnimations: true,
      enableInteractions: true,
      castShadows: true,
      receiveShadows: true
    };
  }

  private initializeDefaultModels(): void {
    // Initialize with some basic primitive models
    // These would be expanded with actual model files
  }
}

// Singleton instance
export const modelManager = new ModelManager();
