// Extensible Component Registry System

import { RoomComponent, ComponentCategory, ComponentCatalog, CatalogFilter } from '../types';

export class ComponentRegistry {
  private components: Map<string, RoomComponent> = new Map();
  private categories: Map<string, ComponentCategory> = new Map();
  private filters: Map<string, CatalogFilter> = new Map();
  private listeners: Set<(catalog: ComponentCatalog) => void> = new Set();

  constructor() {
    this.initializeDefaultCategories();
    this.initializeDefaultFilters();
  }

  // Component Management
  registerComponent(component: RoomComponent): void {
    this.components.set(component.id, component);
    this.notifyListeners();
  }

  registerComponents(components: RoomComponent[]): void {
    components.forEach(component => this.components.set(component.id, component));
    this.notifyListeners();
  }

  unregisterComponent(componentId: string): boolean {
    const result = this.components.delete(componentId);
    if (result) this.notifyListeners();
    return result;
  }

  getComponent(componentId: string): RoomComponent | undefined {
    return this.components.get(componentId);
  }

  getComponentsByCategory(categoryId: string): RoomComponent[] {
    return Array.from(this.components.values())
      .filter(component => component.category === categoryId);
  }

  getAllComponents(): RoomComponent[] {
    return Array.from(this.components.values());
  }

  // Category Management
  registerCategory(category: ComponentCategory): void {
    this.categories.set(category.id, category);
    this.notifyListeners();
  }

  unregisterCategory(categoryId: string): boolean {
    const result = this.categories.delete(categoryId);
    if (result) this.notifyListeners();
    return result;
  }

  getCategory(categoryId: string): ComponentCategory | undefined {
    return this.categories.get(categoryId);
  }

  getAllCategories(): ComponentCategory[] {
    return Array.from(this.categories.values())
      .sort((a, b) => a.sortOrder - b.sortOrder);
  }

  // Filter Management
  registerFilter(filter: CatalogFilter): void {
    this.filters.set(filter.id, filter);
    this.notifyListeners();
  }

  getFilter(filterId: string): CatalogFilter | undefined {
    return this.filters.get(filterId);
  }

  getAllFilters(): CatalogFilter[] {
    return Array.from(this.filters.values());
  }

  // Search and Filter
  searchComponents(query: string, filters?: Record<string, any>): RoomComponent[] {
    let results = Array.from(this.components.values());

    // Text search
    if (query.trim()) {
      const searchTerm = query.toLowerCase();
      results = results.filter(component => 
        component.name.toLowerCase().includes(searchTerm) ||
        component.category.toLowerCase().includes(searchTerm) ||
        component.subcategory?.toLowerCase().includes(searchTerm) ||
        component.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Apply filters
    if (filters) {
      results = this.applyFilters(results, filters);
    }

    return results;
  }

  private applyFilters(components: RoomComponent[], filters: Record<string, any>): RoomComponent[] {
    return components.filter(component => {
      return Object.entries(filters).every(([filterId, value]) => {
        const filter = this.getFilter(filterId);
        if (!filter || value === undefined || value === null) return true;

        switch (filter.type) {
          case 'select':
            return this.getFilterValue(component, filterId) === value;
          
          case 'multiselect':
            const componentValue = this.getFilterValue(component, filterId);
            return Array.isArray(value) ? value.includes(componentValue) : componentValue === value;
          
          case 'range':
            const numValue = Number(this.getFilterValue(component, filterId));
            return numValue >= value.min && numValue <= value.max;
          
          case 'boolean':
            return Boolean(this.getFilterValue(component, filterId)) === Boolean(value);
          
          case 'text':
            const textValue = String(this.getFilterValue(component, filterId)).toLowerCase();
            return textValue.includes(String(value).toLowerCase());
          
          default:
            return true;
        }
      });
    });
  }

  private getFilterValue(component: RoomComponent, filterId: string): any {
    switch (filterId) {
      case 'category':
        return component.category;
      case 'subcategory':
        return component.subcategory;
      case 'modelType':
        return component.modelType;
      case 'placement':
        return component.placement.surface;
      case 'tags':
        return component.tags;
      case 'width':
        return component.dimensions?.width;
      case 'height':
        return component.dimensions?.height;
      case 'depth':
        return component.dimensions?.depth;
      default:
        return component.configuration?.[filterId];
    }
  }

  // Catalog Generation
  getCatalog(): ComponentCatalog {
    return {
      categories: this.getAllCategories(),
      components: this.getAllComponents(),
      filters: this.getAllFilters()
    };
  }

  // Event System
  subscribe(listener: (catalog: ComponentCatalog) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    const catalog = this.getCatalog();
    this.listeners.forEach(listener => listener(catalog));
  }

  // Bulk Operations
  loadComponentPack(pack: {
    components: RoomComponent[];
    categories?: ComponentCategory[];
    filters?: CatalogFilter[];
  }): void {
    if (pack.categories) {
      pack.categories.forEach(cat => this.registerCategory(cat));
    }
    
    if (pack.filters) {
      pack.filters.forEach(filter => this.registerFilter(filter));
    }
    
    this.registerComponents(pack.components);
  }

  exportCatalog(): ComponentCatalog {
    return this.getCatalog();
  }

  importCatalog(catalog: ComponentCatalog): void {
    catalog.categories.forEach(cat => this.registerCategory(cat));
    catalog.filters.forEach(filter => this.registerFilter(filter));
    this.registerComponents(catalog.components);
  }

  // Default Setup
  private initializeDefaultCategories(): void {
    const defaultCategories: ComponentCategory[] = [
      { id: 'furniture', name: 'Furniture', sortOrder: 1 },
      { id: 'furniture-seating', name: 'Seating', parentId: 'furniture', sortOrder: 1 },
      { id: 'furniture-tables', name: 'Tables', parentId: 'furniture', sortOrder: 2 },
      { id: 'furniture-storage', name: 'Storage', parentId: 'furniture', sortOrder: 3 },
      
      { id: 'lighting', name: 'Lighting', sortOrder: 2 },
      { id: 'lighting-ceiling', name: 'Ceiling Lights', parentId: 'lighting', sortOrder: 1 },
      { id: 'lighting-wall', name: 'Wall Lights', parentId: 'lighting', sortOrder: 2 },
      { id: 'lighting-floor', name: 'Floor Lamps', parentId: 'lighting', sortOrder: 3 },
      
      { id: 'fixtures', name: 'Fixtures', sortOrder: 3 },
      { id: 'fixtures-plumbing', name: 'Plumbing', parentId: 'fixtures', sortOrder: 1 },
      { id: 'fixtures-electrical', name: 'Electrical', parentId: 'fixtures', sortOrder: 2 },
      
      { id: 'doors-windows', name: 'Doors & Windows', sortOrder: 4 },
      { id: 'doors', name: 'Doors', parentId: 'doors-windows', sortOrder: 1 },
      { id: 'windows', name: 'Windows', parentId: 'doors-windows', sortOrder: 2 },
      
      { id: 'decorative', name: 'Decorative', sortOrder: 5 },
      { id: 'decorative-art', name: 'Wall Art', parentId: 'decorative', sortOrder: 1 },
      { id: 'decorative-plants', name: 'Plants', parentId: 'decorative', sortOrder: 2 }
    ];

    defaultCategories.forEach(cat => this.registerCategory(cat));
  }

  private initializeDefaultFilters(): void {
    const defaultFilters: CatalogFilter[] = [
      {
        id: 'category',
        name: 'Category',
        type: 'select',
        options: []
      },
      {
        id: 'modelType',
        name: 'Model Type',
        type: 'select',
        options: [
          { value: 'primitive', label: 'Basic Shape' },
          { value: 'mesh', label: 'Custom Mesh' },
          { value: 'gltf', label: 'GLTF Model' },
          { value: 'fbx', label: 'FBX Model' }
        ]
      },
      {
        id: 'placement',
        name: 'Placement',
        type: 'multiselect',
        options: [
          { value: 'floor', label: 'Floor' },
          { value: 'ceiling', label: 'Ceiling' },
          { value: 'wall', label: 'Wall' },
          { value: 'corner', label: 'Corner' },
          { value: 'center', label: 'Center' }
        ]
      },
      {
        id: 'width',
        name: 'Width (m)',
        type: 'range',
        defaultValue: { min: 0, max: 10 }
      },
      {
        id: 'height',
        name: 'Height (m)',
        type: 'range',
        defaultValue: { min: 0, max: 5 }
      },
      {
        id: 'tags',
        name: 'Features',
        type: 'multiselect',
        options: [
          { value: 'interactive', label: 'Interactive' },
          { value: 'animated', label: 'Animated' },
          { value: 'configurable', label: 'Configurable' },
          { value: 'luxury', label: 'Luxury' },
          { value: 'modern', label: 'Modern' },
          { value: 'traditional', label: 'Traditional' }
        ]
      }
    ];

    defaultFilters.forEach(filter => this.registerFilter(filter));
  }
}

// Singleton instance
export const componentRegistry = new ComponentRegistry();
