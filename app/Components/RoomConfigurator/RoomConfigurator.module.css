.configurator {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.modeSelector {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.25rem;
}

.modeSelector button {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.modeSelector button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.modeSelector button.active {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.exportBtn {
  padding: 0.5rem 1.5rem;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s ease;
}

.exportBtn:hover {
  background: #45a049;
}

/* Main Content */
.content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.leftPanel {
  width: 350px;
  background: white;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
}

.viewer {
  flex: 1;
  position: relative;
  background: #f8f9fa;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f5f5f5;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  font-size: 1.1rem;
  margin: 0;
}

/* Error State */
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f5f5f5;
  color: #d32f2f;
  text-align: center;
  padding: 2rem;
}

.error h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.error p {
  margin: 0 0 2rem 0;
  font-size: 1rem;
  max-width: 500px;
}

.error button {
  padding: 0.75rem 2rem;
  background: #d32f2f;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.2s ease;
}

.error button:hover {
  background: #b71c1c;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .leftPanel {
    width: 300px;
  }
  
  .header h1 {
    font-size: 1.25rem;
  }
  
  .modeSelector button {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .configurator {
    height: 100vh;
  }
  
  .header {
    padding: 0.75rem 1rem;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .header h1 {
    font-size: 1.1rem;
    order: 1;
  }
  
  .modeSelector {
    order: 3;
    flex: 1;
    justify-content: center;
  }
  
  .exportBtn {
    order: 2;
    padding: 0.4rem 1rem;
    font-size: 0.9rem;
  }
  
  .content {
    flex-direction: column;
  }
  
  .leftPanel {
    width: 100%;
    height: 40vh;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .viewer {
    height: 60vh;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 0.5rem;
  }
  
  .header h1 {
    font-size: 1rem;
  }
  
  .modeSelector button {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
  }
  
  .exportBtn {
    padding: 0.3rem 0.8rem;
    font-size: 0.8rem;
  }
  
  .leftPanel {
    height: 35vh;
  }
  
  .viewer {
    height: 65vh;
  }
}

/* Panel Transitions */
.leftPanel {
  transition: width 0.3s ease;
}

.viewer {
  transition: all 0.3s ease;
}

/* Focus States */
.modeSelector button:focus,
.exportBtn:focus,
.error button:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .header {
    background: #000;
    border-bottom: 2px solid #fff;
  }
  
  .leftPanel {
    border-right: 2px solid #000;
  }
  
  .modeSelector button.active {
    background: #fff;
    color: #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .spinner {
    animation: none;
  }
  
  .leftPanel,
  .viewer,
  .modeSelector button,
  .exportBtn {
    transition: none;
  }
}
