'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { ConfiguratorState, FloorPlan, RoomDimensions, RoomConfiguration } from './types';
import { materialRegistry } from './core/MaterialRegistry';
import { componentRegistry } from './core/ComponentRegistry';
import { pluginManager } from './core/PluginManager';
import { allSampleMaterials } from './data/sampleMaterials';
import { allSampleModels } from './data/sampleModels';
import DimensionInput from './components/DimensionInput';
import MaterialSelector from './components/MaterialSelector';
import ThreeViewer from './components/ThreeViewer';
import styles from './RoomConfigurator.module.css';

interface RoomConfiguratorProps {
  initialFloorPlan?: FloorPlan;
  onConfigurationChange?: (floorPlan: FloorPlan) => void;
  onExport?: (data: any) => void;
}

export default function RoomConfigurator({
  initialFloorPlan,
  onConfigurationChange,
  onExport
}: RoomConfiguratorProps) {
  const [state, setState] = useState<ConfiguratorState>({
    currentFloorPlan: initialFloorPlan || null,
    selectedRoom: null,
    selectedComponent: null,
    selectedMaterialCategory: null,
    activeMode: 'design',
    viewerSettings: {
      showGrid: true,
      showDimensions: true,
      showComponents: true,
      cameraMode: 'orbit',
      lighting: 'natural',
      quality: 'medium',
      shadows: true,
      reflections: false,
      postProcessing: false
    },
    materialCatalog: materialRegistry.getCatalog(),
    componentCatalog: componentRegistry.getCatalog(),
    loadedPlugins: [],
    isLoading: false,
    error: null,
    history: [],
    undoStack: [],
    redoStack: []
  });

  // Initialize the configurator
  useEffect(() => {
    initializeConfigurator();
  }, []);

  const initializeConfigurator = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      // Load sample materials
      materialRegistry.registerMaterials(allSampleMaterials);

      // Load sample models
      const { modelManager } = await import('./core/ModelManager');
      await modelManager.registerModels(allSampleModels);

      // Set up plugin manager context
      pluginManager.setContext({
        floorPlan: state.currentFloorPlan,
        selectedRoom: state.selectedRoom,
        selectedComponent: state.selectedComponent,
        viewerSettings: state.viewerSettings,
        materialCatalog: state.materialCatalog,
        componentCatalog: state.componentCatalog
      });

      // Subscribe to catalog updates
      const unsubscribeMaterials = materialRegistry.subscribe((catalog) => {
        setState(prev => ({ ...prev, materialCatalog: catalog }));
      });

      const unsubscribeComponents = componentRegistry.subscribe((catalog) => {
        setState(prev => ({ ...prev, componentCatalog: catalog }));
      });

      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        materialCatalog: materialRegistry.getCatalog(),
        componentCatalog: componentRegistry.getCatalog()
      }));

      // Cleanup function
      return () => {
        unsubscribeMaterials();
        unsubscribeComponents();
      };
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to initialize configurator'
      }));
    }
  }, []);

  // Room management
  const createRoom = useCallback((dimensions: Omit<RoomDimensions, 'id'>) => {
    const roomId = `room-${Date.now()}`;
    const newRoom: RoomDimensions = {
      ...dimensions,
      id: roomId,
      type: 'room',
      version: '1.0.0'
    };

    const newConfiguration: RoomConfiguration = {
      roomId,
      appliedMaterials: {
        floor: 'vinyl-oak-luxury',
        ceiling: 'gypsum-smooth-white',
        walls: 'paint-premium-white'
      },
      components: [],
      customizations: {}
    };

    setState(prev => {
      const updatedFloorPlan: FloorPlan = prev.currentFloorPlan ? {
        ...prev.currentFloorPlan,
        rooms: [...prev.currentFloorPlan.rooms, newRoom],
        configurations: [...prev.currentFloorPlan.configurations, newConfiguration]
      } : {
        id: `floorplan-${Date.now()}`,
        name: 'New Floor Plan',
        type: 'floorplan',
        version: '1.0.0',
        rooms: [newRoom],
        configurations: [newConfiguration],
        unit: 'meters',
        buildingType: 'residential'
      };

      onConfigurationChange?.(updatedFloorPlan);

      return {
        ...prev,
        currentFloorPlan: updatedFloorPlan,
        selectedRoom: roomId
      };
    });
  }, [onConfigurationChange]);

  const updateRoom = useCallback((roomId: string, updates: Partial<RoomDimensions>) => {
    setState(prev => {
      if (!prev.currentFloorPlan) return prev;

      const updatedFloorPlan = {
        ...prev.currentFloorPlan,
        rooms: prev.currentFloorPlan.rooms.map(room =>
          room.id === roomId ? { ...room, ...updates, updatedAt: new Date() } : room
        )
      };

      onConfigurationChange?.(updatedFloorPlan);

      return {
        ...prev,
        currentFloorPlan: updatedFloorPlan
      };
    });
  }, [onConfigurationChange]);

  const deleteRoom = useCallback((roomId: string) => {
    setState(prev => {
      if (!prev.currentFloorPlan) return prev;

      const updatedFloorPlan = {
        ...prev.currentFloorPlan,
        rooms: prev.currentFloorPlan.rooms.filter(room => room.id !== roomId),
        configurations: prev.currentFloorPlan.configurations.filter(config => config.roomId !== roomId)
      };

      onConfigurationChange?.(updatedFloorPlan);

      return {
        ...prev,
        currentFloorPlan: updatedFloorPlan,
        selectedRoom: prev.selectedRoom === roomId ? null : prev.selectedRoom
      };
    });
  }, [onConfigurationChange]);

  // Material management
  const applyMaterial = useCallback((roomId: string, surface: string, materialId: string) => {
    setState(prev => {
      if (!prev.currentFloorPlan) return prev;

      const updatedFloorPlan = {
        ...prev.currentFloorPlan,
        configurations: prev.currentFloorPlan.configurations.map(config =>
          config.roomId === roomId
            ? {
                ...config,
                appliedMaterials: {
                  ...config.appliedMaterials,
                  [surface]: materialId
                }
              }
            : config
        )
      };

      onConfigurationChange?.(updatedFloorPlan);

      return {
        ...prev,
        currentFloorPlan: updatedFloorPlan
      };
    });
  }, [onConfigurationChange]);

  // Mode switching
  const setActiveMode = useCallback((mode: ConfiguratorState['activeMode']) => {
    setState(prev => ({ ...prev, activeMode: mode }));
  }, []);

  // Selection management
  const selectRoom = useCallback((roomId: string | null) => {
    setState(prev => ({ ...prev, selectedRoom: roomId }));
  }, []);

  const selectMaterialCategory = useCallback((category: string | null) => {
    setState(prev => ({ ...prev, selectedMaterialCategory: category }));
  }, []);

  // Export functionality
  const handleExport = useCallback(() => {
    if (!state.currentFloorPlan) return;

    const exportData = {
      floorPlan: state.currentFloorPlan,
      viewerSettings: state.viewerSettings,
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    };

    onExport?.(exportData);
  }, [state.currentFloorPlan, state.viewerSettings, onExport]);

  if (state.isLoading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Initializing Room Configurator...</p>
      </div>
    );
  }

  if (state.error) {
    return (
      <div className={styles.error}>
        <h3>Error</h3>
        <p>{state.error}</p>
        <button onClick={() => setState(prev => ({ ...prev, error: null }))}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={styles.configurator}>
      {/* Header */}
      <div className={styles.header}>
        <h1>3D Room Configurator</h1>
        <div className={styles.modeSelector}>
          <button
            className={state.activeMode === 'design' ? styles.active : ''}
            onClick={() => setActiveMode('design')}
          >
            Design
          </button>
          <button
            className={state.activeMode === 'material' ? styles.active : ''}
            onClick={() => setActiveMode('material')}
          >
            Materials
          </button>
          <button
            className={state.activeMode === 'view' ? styles.active : ''}
            onClick={() => setActiveMode('view')}
          >
            View
          </button>
        </div>
        <button className={styles.exportBtn} onClick={handleExport}>
          Export
        </button>
      </div>

      {/* Main Content */}
      <div className={styles.content}>
        {/* Left Panel */}
        <div className={styles.leftPanel}>
          {state.activeMode === 'design' && (
            <DimensionInput
              floorPlan={state.currentFloorPlan}
              selectedRoom={state.selectedRoom}
              onCreateRoom={createRoom}
              onUpdateRoom={updateRoom}
              onDeleteRoom={deleteRoom}
              onSelectRoom={selectRoom}
            />
          )}
          
          {state.activeMode === 'material' && (
            <MaterialSelector
              catalog={state.materialCatalog}
              selectedRoom={state.selectedRoom}
              selectedCategory={state.selectedMaterialCategory}
              currentConfiguration={
                state.currentFloorPlan?.configurations.find(
                  config => config.roomId === state.selectedRoom
                )
              }
              onSelectCategory={selectMaterialCategory}
              onApplyMaterial={applyMaterial}
            />
          )}
        </div>

        {/* 3D Viewer */}
        <div className={styles.viewer}>
          <ThreeViewer
            floorPlan={state.currentFloorPlan}
            selectedRoom={state.selectedRoom}
            materialCatalog={state.materialCatalog}
            viewerSettings={state.viewerSettings}
            onRoomSelect={selectRoom}
          />
        </div>
      </div>
    </div>
  );
}
