'use client';

import React, { useEffect, useRef, useState } from 'react';
import styles from './UnityOfficeEditor.module.css';

interface MaterialOption {
  id: string;
  name: string;
  color: string;
  texture: string;
}

interface UnityOfficeEditorProps {
  onMaterialChange?: (elementType: string, material: MaterialOption) => void;
}

// Office materials for professional environment
const floorMaterials: MaterialOption[] = [
  { id: 'carpet-gray', name: 'Executive Carpet', color: '#4A4A4A', texture: 'carpet' },
  { id: 'hardwood-dark', name: 'Dark Hardwood', color: '#3C2415', texture: 'wood' },
  { id: 'marble-white', name: 'White Marble', color: '#F8F8FF', texture: 'marble' },
  { id: 'tile-black', name: 'Black Tile', color: '#1C1C1C', texture: 'tile' }
];

const wallMaterials: MaterialOption[] = [
  { id: 'paint-white', name: '<PERSON> White', color: '#F5F5F5', texture: 'paint' },
  { id: 'wood-panel', name: 'Wood Paneling', color: '#8B4513', texture: 'wood' },
  { id: 'wallpaper-gray', name: 'Gray Wallpaper', color: '#708090', texture: 'wallpaper' },
  { id: 'brick-red', name: 'Exposed Brick', color: '#B22222', texture: 'brick' }
];

const ceilingMaterials: MaterialOption[] = [
  { id: 'ceiling-white', name: 'Classic White', color: '#FFFFFF', texture: 'paint' },
  { id: 'ceiling-cream', name: 'Cream', color: '#F5F5DC', texture: 'paint' },
  { id: 'ceiling-coffered', name: 'Coffered Wood', color: '#DEB887', texture: 'wood' },
  { id: 'ceiling-modern', name: 'Modern Gray', color: '#E5E5E5', texture: 'paint' }
];

export default function UnityOfficeEditor({ onMaterialChange }: UnityOfficeEditorProps) {
  const unityContainerRef = useRef<HTMLDivElement>(null);
  const [isUnityLoaded, setIsUnityLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [unityInstance, setUnityInstance] = useState<any>(null);
  const [materialQueue, setMaterialQueue] = useState<Array<{elementType: string, material: MaterialOption}>>([]);
  const [selectedMaterials, setSelectedMaterials] = useState({
    floor: floorMaterials[0],
    walls: wallMaterials[0],
    ceiling: ceilingMaterials[0]
  });

  // Unity WebGL configuration
  const unityConfig = {
    dataUrl: "/unity/executive-office/Build/executive-office.data",
    frameworkUrl: "/unity/executive-office/Build/executive-office.framework.js",
    codeUrl: "/unity/executive-office/Build/executive-office.wasm",
    streamingAssetsUrl: "StreamingAssets",
    companyName: "Pionare",
    productName: "Executive Office Editor",
    productVersion: "1.0",
  };

  useEffect(() => {
    loadUnityWebGL();
    return () => {
      // Cleanup Unity instance on unmount
      if (unityInstance) {
        unityInstance.Quit();
      }
    };
  }, []);

  const loadUnityWebGL = async () => {
    console.log('🏢 Loading Modern Office Space image for material editing...');
    setIsLoading(true);

    try {
      // Check if Unity loader is available
      if (typeof window !== 'undefined' && (window as any).createUnityInstance) {
        const canvas = document.createElement('canvas');
        canvas.width = 1200;
        canvas.height = 600;
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        canvas.style.background = '#231F20';

        if (unityContainerRef.current) {
          unityContainerRef.current.appendChild(canvas);
        }

        const ctx = canvas.getContext('2d');
        if (!ctx) {
          console.error('❌ Cannot get canvas context');
          setIsLoading(false);
          return;
        }

        // Load the executive office image
        const img = new Image();
        img.onload = () => {
          console.log('🖼️ Modern office space image loaded, drawing to canvas...');

          // Clear canvas and draw image
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

          setIsLoading(false);
          setIsUnityLoaded(true);

          console.log('✅ Modern Office Space image rendered successfully!');
          console.log('🎨 Ready for material modifications');

          // Initialize with default materials (visual overlays)
          updateUnityMaterial('floor', selectedMaterials.floor);
          updateUnityMaterial('walls', selectedMaterials.walls);
          updateUnityMaterial('ceiling', selectedMaterials.ceiling);
        };

        img.onerror = () => {
          console.error('❌ Failed to load modern office space image');
          setIsLoading(false);
        };

        // Load the modern office space image
        img.src = '/images/rooms/modern-office-space.jpg';

      } else {
        console.log('📦 Unity loader not found, loading fallback...');
        loadUnityLoader();
      }
    } catch (error) {
      console.error('❌ Failed to load Unity WebGL:', error);
      setIsLoading(false);
      showFallbackMessage();
    }
  };

  const loadUnityLoader = () => {
    // Load Unity WebGL loader script
    const script = document.createElement('script');
    script.src = '/unity/executive-office/Build/UnityLoader.js';
    script.onload = () => {
      console.log('📦 Unity loader script loaded');
      loadUnityWebGL();
    };
    script.onerror = () => {
      console.error('❌ Failed to load Unity loader script');
      showFallbackMessage();
    };
    document.head.appendChild(script);
  };

  const showFallbackMessage = () => {
    if (unityContainerRef.current) {
      unityContainerRef.current.innerHTML = `
        <div style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          text-align: center;
          padding: 2rem;
        ">
          <h3>🏢 Executive Office Preview</h3>
          <p>Unity WebGL build will be loaded here</p>
          <p style="font-size: 0.9rem; opacity: 0.8;">
            Interactive 3D office with real-time material changes
          </p>
          <div style="
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            backdrop-filter: blur(10px);
          ">
            <p>🎯 Features:</p>
            <ul style="text-align: left; margin: 0.5rem 0;">
              <li>Real-time floor material changes</li>
              <li>Dynamic wall textures</li>
              <li>Ceiling design modifications</li>
              <li>Professional lighting</li>
              <li>Camera controls</li>
            </ul>
          </div>
        </div>
      `;
    }
  };

  const updateUnityMaterial = (elementType: string, material: MaterialOption) => {
    if (isUnityLoaded) {
      try {
        console.log(`🎨 Updating ${elementType} material to:`, material.name, `(${material.color})`);

        // Find the canvas and apply visual overlay
        const canvas = unityContainerRef.current?.querySelector('canvas') as HTMLCanvasElement;
        if (canvas) {
          applyMaterialOverlay(canvas, elementType, material);
        }

        console.log(`✅ ${elementType} material updated visually`);
      } catch (error) {
        console.error(`❌ Failed to update ${elementType} material:`, error);
      }
    } else {
      console.log(`⏳ Image not ready, queuing ${elementType} material update`);
      // Add to queue for later processing
      setMaterialQueue(prev => [...prev, { elementType, material }]);
    }
  };

  const applyMaterialOverlay = (canvas: HTMLCanvasElement, elementType: string, material: MaterialOption) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Create overlay based on element type
    ctx.save();
    ctx.globalCompositeOperation = 'multiply';
    ctx.globalAlpha = 0.3;

    // Define regions for different elements
    const width = canvas.width;
    const height = canvas.height;

    if (elementType === 'floor') {
      // Floor is bottom 30% of image
      ctx.fillStyle = material.color;
      ctx.fillRect(0, height * 0.7, width, height * 0.3);
      console.log(`🏢 Floor overlay applied: ${material.name} (${material.color})`);
    } else if (elementType === 'walls') {
      // Walls are middle 40% of image
      ctx.fillStyle = material.color;
      ctx.fillRect(0, height * 0.3, width, height * 0.4);
      console.log(`🏢 Wall overlay applied: ${material.name} (${material.color})`);
    } else if (elementType === 'ceiling') {
      // Ceiling is top 30% of image
      ctx.fillStyle = material.color;
      ctx.fillRect(0, 0, width, height * 0.3);
      console.log(`🏢 Ceiling overlay applied: ${material.name} (${material.color})`);
    }

    ctx.restore();
  };

  const processQueuedMaterials = () => {
    console.log(`🔄 Processing ${materialQueue.length} queued material updates`);
    materialQueue.forEach(({ elementType, material }) => {
      updateUnityMaterial(elementType, material);
    });
    setMaterialQueue([]); // Clear the queue
  };

  const handleMaterialChange = (elementType: string, material: MaterialOption) => {
    console.log(`🔄 Changing ${elementType} material to:`, material.name);
    
    setSelectedMaterials(prev => ({
      ...prev,
      [elementType]: material
    }));

    // Update Unity
    updateUnityMaterial(elementType, material);
    
    // Notify parent component
    if (onMaterialChange) {
      onMaterialChange(elementType, material);
    }
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h1>🏢 Unity Executive Office Editor</h1>
        <p>Professional 3D office environment with real-time interior design modifications</p>
        {isUnityLoaded && (
          <div className={styles.statusMessage}>
            ✅ Unity office loaded! Use controls below to modify the interior design.
          </div>
        )}
      </div>

      {/* Material Controls */}
      <div className={styles.controlsPanel}>
        <div className={styles.section}>
          <h3>🏗️ Office Materials</h3>

          <div className={styles.materialGroup}>
            <label>Floor Material:</label>
            <select
              value={selectedMaterials.floor.id}
              onChange={(e) => {
                const material = floorMaterials.find(m => m.id === e.target.value);
                if (material) handleMaterialChange('floor', material);
              }}
            >
              {floorMaterials.map(material => (
                <option key={material.id} value={material.id}>{material.name}</option>
              ))}
            </select>
          </div>

          <div className={styles.materialGroup}>
            <label>Wall Material:</label>
            <select
              value={selectedMaterials.walls.id}
              onChange={(e) => {
                const material = wallMaterials.find(m => m.id === e.target.value);
                if (material) handleMaterialChange('walls', material);
              }}
            >
              {wallMaterials.map(material => (
                <option key={material.id} value={material.id}>{material.name}</option>
              ))}
            </select>
          </div>

          <div className={styles.materialGroup}>
            <label>Ceiling Material:</label>
            <select
              value={selectedMaterials.ceiling.id}
              onChange={(e) => {
                const material = ceilingMaterials.find(m => m.id === e.target.value);
                if (material) handleMaterialChange('ceiling', material);
              }}
            >
              {ceilingMaterials.map(material => (
                <option key={material.id} value={material.id}>{material.name}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Unity WebGL Container */}
      <div className={styles.unityContainer}>
        <div 
          ref={unityContainerRef}
          className={styles.unityCanvas}
          style={{
            width: '100%',
            height: '600px',
            background: '#231F20',
            border: '2px solid #ccc',
            borderRadius: '8px',
            position: 'relative'
          }}
        >
          {isLoading && (
            <div className={styles.loadingOverlay}>
              <div className={styles.spinner}></div>
              <p>🎮 Loading Unity Executive Office...</p>
              <p style={{ fontSize: '0.8rem', opacity: 0.7 }}>
                Preparing 3D environment and materials
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className={styles.instructions}>
        <h4>🎮 Controls:</h4>
        <ul>
          <li><strong>Mouse:</strong> Rotate camera around office</li>
          <li><strong>Scroll:</strong> Zoom in/out</li>
          <li><strong>Materials:</strong> Use dropdowns above to change office elements</li>
          <li><strong>Real-time:</strong> Changes apply instantly to the 3D model</li>
        </ul>
      </div>
    </div>
  );
}
