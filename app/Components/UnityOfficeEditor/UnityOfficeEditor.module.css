.container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header {
  text-align: center;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.header h1 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header p {
  margin: 0;
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
}

.statusMessage {
  margin-top: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
}

.controlsPanel {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.section {
  margin-bottom: 2rem;
}

.section h3 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid #667eea;
  padding-bottom: 0.5rem;
}

.materialGroup {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.materialGroup:hover {
  background: #e3f2fd;
  border-color: #2196F3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
}

.materialGroup label {
  font-weight: 600;
  color: #495057;
  min-width: 120px;
  font-size: 1rem;
}

.materialGroup select {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #ced4da;
  border-radius: 6px;
  font-size: 1rem;
  background: white;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
}

.materialGroup select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.materialGroup select:hover {
  border-color: #adb5bd;
}

.unityContainer {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.unityCanvas {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  background: #231F20;
  min-height: 600px;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(35, 31, 32, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 10;
}

.loadingOverlay p {
  margin: 0.5rem 0;
  font-size: 1.1rem;
  text-align: center;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.instructions {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.instructions h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.instructions ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #495057;
  line-height: 1.8;
}

.instructions li {
  margin-bottom: 0.5rem;
}

.instructions strong {
  color: #667eea;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
    gap: 1rem;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .materialGroup {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .materialGroup label {
    min-width: auto;
  }
  
  .unityCanvas {
    height: 400px;
  }
}

@media (max-width: 480px) {
  .header h1 {
    font-size: 1.5rem;
  }
  
  .header p {
    font-size: 1rem;
  }
  
  .controlsPanel,
  .unityContainer,
  .instructions {
    padding: 1rem;
  }
  
  .unityCanvas {
    height: 300px;
  }
}

/* Unity Canvas Specific Styles */
.unityCanvas canvas {
  width: 100% !important;
  height: 100% !important;
  display: block;
  border-radius: 8px;
}

/* Material Preview Colors */
.materialGroup::before {
  content: '';
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 2px solid #dee2e6;
  background: var(--material-color, #f8f9fa);
  margin-right: 0.5rem;
}

/* Hover Effects */
.materialGroup:hover::before {
  border-color: #667eea;
  transform: scale(1.1);
}

/* Loading Animation */
.loadingOverlay {
  backdrop-filter: blur(5px);
}

.loadingOverlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(102, 126, 234, 0.1) 50%, transparent 70%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
