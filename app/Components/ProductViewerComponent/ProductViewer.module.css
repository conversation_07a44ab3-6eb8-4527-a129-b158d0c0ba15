.container {
  padding: 60px 30px;
  color: #0a0a0a;
  font-family: 'Inter', sans-serif;
  display: flex;
  flex-direction: column;
  gap: 30px;
  align-items: center;
  background-color: #fff;
}

.textBlock {
  max-width: 800px;
  text-align: center;
}

.textBlock h1 {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #004d1a;
}

.textBlock p {
  font-size: 16px;
  color: #444;
  line-height: 1.6;
}

.slider {
  display: flex;
  gap: 36px;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  padding: 30 30px;
  scroll-padding-left: 30px;
  justify-content: center; /* Center images on desktop */

}
.imageWrapper {
  flex: 0 0 auto;
  width: 320px;
  height: 220px;
  scroll-snap-align: center;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease-in-out;
}

.zoomed {
  transform: scale(1.15);
  z-index: 2;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  transition: transform 0.4s ease;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Dot Indicator */
.dots {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 10px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ccc;
  transition: background 0.3s;
}

.dot.active {
  background: #004d1a;
}

/* Mobile Responsive */
@media (max-width: 768px) {

  .slider {
    justify-content: flex-start; /* Allow horizontal scroll on mobile */
  }
  .imageWrapper {
    width: 280px;
    height: 180px;
  }

  .zoomed {
    transform: scale(1.25);
  }

  .textBlock h1 {
    font-size: 24px;
  }

  .textBlock p {
    font-size: 14px;
  }

}
