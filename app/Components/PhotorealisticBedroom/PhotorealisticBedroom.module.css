.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.header {
  text-align: center;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.2rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header p {
  margin: 0;
  font-size: 1rem;
  opacity: 0.9;
  color: #e0e0e0;
}

.controlsPanel {
  position: absolute;
  top: 120px;
  left: 1rem;
  z-index: 100;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 215, 0, 0.3);
  max-width: 320px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.section {
  margin-bottom: 2rem;
}

.section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #FFD700;
  border-bottom: 1px solid rgba(255, 215, 0, 0.3);
  padding-bottom: 0.5rem;
}

.inputGroup {
  margin-bottom: 1rem;
}

.inputGroup label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #e0e0e0;
}

.inputGroup input[type="range"] {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  -webkit-appearance: none;
}

.inputGroup input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  cursor: pointer;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
}

.inputGroup input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  cursor: pointer;
  border: none;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
}

.materialGroup {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.materialGroup label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #e0e0e0;
  min-width: 70px;
}

.materialGroup select {
  flex: 1;
  padding: 0.6rem;
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.materialGroup select:focus {
  outline: none;
  border-color: #FFD700;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.materialGroup select option {
  background: #2d2d2d;
  color: white;
}

.toggleControls {
  position: absolute;
  top: 120px;
  right: 1rem;
  z-index: 100;
  padding: 0.8rem 1.8rem;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: #1a1a1a;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 700;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.toggleControls:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.6);
}

.sceneContainer {
  flex: 1;
  position: relative;
  min-height: calc(100vh - 120px);
  height: calc(100vh - 120px);
  width: 100%;
}

.canvas {
  width: 100% !important;
  height: 100% !important;
  border-radius: 0;
  display: block;
  background: linear-gradient(135deg, #87CEEB 0%, #98D8E8 100%);
}

.sceneInfo {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.85);
  padding: 0.8rem 1.8rem;
  border-radius: 25px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 215, 0, 0.3);
  z-index: 50;
}

.sceneInfo span {
  font-size: 0.9rem;
  font-weight: 500;
  color: #e0e0e0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .controlsPanel {
    position: relative;
    top: auto;
    left: auto;
    max-width: none;
    margin: 1rem;
    display: flex;
    gap: 2rem;
    overflow-x: auto;
  }
  
  .section {
    min-width: 280px;
    margin-bottom: 0;
  }
  
  .toggleControls {
    position: relative;
    top: auto;
    right: auto;
    margin: 1rem auto;
    display: block;
  }
}

@media (max-width: 768px) {
  .header h1 {
    font-size: 1.8rem;
  }
  
  .controlsPanel {
    flex-direction: column;
    gap: 1rem;
  }
  
  .section {
    min-width: auto;
  }
  
  .materialGroup {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .materialGroup label {
    min-width: auto;
  }
  
  .materialGroup select {
    width: 100%;
  }
  
  .sceneContainer {
    min-height: calc(100vh - 200px);
    height: calc(100vh - 200px);
  }
}

@media (max-width: 480px) {
  .header {
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 1.5rem;
  }
  
  .controlsPanel {
    margin: 0.5rem;
    padding: 1rem;
  }
  
  .section h3 {
    font-size: 1rem;
  }
  
  .inputGroup label,
  .materialGroup label {
    font-size: 0.8rem;
  }
  
  .materialGroup select {
    font-size: 0.8rem;
    padding: 0.5rem;
  }
  
  .toggleControls {
    padding: 0.7rem 1.5rem;
    font-size: 0.8rem;
  }
  
  .sceneContainer {
    min-height: calc(100vh - 250px);
    height: calc(100vh - 250px);
  }
}

/* Loading and Performance */
.canvas {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Smooth transitions for all interactive elements */
.inputGroup input,
.materialGroup select,
.toggleControls {
  transition: all 0.3s ease;
}

/* Focus states for accessibility */
.inputGroup input:focus,
.materialGroup select:focus,
.toggleControls:focus {
  outline: 2px solid #FFD700;
  outline-offset: 2px;
}

/* Photorealistic theme enhancements */
.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.05) 0%, transparent 70%);
  pointer-events: none;
  z-index: 1;
}

.controlsPanel,
.toggleControls,
.sceneInfo {
  position: relative;
  z-index: 2;
}
