'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Engine, Scene, Vector3, Hemis<PERSON>Light, DirectionalLight, ArcRotateCamera, MeshBuilder, StandardMaterial, Color3, Texture, PBRMaterial, ShadowGenerator, SpotLight, PointLight } from '@babylonjs/core';
import '@babylonjs/loaders';
import styles from './PhotorealisticBedroom.module.css';

interface RoomDimensions {
  length: number;
  width: number;
  height: number;
}

interface MaterialOptions {
  floor: string;
  walls: string;
  bed: string;
  ceiling: string;
}

// Photorealistic material configurations
const materialConfigs = {
  floor: {
    'oak-hardwood': { 
      baseColor: '#D2B48C', 
      roughness: 0.8, 
      metallic: 0.0,
      normalIntensity: 1.0,
      emissive: '#000000'
    },
    'premium-carpet': { 
      baseColor: '#8B7355', 
      roughness: 0.95, 
      metallic: 0.0,
      normalIntensity: 1.5,
      emissive: '#000000'
    },
    'white-marble': { 
      baseColor: '#F8F8FF', 
      roughness: 0.1, 
      metallic: 0.0,
      normalIntensity: 0.8,
      emissive: '#111111'
    },
    'luxury-vinyl': { 
      baseColor: '#8B4513', 
      roughness: 0.6, 
      metallic: 0.0,
      normalIntensity: 1.2,
      emissive: '#000000'
    }
  },
  walls: {
    'white-paint': { 
      baseColor: '#FFF8DC', 
      roughness: 0.9, 
      metallic: 0.0,
      normalIntensity: 0.5,
      emissive: '#000000'
    },
    'wood-paneling': { 
      baseColor: '#8B4513', 
      roughness: 0.7, 
      metallic: 0.0,
      normalIntensity: 1.3,
      emissive: '#000000'
    },
    'modern-wallpaper': { 
      baseColor: '#F5F5DC', 
      roughness: 0.8, 
      metallic: 0.0,
      normalIntensity: 0.7,
      emissive: '#000000'
    },
    'exposed-brick': { 
      baseColor: '#A0522D', 
      roughness: 0.9, 
      metallic: 0.0,
      normalIntensity: 2.0,
      emissive: '#000000'
    }
  },
  ceiling: {
    'smooth-gypsum': { 
      baseColor: '#FFFFFF', 
      roughness: 0.8, 
      metallic: 0.0,
      normalIntensity: 0.3,
      emissive: '#000000'
    },
    'wood-planks': { 
      baseColor: '#8B4513', 
      roughness: 0.7, 
      metallic: 0.0,
      normalIntensity: 1.0,
      emissive: '#000000'
    },
    'textured-gypsum': { 
      baseColor: '#F8F8FF', 
      roughness: 0.9, 
      metallic: 0.0,
      normalIntensity: 1.5,
      emissive: '#000000'
    },
    'suspended-grid': { 
      baseColor: '#E0E0E0', 
      roughness: 0.6, 
      metallic: 0.1,
      normalIntensity: 0.8,
      emissive: '#000000'
    }
  },
  bed: {
    'executive-leather': { 
      baseColor: '#654321', 
      roughness: 0.3, 
      metallic: 0.0,
      normalIntensity: 0.8,
      emissive: '#000000'
    },
    'luxury-fabric': { 
      baseColor: '#2F4F4F', 
      roughness: 0.8, 
      metallic: 0.0,
      normalIntensity: 1.2,
      emissive: '#000000'
    },
    'modern-wood': { 
      baseColor: '#8B4513', 
      roughness: 0.6, 
      metallic: 0.0,
      normalIntensity: 1.0,
      emissive: '#000000'
    }
  }
};

export default function PhotorealisticBedroom() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<Engine | null>(null);
  const sceneRef = useRef<Scene | null>(null);
  
  const [dimensions, setDimensions] = useState<RoomDimensions>({ 
    length: 16, 
    width: 14, 
    height: 10 
  });
  
  const [materials, setMaterials] = useState<MaterialOptions>({
    floor: 'oak-hardwood',
    walls: 'white-paint',
    bed: 'executive-leather',
    ceiling: 'smooth-gypsum'
  });

  const [showControls, setShowControls] = useState(true);

  useEffect(() => {
    if (!canvasRef.current) {
      console.error('Canvas ref is null');
      return;
    }

    console.log('Initializing Babylon.js...');

    try {
      // Initialize Babylon.js engine
      const engine = new Engine(canvasRef.current, true, {
        antialias: true,
        stencil: true,
        preserveDrawingBuffer: true,
        powerPreference: "high-performance"
      });

      console.log('Engine created successfully');
      engineRef.current = engine;

      // Force resize to ensure proper canvas dimensions
      engine.resize();

      // Create scene
      const scene = new Scene(engine);
      console.log('Scene created successfully');
      sceneRef.current = scene;

      // Setup photorealistic rendering
      console.log('Setting up photorealistic scene...');
      setupPhotorealisticScene(scene, dimensions, materials);
      console.log('Scene setup complete');

      // Render loop
      engine.runRenderLoop(() => {
        scene.render();
      });
      console.log('Render loop started');

      // Handle resize
      const handleResize = () => {
        engine.resize();
      };
      window.addEventListener('resize', handleResize);

      return () => {
        console.log('Cleaning up Babylon.js...');
        window.removeEventListener('resize', handleResize);
        engine.dispose();
      };
    } catch (error) {
      console.error('Error initializing Babylon.js:', error);
    }
  }, []);

  // Update scene when materials or dimensions change
  useEffect(() => {
    if (sceneRef.current) {
      updateSceneMaterials(sceneRef.current, materials);
    }
  }, [materials]);

  useEffect(() => {
    if (sceneRef.current) {
      updateSceneDimensions(sceneRef.current, dimensions);
    }
  }, [dimensions]);

  const handleDimensionChange = (field: keyof RoomDimensions, value: number) => {
    setDimensions(prev => ({ ...prev, [field]: value }));
  };

  const handleMaterialChange = (type: keyof MaterialOptions, value: string) => {
    setMaterials(prev => ({ ...prev, [type]: value }));
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h1>📸 Photorealistic Executive Bedroom</h1>
        <p>Babylon.js powered photorealistic rendering - looks like a real photograph</p>
      </div>

      {/* Controls */}
      {showControls && (
        <div className={styles.controlsPanel}>
          {/* Dimensions */}
          <div className={styles.section}>
            <h3>📏 Dimensions (feet)</h3>
            <div className={styles.inputGroup}>
              <label>Length: {dimensions.length}'</label>
              <input
                type="range"
                min="10"
                max="20"
                value={dimensions.length}
                onChange={(e) => handleDimensionChange('length', Number(e.target.value))}
              />
            </div>
            <div className={styles.inputGroup}>
              <label>Width: {dimensions.width}'</label>
              <input
                type="range"
                min="8"
                max="16"
                value={dimensions.width}
                onChange={(e) => handleDimensionChange('width', Number(e.target.value))}
              />
            </div>
            <div className={styles.inputGroup}>
              <label>Height: {dimensions.height}'</label>
              <input
                type="range"
                min="8"
                max="12"
                value={dimensions.height}
                onChange={(e) => handleDimensionChange('height', Number(e.target.value))}
              />
            </div>
          </div>

          {/* Materials */}
          <div className={styles.section}>
            <h3>🎨 Photorealistic Materials</h3>
            <div className={styles.materialGroup}>
              <label>Floor:</label>
              <select 
                value={materials.floor} 
                onChange={(e) => handleMaterialChange('floor', e.target.value)}
              >
                <option value="oak-hardwood">Oak Hardwood</option>
                <option value="premium-carpet">Premium Carpet</option>
                <option value="white-marble">White Marble</option>
                <option value="luxury-vinyl">Luxury Vinyl</option>
              </select>
            </div>
            <div className={styles.materialGroup}>
              <label>Walls:</label>
              <select 
                value={materials.walls} 
                onChange={(e) => handleMaterialChange('walls', e.target.value)}
              >
                <option value="white-paint">White Paint</option>
                <option value="wood-paneling">Wood Paneling</option>
                <option value="modern-wallpaper">Modern Wallpaper</option>
                <option value="exposed-brick">Exposed Brick</option>
              </select>
            </div>
            <div className={styles.materialGroup}>
              <label>Ceiling:</label>
              <select 
                value={materials.ceiling} 
                onChange={(e) => handleMaterialChange('ceiling', e.target.value)}
              >
                <option value="smooth-gypsum">Smooth Gypsum</option>
                <option value="wood-planks">Wood Planks</option>
                <option value="textured-gypsum">Textured Gypsum</option>
                <option value="suspended-grid">Suspended Grid</option>
              </select>
            </div>
            <div className={styles.materialGroup}>
              <label>Bed:</label>
              <select 
                value={materials.bed} 
                onChange={(e) => handleMaterialChange('bed', e.target.value)}
              >
                <option value="executive-leather">Executive Leather</option>
                <option value="luxury-fabric">Luxury Fabric</option>
                <option value="modern-wood">Modern Wood</option>
              </select>
            </div>
          </div>
        </div>
      )}

      <button 
        className={styles.toggleControls}
        onClick={() => setShowControls(!showControls)}
      >
        {showControls ? 'Hide Controls' : 'Show Controls'}
      </button>

      {/* Babylon.js Canvas */}
      <div className={styles.sceneContainer}>
        <canvas
          ref={canvasRef}
          className={styles.canvas}
          width={800}
          height={600}
          style={{
            backgroundColor: '#87CEEB',
            border: '2px solid #FFD700',
            width: '100%',
            height: '100%'
          }}
        />
        <div className={styles.sceneInfo}>
          <span>🖱️ Left-click to rotate • Right-click to pan • Scroll to zoom</span>
        </div>
      </div>
    </div>
  );
}

// Photorealistic Scene Setup Functions
function setupPhotorealisticScene(scene: Scene, dimensions: RoomDimensions, materials: MaterialOptions) {
  try {
    console.log('Clearing existing scene...');
    // Clear existing meshes
    scene.meshes.forEach(mesh => mesh.dispose());
    scene.lights.forEach(light => light.dispose());
    scene.cameras.forEach(camera => camera.dispose());

    console.log('Setting up camera...');
    // Setup photorealistic camera
    const camera = new ArcRotateCamera("camera", -Math.PI / 2, Math.PI / 3, 15, Vector3.Zero(), scene);
    camera.setTarget(new Vector3(0, 0, 0));

    // Attach controls to canvas - CORRECT BABYLON.JS SYNTAX
    const canvas = scene.getEngine().getRenderingCanvas();
    if (canvas) {
      camera.attachControl(canvas, true);
      console.log('Camera controls attached');
    } else {
      console.error('Canvas not found for camera controls');
    }

    // Advanced camera settings for photorealism
    camera.wheelPrecision = 50;
    camera.minZ = 0.1;
    camera.maxZ = 1000;
    camera.fov = 0.8; // More realistic field of view
    camera.inertia = 0.9; // Smooth camera movement
    camera.angularSensibilityX = 1000;
    camera.angularSensibilityY = 1000;
    camera.panningSensibility = 1000;

    console.log('Setting up lighting...');
    // Setup photorealistic lighting
    setupPhotorealisticLighting(scene);

    console.log('Creating room geometry...');
    // Create room geometry
    createPhotorealisticRoom(scene, dimensions, materials);

    console.log('Creating furniture...');
    // Create executive furniture
    createExecutiveFurniture(scene, dimensions, materials);

    console.log('Enabling post-processing...');
    // Enable post-processing for photorealism
    enablePhotorealisticPostProcessing(scene);

    console.log('Scene setup completed successfully');

  } catch (error) {
    console.error("Error setting up photorealistic scene:", error);
  }
}

function setupPhotorealisticLighting(scene: Scene) {
  // Advanced environment lighting for photorealism
  scene.environmentIntensity = 1.2;

  // Create realistic sky environment with proper error handling
  try {
    const skybox = scene.createDefaultSkybox(null, true, 1000, 0.3);
    if (!skybox) {
      scene.clearColor = new Color3(0.8, 0.9, 1.0);
    }
  } catch (error) {
    console.log("Skybox creation failed, using clear color");
    scene.clearColor = new Color3(0.8, 0.9, 1.0);
  }

  // Main directional light (sun/window light) - Enhanced for photorealism
  const sunLight = new DirectionalLight("sunLight", new Vector3(-0.8, -1, -0.3), scene);
  sunLight.intensity = 4.0;
  sunLight.diffuse = new Color3(1, 0.95, 0.85); // Natural sunlight color
  sunLight.specular = new Color3(1, 1, 1);

  // Advanced shadow generator for ultra-realistic shadows
  const shadowGenerator = new ShadowGenerator(4096, sunLight); // Higher resolution
  shadowGenerator.useExponentialShadowMap = true;
  shadowGenerator.useKernelBlur = true;
  shadowGenerator.blurKernel = 64;
  shadowGenerator.darkness = 0.4;
  shadowGenerator.depthScale = 50;
  shadowGenerator.bias = 0.00001;

  // Secondary directional light for fill lighting
  const fillLight = new DirectionalLight("fillLight", new Vector3(0.5, -0.8, 0.6), scene);
  fillLight.intensity = 1.5;
  fillLight.diffuse = new Color3(0.7, 0.8, 1.0); // Cool fill light
  fillLight.specular = new Color3(0.5, 0.5, 0.5);

  // Ambient light for global illumination simulation
  const ambientLight = new HemisphericLight("ambientLight", new Vector3(0, 1, 0), scene);
  ambientLight.intensity = 0.4;
  ambientLight.diffuse = new Color3(0.6, 0.7, 0.9); // Sky color
  ambientLight.groundColor = new Color3(0.3, 0.3, 0.2); // Ground bounce

  // Interior ceiling lights for realistic room lighting
  const ceilingLight1 = new PointLight("ceilingLight1", new Vector3(-3, 9, 0), scene);
  ceilingLight1.intensity = 3.0;
  ceilingLight1.diffuse = new Color3(1, 0.9, 0.7); // Warm LED
  ceilingLight1.range = 15;
  ceilingLight1.falloffType = PointLight.FALLOFF_PHYSICAL;

  const ceilingLight2 = new PointLight("ceilingLight2", new Vector3(3, 9, 0), scene);
  ceilingLight2.intensity = 3.0;
  ceilingLight2.diffuse = new Color3(1, 0.9, 0.7);
  ceilingLight2.range = 15;
  ceilingLight2.falloffType = PointLight.FALLOFF_PHYSICAL;

  // Nightstand lamps with realistic spot lighting
  const lamp1 = new SpotLight("lamp1", new Vector3(-4.5, 4, 2), new Vector3(0, -1, 0.2), Math.PI / 4, 3, scene);
  lamp1.intensity = 2.5;
  lamp1.diffuse = new Color3(1, 0.8, 0.5); // Warm lamp light
  lamp1.specular = new Color3(0.8, 0.6, 0.4);
  lamp1.falloffType = SpotLight.FALLOFF_PHYSICAL;

  const lamp2 = new SpotLight("lamp2", new Vector3(4.5, 4, 2), new Vector3(0, -1, 0.2), Math.PI / 4, 3, scene);
  lamp2.intensity = 2.5;
  lamp2.diffuse = new Color3(1, 0.8, 0.5);
  lamp2.specular = new Color3(0.8, 0.6, 0.4);
  lamp2.falloffType = SpotLight.FALLOFF_PHYSICAL;

  // Desk lamp for workspace
  const deskLamp = new SpotLight("deskLamp", new Vector3(8, 5, 6), new Vector3(-0.3, -1, -0.2), Math.PI / 6, 2, scene);
  deskLamp.intensity = 3.0;
  deskLamp.diffuse = new Color3(1, 1, 0.9); // Cool white LED
  deskLamp.specular = new Color3(1, 1, 1);
  deskLamp.falloffType = SpotLight.FALLOFF_PHYSICAL;

  // Window light simulation
  const windowLight = new SpotLight("windowLight", new Vector3(-8, 6, 8), new Vector3(1, -0.5, -1), Math.PI / 2, 1, scene);
  windowLight.intensity = 5.0;
  windowLight.diffuse = new Color3(0.9, 0.95, 1.0); // Natural daylight
  windowLight.specular = new Color3(1, 1, 1);
  windowLight.falloffType = SpotLight.FALLOFF_PHYSICAL;

  // Add all shadow casters to shadow generator
  const shadowCasters = scene.meshes.filter(mesh =>
    mesh.name.includes('bed') ||
    mesh.name.includes('nightstand') ||
    mesh.name.includes('dresser') ||
    mesh.name.includes('desk') ||
    mesh.name.includes('chair')
  );

  shadowCasters.forEach(mesh => {
    shadowGenerator.addShadowCaster(mesh);
  });
}

function createPhotorealisticMaterial(scene: Scene, materialType: keyof typeof materialConfigs, materialName: string): PBRMaterial {
  const config = materialConfigs[materialType][materialName as keyof typeof materialConfigs[typeof materialType]];

  const material = new PBRMaterial(`${materialType}_${materialName}`, scene);

  // Advanced PBR properties for ultra-photorealism
  material.baseColor = Color3.FromHexString(config.baseColor);
  material.roughness = config.roughness;
  material.metallic = config.metallic;
  material.emissiveColor = Color3.FromHexString(config.emissive);

  // Enable all advanced PBR features for maximum realism
  material.enableSpecularAntiAliasing = true;
  material.useRadianceOverAlpha = true;
  material.useSpecularOverAlpha = true;
  material.usePhysicalLightFalloff = true;
  material.useGLTFLightFalloff = false;

  // Advanced material properties based on type
  if (materialType === 'floor') {
    if (materialName === 'white-marble') {
      // Marble properties
      material.clearCoat.isEnabled = true;
      material.clearCoat.intensity = 0.8;
      material.clearCoat.roughness = 0.1;
      material.indexOfRefraction = 1.5;
      material.subSurface.isScatteringEnabled = true;
      material.subSurface.scatteringStrength = 0.3;
    } else if (materialName === 'oak-hardwood') {
      // Wood properties
      material.anisotropy.isEnabled = true;
      material.anisotropy.intensity = 0.3;
      material.anisotropy.direction = new Vector3(1, 0, 0);
      material.subSurface.isScatteringEnabled = true;
      material.subSurface.scatteringStrength = 0.1;
    } else if (materialName === 'premium-carpet') {
      // Fabric properties
      material.sheen.isEnabled = true;
      material.sheen.intensity = 0.4;
      material.sheen.color = Color3.FromHexString("#FFFFFF");
      material.roughness = 0.95;
    }
  } else if (materialType === 'walls') {
    if (materialName === 'exposed-brick') {
      // Brick properties
      material.roughness = 0.9;
      material.subSurface.isScatteringEnabled = true;
      material.subSurface.scatteringStrength = 0.2;
    } else if (materialName === 'wood-paneling') {
      // Wood paneling
      material.anisotropy.isEnabled = true;
      material.anisotropy.intensity = 0.2;
      material.clearCoat.isEnabled = true;
      material.clearCoat.intensity = 0.3;
    }
  } else if (materialType === 'bed') {
    if (materialName === 'executive-leather') {
      // Leather properties
      material.clearCoat.isEnabled = true;
      material.clearCoat.intensity = 0.6;
      material.clearCoat.roughness = 0.2;
      material.subSurface.isScatteringEnabled = true;
      material.subSurface.scatteringStrength = 0.4;
    } else if (materialName === 'luxury-fabric') {
      // Fabric properties
      material.sheen.isEnabled = true;
      material.sheen.intensity = 0.5;
      material.sheen.color = Color3.FromHexString("#FFFFFF");
    }
  }

  // Add subtle color variation for realism
  material.albedoColor = material.baseColor.scale(1.1);

  // Enable real-time reflections
  material.realTimeFiltering = true;
  material.realTimeFilteringQuality = 8;

  // Add procedural noise for surface variation
  try {
    // Create a simple procedural texture for surface detail
    const proceduralTexture = new Texture("data:image/svg+xml;base64," + btoa(`
      <svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <filter id="noise">
            <feTurbulence baseFrequency="0.9" numOctaves="4" result="noise"/>
            <feColorMatrix in="noise" type="saturate" values="0"/>
            <feComponentTransfer>
              <feFuncA type="discrete" tableValues="0.5 0.6 0.7 0.8"/>
            </feComponentTransfer>
          </filter>
        </defs>
        <rect width="100%" height="100%" fill="${config.baseColor}" filter="url(#noise)" opacity="0.1"/>
      </svg>
    `), scene);

    proceduralTexture.uScale = materialType === 'floor' ? 8 : 4;
    proceduralTexture.vScale = materialType === 'floor' ? 8 : 4;
    proceduralTexture.wrapU = 1; // Mirror mode
    proceduralTexture.wrapV = 1; // Mirror mode

    // Use as bump map for surface detail
    material.bumpTexture = proceduralTexture;
    material.invertNormalMapX = false;
    material.invertNormalMapY = false;
    material.useParallax = true;
    material.useParallaxOcclusion = true;
    material.parallaxScaleBias = 0.1;

  } catch (error) {
    console.log("Procedural texture generation failed, using solid material");
  }

  console.log(`Created ultra-photorealistic material for ${materialType}_${materialName}`);

  return material;
}

function createPhotorealisticRoom(scene: Scene, dimensions: RoomDimensions, materials: MaterialOptions) {
  const length = dimensions.length * 1.2;
  const width = dimensions.width * 1.2;
  const height = dimensions.height * 1.2;

  // Floor
  const floor = MeshBuilder.CreateGround("floor", { width: length, height: width }, scene);
  floor.material = createPhotorealisticMaterial(scene, 'floor', materials.floor);
  floor.receiveShadows = true;

  // Walls
  const backWall = MeshBuilder.CreatePlane("backWall", { width: length, height: height }, scene);
  backWall.position = new Vector3(0, height / 2, -width / 2);
  backWall.material = createPhotorealisticMaterial(scene, 'walls', materials.walls);
  backWall.receiveShadows = true;

  const leftWall = MeshBuilder.CreatePlane("leftWall", { width: width, height: height }, scene);
  leftWall.position = new Vector3(-length / 2, height / 2, 0);
  leftWall.rotation.y = Math.PI / 2;
  leftWall.material = createPhotorealisticMaterial(scene, 'walls', materials.walls);
  leftWall.receiveShadows = true;

  const rightWall = MeshBuilder.CreatePlane("rightWall", { width: width, height: height }, scene);
  rightWall.position = new Vector3(length / 2, height / 2, 0);
  rightWall.rotation.y = -Math.PI / 2;
  rightWall.material = createPhotorealisticMaterial(scene, 'walls', materials.walls);
  rightWall.receiveShadows = true;

  // Ceiling
  const ceiling = MeshBuilder.CreateGround("ceiling", { width: length, height: width }, scene);
  ceiling.position = new Vector3(0, height, 0);
  ceiling.rotation.x = Math.PI;
  ceiling.material = createPhotorealisticMaterial(scene, 'ceiling', materials.ceiling);
  ceiling.receiveShadows = true;
}

function createExecutiveFurniture(scene: Scene, dimensions: RoomDimensions, materials: MaterialOptions) {
  const length = dimensions.length * 1.2;
  const width = dimensions.width * 1.2;

  // Executive King Size Bed
  const bedFrame = MeshBuilder.CreateBox("bedFrame", { width: 7.0, height: 0.4, depth: 5.0 }, scene);
  bedFrame.position = new Vector3(0, 0.2, width * 0.05);
  bedFrame.material = createPhotorealisticMaterial(scene, 'bed', materials.bed);
  bedFrame.receiveShadows = true;

  // Mattress
  const mattress = MeshBuilder.CreateBox("mattress", { width: 6.7, height: 0.6, depth: 4.7 }, scene);
  mattress.position = new Vector3(0, 0.7, width * 0.05);
  const mattressMaterial = new PBRMaterial("mattressMaterial", scene);
  mattressMaterial.baseColor = Color3.FromHexString("#F8F8FF");
  mattressMaterial.roughness = 0.8;
  mattressMaterial.metallic = 0.0;
  mattress.material = mattressMaterial;
  mattress.receiveShadows = true;

  // Executive Headboard
  const headboard = MeshBuilder.CreateBox("headboard", { width: 7.5, height: 4.0, depth: 0.4 }, scene);
  headboard.position = new Vector3(0, 2.2, width * 0.05 - 2.5);
  headboard.material = createPhotorealisticMaterial(scene, 'bed', materials.bed);
  headboard.receiveShadows = true;

  // Nightstands
  const nightstand1 = MeshBuilder.CreateBox("nightstand1", { width: 1.8, height: 2.2, depth: 1.5 }, scene);
  nightstand1.position = new Vector3(-4.5, 1.1, width * 0.05);
  nightstand1.material = createPhotorealisticMaterial(scene, 'bed', materials.bed);
  nightstand1.receiveShadows = true;

  const nightstand2 = MeshBuilder.CreateBox("nightstand2", { width: 1.8, height: 2.2, depth: 1.5 }, scene);
  nightstand2.position = new Vector3(4.5, 1.1, width * 0.05);
  nightstand2.material = createPhotorealisticMaterial(scene, 'bed', materials.bed);
  nightstand2.receiveShadows = true;

  // Executive Dresser
  const dresser = MeshBuilder.CreateBox("dresser", { width: 5.0, height: 3.0, depth: 1.8 }, scene);
  dresser.position = new Vector3(0, 1.5, -width * 0.4);
  dresser.material = createPhotorealisticMaterial(scene, 'bed', materials.bed);
  dresser.receiveShadows = true;

  // Mirror
  const mirror = MeshBuilder.CreateBox("mirror", { width: 3.5, height: 2.5, depth: 0.1 }, scene);
  mirror.position = new Vector3(0, 4.8, -width * 0.4 - 0.1);
  const mirrorMaterial = new PBRMaterial("mirrorMaterial", scene);
  mirrorMaterial.baseColor = Color3.FromHexString("#E6E6FA");
  mirrorMaterial.roughness = 0.0;
  mirrorMaterial.metallic = 0.9;
  mirrorMaterial.clearCoat.isEnabled = true;
  mirrorMaterial.clearCoat.intensity = 1.0;
  mirror.material = mirrorMaterial;
  mirror.receiveShadows = true;

  // Executive Desk
  const desk = MeshBuilder.CreateBox("desk", { width: 3.5, height: 0.1, depth: 2.0 }, scene);
  desk.position = new Vector3(length * 0.35, 2.4, width * 0.35);
  desk.material = createPhotorealisticMaterial(scene, 'bed', materials.bed);
  desk.receiveShadows = true;

  // Desk legs
  for (let i = 0; i < 4; i++) {
    const leg = MeshBuilder.CreateBox(`deskLeg${i}`, { width: 0.2, height: 2.4, depth: 0.2 }, scene);
    const xPos = length * 0.35 + (i % 2 === 0 ? -1.6 : 1.6);
    const zPos = width * 0.35 + (i < 2 ? -0.8 : 0.8);
    leg.position = new Vector3(xPos, 1.2, zPos);
    leg.material = createPhotorealisticMaterial(scene, 'bed', materials.bed);
    leg.receiveShadows = true;
  }

  // Executive Chair
  const chairSeat = MeshBuilder.CreateBox("chairSeat", { width: 1.2, height: 0.1, depth: 1.2 }, scene);
  chairSeat.position = new Vector3(length * 0.35, 1.8, width * 0.25);
  const chairMaterial = new PBRMaterial("chairMaterial", scene);
  chairMaterial.baseColor = Color3.FromHexString("#2F4F4F");
  chairMaterial.roughness = 0.7;
  chairMaterial.metallic = 0.0;
  chairSeat.material = chairMaterial;
  chairSeat.receiveShadows = true;

  const chairBack = MeshBuilder.CreateBox("chairBack", { width: 1.0, height: 2.0, depth: 0.2 }, scene);
  chairBack.position = new Vector3(length * 0.35, 2.8, width * 0.25 - 0.4);
  chairBack.material = chairMaterial;
  chairBack.receiveShadows = true;

  // Add realistic details - Pillows
  const pillow1 = MeshBuilder.CreateBox("pillow1", { width: 1.2, height: 0.3, depth: 0.8 }, scene);
  pillow1.position = new Vector3(-2.0, 1.2, width * 0.05 - 1.8);
  const pillowMaterial = new PBRMaterial("pillowMaterial", scene);
  pillowMaterial.baseColor = Color3.FromHexString("#FFFFFF");
  pillowMaterial.roughness = 0.9;
  pillowMaterial.metallic = 0.0;
  pillowMaterial.sheen.isEnabled = true;
  pillowMaterial.sheen.intensity = 0.3;
  pillow1.material = pillowMaterial;
  pillow1.receiveShadows = true;

  const pillow2 = MeshBuilder.CreateBox("pillow2", { width: 1.2, height: 0.3, depth: 0.8 }, scene);
  pillow2.position = new Vector3(2.0, 1.2, width * 0.05 - 1.8);
  pillow2.material = pillowMaterial;
  pillow2.receiveShadows = true;

  // Add bedside lamps
  const lampBase1 = MeshBuilder.CreateCylinder("lampBase1", { height: 1.0, diameter: 0.3 }, scene);
  lampBase1.position = new Vector3(-4.5, 2.8, width * 0.05);
  const lampBaseMaterial = new PBRMaterial("lampBaseMaterial", scene);
  lampBaseMaterial.baseColor = Color3.FromHexString("#8B4513");
  lampBaseMaterial.roughness = 0.3;
  lampBaseMaterial.metallic = 0.1;
  lampBase1.material = lampBaseMaterial;
  lampBase1.receiveShadows = true;

  const lampShade1 = MeshBuilder.CreateCylinder("lampShade1", { height: 0.6, diameterTop: 0.8, diameterBottom: 0.6 }, scene);
  lampShade1.position = new Vector3(-4.5, 3.5, width * 0.05);
  const lampShadeMaterial = new PBRMaterial("lampShadeMaterial", scene);
  lampShadeMaterial.baseColor = Color3.FromHexString("#F5DEB3");
  lampShadeMaterial.roughness = 0.9;
  lampShadeMaterial.metallic = 0.0;
  lampShadeMaterial.emissiveColor = Color3.FromHexString("#FFF8DC").scale(0.2);
  lampShade1.material = lampShadeMaterial;
  lampShade1.receiveShadows = true;

  // Mirror lamp
  const lampBase2 = MeshBuilder.CreateCylinder("lampBase2", { height: 1.0, diameter: 0.3 }, scene);
  lampBase2.position = new Vector3(4.5, 2.8, width * 0.05);
  lampBase2.material = lampBaseMaterial;
  lampBase2.receiveShadows = true;

  const lampShade2 = MeshBuilder.CreateCylinder("lampShade2", { height: 0.6, diameterTop: 0.8, diameterBottom: 0.6 }, scene);
  lampShade2.position = new Vector3(4.5, 3.5, width * 0.05);
  lampShade2.material = lampShadeMaterial;
  lampShade2.receiveShadows = true;
}

function enablePhotorealisticPostProcessing(scene: Scene) {
  // Ultra-realistic tone mapping and exposure
  scene.imageProcessingConfiguration.toneMappingEnabled = true;
  scene.imageProcessingConfiguration.toneMappingType = 1; // ACES tone mapping for cinema-quality
  scene.imageProcessingConfiguration.exposure = 1.3;

  // Advanced contrast and saturation for photorealism
  scene.imageProcessingConfiguration.contrast = 1.15;
  scene.imageProcessingConfiguration.colorGradingEnabled = true;
  scene.imageProcessingConfiguration.colorCurvesEnabled = true;

  // Realistic color curves
  if (scene.imageProcessingConfiguration.colorCurves) {
    scene.imageProcessingConfiguration.colorCurves.globalHue = 0;
    scene.imageProcessingConfiguration.colorCurves.globalDensity = 0;
    scene.imageProcessingConfiguration.colorCurves.globalSaturation = 10; // Slight saturation boost
    scene.imageProcessingConfiguration.colorCurves.globalExposure = 0;

    // Shadow/highlight adjustments
    scene.imageProcessingConfiguration.colorCurves.shadowsHue = 0;
    scene.imageProcessingConfiguration.colorCurves.shadowsDensity = 0;
    scene.imageProcessingConfiguration.colorCurves.shadowsSaturation = 5;
    scene.imageProcessingConfiguration.colorCurves.shadowsExposure = 0;

    scene.imageProcessingConfiguration.colorCurves.highlightsHue = 0;
    scene.imageProcessingConfiguration.colorCurves.highlightsDensity = 0;
    scene.imageProcessingConfiguration.colorCurves.highlightsSaturation = -5;
    scene.imageProcessingConfiguration.colorCurves.highlightsExposure = 0;
  }

  // Realistic vignette effect
  scene.imageProcessingConfiguration.vignetteEnabled = true;
  scene.imageProcessingConfiguration.vignetteWeight = 0.4;
  scene.imageProcessingConfiguration.vignetteStretch = 0.2;
  scene.imageProcessingConfiguration.vignetteCameraFov = 0.8;
  scene.imageProcessingConfiguration.vignetteColor = new Color3(0, 0, 0);

  // Camera grain for film-like quality
  scene.imageProcessingConfiguration.grainEnabled = true;
  scene.imageProcessingConfiguration.grainIntensity = 15;
  scene.imageProcessingConfiguration.grainAnimated = true;

  // Chromatic aberration for lens realism
  scene.imageProcessingConfiguration.chromaticAberrationEnabled = true;
  scene.imageProcessingConfiguration.chromaticAberrationIntensity = 0.5;

  // Dithering for smooth gradients
  scene.imageProcessingConfiguration.ditheringEnabled = true;
  scene.imageProcessingConfiguration.ditheringIntensity = 1.0 / 255.0;

  // Enable MSAA for ultra-smooth edges
  scene.getEngine().setHardwareScalingLevel(1.0);

  console.log("Ultra-photorealistic post-processing enabled");
}

function updateSceneMaterials(scene: Scene, materials: MaterialOptions) {
  // Update floor material
  const floor = scene.getMeshByName("floor");
  if (floor) {
    floor.material = createPhotorealisticMaterial(scene, 'floor', materials.floor);
  }

  // Update wall materials
  const walls = ["backWall", "leftWall", "rightWall"];
  walls.forEach(wallName => {
    const wall = scene.getMeshByName(wallName);
    if (wall) {
      wall.material = createPhotorealisticMaterial(scene, 'walls', materials.walls);
    }
  });

  // Update ceiling material
  const ceiling = scene.getMeshByName("ceiling");
  if (ceiling) {
    ceiling.material = createPhotorealisticMaterial(scene, 'ceiling', materials.ceiling);
  }

  // Update bed materials
  const bedElements = ["bedFrame", "headboard", "nightstand1", "nightstand2", "dresser", "desk"];
  bedElements.forEach(elementName => {
    const element = scene.getMeshByName(elementName);
    if (element) {
      element.material = createPhotorealisticMaterial(scene, 'bed', materials.bed);
    }
  });
}

function updateSceneDimensions(scene: Scene, dimensions: RoomDimensions) {
  // This would require recreating the scene geometry
  // For now, we'll just log the change
  console.log("Dimensions updated:", dimensions);
  // In a full implementation, you would recreate the room geometry here
}
