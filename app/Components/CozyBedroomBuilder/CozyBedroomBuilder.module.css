.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #8B4513 0%, #D2B48C 50%, #F5DEB3 100%);
  color: white;
}

.header {
  text-align: center;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.dimensionPanel {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  padding: 1.5rem;
  margin: 1rem 2rem;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dimensionPanel h3 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  text-align: center;
}

.dimensionInputs {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.inputGroup label {
  font-size: 0.9rem;
  font-weight: 500;
  opacity: 0.9;
}

.inputGroup input {
  padding: 0.75rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1.1rem;
  width: 80px;
  text-align: center;
  font-weight: 600;
}

.inputGroup input:focus {
  outline: none;
  border-color: #F5DEB3;
  background: rgba(255, 255, 255, 0.2);
}

.toggleBtn,
.showDimensionsBtn {
  display: block;
  margin: 0 auto;
  padding: 0.75rem 1.5rem;
  background: rgba(245, 222, 179, 0.8);
  color: #8B4513;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.toggleBtn:hover,
.showDimensionsBtn:hover {
  background: rgba(245, 222, 179, 1);
  transform: translateY(-2px);
}

.showDimensionsBtn {
  position: fixed;
  top: 120px;
  right: 2rem;
  z-index: 100;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.materialControls {
  display: flex;
  gap: 2rem;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  overflow-x: auto;
}

.materialSection {
  min-width: 200px;
}

.materialSection h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
}

.materialGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.materialBtn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
}

.materialBtn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.materialBtn.selected {
  border-color: #F5DEB3;
  background: rgba(245, 222, 179, 0.3);
  box-shadow: 0 4px 12px rgba(245, 222, 179, 0.4);
}

.materialPreview {
  width: 35px;
  height: 35px;
  border-radius: 6px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.viewerContainer {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  min-height: 500px;
}

.canvas {
  width: 100%;
  height: 100%;
  max-width: 1000px;
  max-height: 600px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: 3px solid rgba(245, 222, 179, 0.5);
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.loadingSpinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(245, 222, 179, 0.3);
  border-top: 4px solid #F5DEB3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingOverlay p {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: #F5DEB3;
}

.viewerInfo {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.viewerInfo span {
  font-size: 0.9rem;
  font-weight: 500;
  color: #F5DEB3;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .materialControls {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .dimensionInputs {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .header h1 {
    font-size: 2rem;
  }
  
  .dimensionPanel {
    margin: 1rem;
  }
  
  .materialControls {
    padding: 1rem;
  }
  
  .materialSection {
    min-width: auto;
  }
  
  .materialGrid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .showDimensionsBtn {
    right: 1rem;
    top: 100px;
  }
  
  .viewerContainer {
    padding: 1rem;
    min-height: 400px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 1.5rem;
  }
  
  .materialGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .materialBtn {
    padding: 0.5rem;
    font-size: 0.7rem;
  }
  
  .materialPreview {
    width: 30px;
    height: 30px;
  }
  
  .showDimensionsBtn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}
