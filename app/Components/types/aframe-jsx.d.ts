// A-Frame JSX type definitions
import 'aframe';

declare global {
  interface Window {
    AFRAME?: any;
  }

  namespace JSX {
    interface IntrinsicElements {
      'a-scene': any;
      'a-assets': any;
      'a-asset-item': any;
      'a-sky': any;
      'a-camera': any;
      'a-entity': any;
      'a-light': any;
      'a-box': any;
      'a-sphere': any;
      'a-cylinder': any;
      'a-plane': any;
      'a-text': any;
      'a-cursor': any;
      'a-animation': any;
      'a-sound': any;
      'a-video': any;
      'a-image': any;
      'a-gltf-model': any;
      'a-obj-model': any;
      'a-collada-model': any;
      'a-ring': any;
      'a-torus': any;
      'a-cone': any;
      'a-dodecahedron': any;
      'a-icosahedron': any;
      'a-octahedron': any;
      'a-tetrahedron': any;
      'a-triangle': any;
      'a-circle': any;
      'a-curvedimage': any;
      'a-videosphere': any;
      img: React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>;
      video: React.DetailedHTMLProps<React.VideoHTMLAttributes<HTMLVideoElement>, HTMLVideoElement>;
      audio: React.DetailedHTMLProps<React.AudioHTMLAttributes<HTMLAudioElement>, HTMLAudioElement>;
    }
  }
}
