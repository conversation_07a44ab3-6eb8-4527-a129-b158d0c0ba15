.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #004d1a;
    padding: 14px 24px;
    color: white;
    position: static;
    top: 0;
    z-index: 1000;
  }
  
  .logo {
    font-size: 20px;
    font-weight: bold;
  }
  
  .actions {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .iconBtn {
    background-color: white;
    color: black;
    border: none;
    padding: 8px 10px;
    border-radius: 8px;
    font-size: 18px;
    cursor: pointer;
  }
  
  .hamburger {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
  }

  .hamburger:hover {
  color: #007f5f;
}
  
  .menuBackdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1999;
  }
  
  /* Drawer */
  .fullMenuOverlay {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    width: 40%;
    max-width: 400px;
    background-color: #004d1a;
    z-index: 2000;
    overflow-y: auto;
    padding: 60px 20px 20px;
    display: flex;
    flex-direction: column;
    box-shadow: -4px 0 10px rgba(0, 0, 0, 0.3);
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
  }
  
  .slideIn {
    transform: translateX(0%);
  }
  
  .menuItems {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 14px;
  }
  
  .navItem {
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 8px 0;
    text-align: left;
    width: 100%;
    border-radius: 0;
    transition: background 0.2s ease;
  }
  
  .navItem:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .phoneIcon {
  font-size: 20px;
  margin-right: 16px;
  cursor: pointer;
  color: white;
  display: flex;
  align-items: left;
  transition: color 0.3s ease;
}

.phoneIcon:hover {
  color: #007f5f;
}

.menuHeader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 14px 20px;
  background-color: #003814;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  z-index: 1;
}

.backButton {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 6px 10px;
  transition: color 0.2s ease;
}

.backButton:hover {
  color: #80ed99;
}

.backButton svg {
  font-size: 20px;
}


  
  @media (max-width: 768px) {
    .fullMenuOverlay {
      width: 90%;
    }
  }
  