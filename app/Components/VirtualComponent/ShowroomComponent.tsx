'use client';
import styles from './ShowroomComponent.module.css';
import { useRef, useState } from 'react';
import { imageGenerationService } from '../../services/imageGeneration';

export default function ShowroomComponent() {
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [designPrompt, setDesignPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        setError('Image size should be less than 10MB');
        return;
      }
      
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadedImage(e.target?.result as string);
        setError(null);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleGenerateDesign = async () => {
    if (!uploadedImage) {
      setError('Please upload an image of your room first');
      return;
    }
    
    if (!designPrompt.trim()) {
      setError('Please describe how you want to transform your room');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      // For now, we'll use text-to-image generation with the prompt
      // In the future, this could be enhanced with image-to-image generation
      const enhancedPrompt = `Transform this room: ${designPrompt}. Professional interior design, high quality, realistic lighting, modern architecture, elegant design, photorealistic rendering, interior design magazine quality, 8k resolution, detailed textures, warm and inviting atmosphere.`;
      
      console.log('🎨 Generating room design with prompt:', enhancedPrompt);
      
      // Call the image generation service
      const generatedImageBlob = await imageGenerationService.generateCustomizedRoom(
        {
          title: 'Custom Room Design',
          category: 'custom',
          roomSize: 'custom',
          originalImage: uploadedImage
        },
        {
          flooring: { id: 'custom', name: 'Custom', description: designPrompt },
          walls: { id: 'custom', name: 'Custom', description: designPrompt },
          ceiling: { id: 'custom', name: 'Custom', description: designPrompt },
          lighting: { id: 'custom', name: 'Custom', description: designPrompt }
        }
      );
      
      setGeneratedImage(generatedImageBlob);
      
    } catch (error) {
      console.error('Error generating design:', error);

      // Check if it's a credit/quota issue
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (errorMessage.includes('credit') || errorMessage.includes('quota') || errorMessage.includes('limit')) {
        setError('AI service temporarily unavailable (quota exceeded). The demo will show a placeholder image. Please try again later or contact support for API credits.');
      } else {
        setError('Failed to generate design. Please try again or check your internet connection.');
      }
    } finally {
      setIsGenerating(false);
    }
  };

  const handleReset = () => {
    setUploadedImage(null);
    setDesignPrompt('');
    setGeneratedImage(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDownloadDesign = () => {
    if (!generatedImage) return;

    try {
      // Create a link element to download the image
      const link = document.createElement('a');
      link.href = generatedImage;
      link.download = `ai-room-design-${new Date().toISOString().split('T')[0]}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading image:', error);
      setError('Failed to download image. Please try right-clicking and saving the image manually.');
    }
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h1>Design Your Room with AI</h1>
        <p>Upload a photo of your room and describe how you want to transform it. Our AI will generate a new design for you.</p>
      </div>

      {/* Main Content */}
      <div className={styles.aiDesignContainer}>
        {/* Upload Section */}
        <div className={styles.uploadSection}>
          <h2>1. Upload Your Room Photo</h2>
          <div 
            className={styles.uploadArea}
            onClick={() => fileInputRef.current?.click()}
          >
            {uploadedImage ? (
              <div className={styles.uploadedImageContainer}>
                <img src={uploadedImage} alt="Uploaded room" className={styles.uploadedImage} />
                <div className={styles.uploadOverlay}>
                  <span>Click to change image</span>
                </div>
              </div>
            ) : (
              <div className={styles.uploadPlaceholder}>
                <div className={styles.uploadIcon}>📷</div>
                <h3>Upload Room Photo</h3>
                <p>Click here or drag and drop your room image</p>
                <span className={styles.uploadHint}>Supports JPG, PNG (max 10MB)</span>
              </div>
            )}
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className={styles.hiddenInput}
          />
        </div>

        {/* Prompt Section */}
        <div className={styles.promptSection}>
          <h2>2. Describe Your Vision</h2>
          <textarea
            className={styles.promptTextarea}
            placeholder="Describe how you want to transform your room... 

Examples:
• Make it more modern with minimalist furniture and neutral colors
• Add luxury elements like marble countertops and gold accents  
• Create a cozy reading nook with warm lighting and bookshelves
• Transform into a home office with built-in desk and storage
• Add plants and natural elements for a biophilic design"
            value={designPrompt}
            onChange={(e) => setDesignPrompt(e.target.value)}
            rows={8}
          />
        </div>

        {/* Generate Button */}
        <div className={styles.generateSection}>
          <button
            className={styles.generateBtn}
            onClick={handleGenerateDesign}
            disabled={isGenerating || !uploadedImage || !designPrompt.trim()}
          >
            {isGenerating ? (
              <>
                <span className={styles.spinner}></span>
                Generating Your Design...
              </>
            ) : (
              '✨ Generate AI Design'
            )}
          </button>
          
          {error && (
            <div className={styles.errorMessage}>
              ❌ {error}
            </div>
          )}
        </div>

        {/* Results Section */}
        {generatedImage && (
          <div className={styles.resultsSection}>
            <h2>3. Your AI-Generated Design</h2>
            <div className={styles.beforeAfter}>
              <div className={styles.beforeContainer}>
                <h3>Before</h3>
                <img src={uploadedImage} alt="Original room" className={styles.resultImage} />
              </div>
              <div className={styles.afterContainer}>
                <h3>After - AI Design</h3>
                <img src={generatedImage} alt="AI generated design" className={styles.resultImage} />
                <div className={styles.aiGenerated}>✨ AI Generated</div>
              </div>
            </div>
            
            <div className={styles.resultActions}>
              <button className={styles.downloadBtn} onClick={handleDownloadDesign}>
                💾 Download Design
              </button>
              <button className={styles.newDesignBtn} onClick={handleReset}>
                🎨 Create New Design
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
