.container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 3rem;
}

.header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-weight: 700;
}

.header p {
  font-size: 1.1rem;
  color: #6c757d;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
}

/* AI Design Container */
.aiDesignContainer {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Upload Section */
.uploadSection {
  padding: 2rem;
  border-bottom: 1px solid #e9ecef;
}

.uploadSection h2 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 600;
}

.uploadArea {
  border: 3px dashed #dee2e6;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.uploadArea:hover {
  border-color: #667eea;
  background: #f0f2ff;
}

.uploadedImageContainer {
  position: relative;
  width: 100%;
  max-width: 400px;
  border-radius: 12px;
  overflow: hidden;
}

.uploadedImage {
  width: 100%;
  height: auto;
  max-height: 300px;
  object-fit: cover;
  border-radius: 12px;
}

.uploadOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}

.uploadedImageContainer:hover .uploadOverlay {
  opacity: 1;
}

.uploadPlaceholder {
  text-align: center;
}

.uploadIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.uploadPlaceholder h3 {
  margin: 0 0 0.5rem 0;
  color: #495057;
  font-size: 1.2rem;
  font-weight: 600;
}

.uploadPlaceholder p {
  margin: 0 0 1rem 0;
  color: #6c757d;
}

.uploadHint {
  color: #adb5bd;
  font-size: 0.9rem;
}

.hiddenInput {
  display: none;
}

/* Prompt Section */
.promptSection {
  padding: 2rem;
  border-bottom: 1px solid #e9ecef;
}

.promptSection h2 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 600;
}

.promptTextarea {
  width: 100%;
  min-height: 150px;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.6;
  resize: vertical;
  transition: border-color 0.3s ease;
}

.promptTextarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.promptTextarea::placeholder {
  color: #adb5bd;
}

/* Generate Section */
.generateSection {
  padding: 2rem;
  text-align: center;
  border-bottom: 1px solid #e9ecef;
}

.generateBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 200px;
  justify-content: center;
}

.generateBtn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.generateBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Results Section */
.resultsSection {
  padding: 2rem;
}

.resultsSection h2 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 600;
  text-align: center;
}

.beforeAfter {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.beforeContainer,
.afterContainer {
  text-align: center;
}

.beforeContainer h3,
.afterContainer h3 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
}

.resultImage {
  width: 100%;
  height: 250px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.afterContainer {
  position: relative;
}

.aiGenerated {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
}

.resultActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.downloadBtn,
.newDesignBtn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.downloadBtn {
  background: #28a745;
  color: white;
}

.newDesignBtn {
  background: #6c757d;
  color: white;
}

.downloadBtn:hover,
.newDesignBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Spinner Animation */
.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error Message */
.errorMessage {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  border: 1px solid #f5c6cb;
  text-align: center;
  font-weight: 500;
}

.roomCard {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 3px solid transparent;
}

.roomCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.roomCard.selectedRoom {
  border-color: #667eea;
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
}

.roomImageContainer {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.roomImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.roomCard:hover .roomImage {
  transform: scale(1.05);
}

.roomOverlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.roomCategory {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.roomSize {
  background: rgba(102, 126, 234, 0.9);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.roomInfo {
  padding: 1.5rem;
}

.roomInfo h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.roomInfo p {
  margin: 0 0 1rem 0;
  color: #6c757d;
  line-height: 1.6;
}

.roomFeatures {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.feature {
  background: #f8f9fa;
  color: #495057;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.customizeBtn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.customizeBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.selectedRoom .customizeBtn {
  background: #28a745;
}

.videoPlayer {
  width: 100%;
  max-width: 100%;
  border-radius: 16px;
}

.controls {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.controls button {
  background: #fff;
  color: #000;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s ease;
}

.controls button:hover {
  background: #ddd;
}
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header h1 {
    font-size: 2rem;
  }

  .header p {
    font-size: 1rem;
  }

  .aiDesignContainer {
    margin: 0;
  }

  .uploadSection,
  .promptSection,
  .generateSection,
  .resultsSection {
    padding: 1.5rem;
  }

  .uploadArea {
    padding: 1.5rem;
    min-height: 150px;
  }

  .uploadIcon {
    font-size: 2rem;
  }

  .uploadPlaceholder h3 {
    font-size: 1rem;
  }

  .promptTextarea {
    min-height: 120px;
    font-size: 0.9rem;
  }

  .beforeAfter {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .resultActions {
    flex-direction: column;
    align-items: center;
  }

  .downloadBtn,
  .newDesignBtn {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
}

/* Customizer Styles */
.customizer {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-top: 2rem;
}

.customizerHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.customizerHeader h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.customizerActions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.toggleCustomizer {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.toggleCustomizer:hover {
  background: rgba(255, 255, 255, 0.3);
}

.closeCustomizer {
  background: none;
  color: white;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.closeCustomizer:hover {
  background: rgba(255, 255, 255, 0.2);
}

.customizerContent {
  padding: 2rem;
}

.optionSection {
  margin-bottom: 2rem;
}

.optionSection h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.optionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.optionCard {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.optionCard:hover {
  border-color: #667eea;
  transform: translateY(-2px);
}

.optionCard.selectedOption {
  border-color: #28a745;
  background: #e8f5e8;
}

.optionCard img {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.optionInfo {
  padding: 1rem;
}

.optionInfo h4 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.optionPrice {
  color: #28a745;
  font-weight: 600;
  font-size: 0.9rem;
}

.costSummary {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 2rem;
  text-align: center;
}

.costSummary h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.costBreakdown p {
  margin: 0.5rem 0;
  color: #6c757d;
}

.totalCost {
  font-size: 2rem;
  color: #28a745;
  font-weight: 700;
  margin: 1rem 0;
}

.applyBtn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.2s ease;
}

.applyBtn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.applyBtn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorMessage {
  background: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 8px;
  margin-top: 1rem;
  border: 1px solid #f5c6cb;
  font-size: 0.9rem;
}

/* Preview Modal */
.previewModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  overflow-y: auto;
}

.previewContent {
  background: white;
  border-radius: 20px;
  max-width: 1000px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.previewHeader {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20px 20px 0 0;
}

.previewHeader h2 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.closePreview {
  background: none;
  color: white;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.closePreview:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.previewBody {
  padding: 2rem;
}

.previewImageSection {
  margin-bottom: 2rem;
}

.beforeAfter {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.beforeImage,
.afterImage {
  text-align: center;
}

.beforeImage h4,
.afterImage h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.beforeImage img,
.afterImage img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.afterImage {
  position: relative;
}

.customizedBadge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.previewDetails h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 700;
}

.previewDetails p {
  margin: 0 0 2rem 0;
  color: #6c757d;
  font-size: 1.1rem;
  line-height: 1.6;
}

.appliedMaterials h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.materialsList {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.materialItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 12px;
  border: 2px solid #e9ecef;
}

.materialItem img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
}

.materialItem div {
  flex: 1;
}

.materialItem strong {
  display: block;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.materialPrice {
  color: #28a745;
  font-weight: 600;
  font-size: 0.9rem;
}

.previewSummary {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.summaryItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
}

.summaryItem:last-child {
  border-bottom: none;
}

.summaryItem span {
  color: #6c757d;
  font-weight: 500;
}

.summaryItem strong {
  color: #2c3e50;
  font-weight: 600;
}

.totalCostPreview {
  color: #28a745 !important;
  font-size: 1.2rem !important;
  font-weight: 700 !important;
}

.previewActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.saveDesignBtn,
.shareDesignBtn,
.newDesignBtn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.saveDesignBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.shareDesignBtn {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
}

.newDesignBtn {
  background: linear-gradient(135deg, #fd7e14 0%, #e55a4e 100%);
  color: white;
}

.saveDesignBtn:hover,
.shareDesignBtn:hover,
.newDesignBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Responsive Design for Preview */
@media (max-width: 768px) {
  .previewModal {
    padding: 1rem;
  }

  .previewHeader {
    padding: 1.5rem;
  }

  .previewHeader h2 {
    font-size: 1.5rem;
  }

  .previewBody {
    padding: 1.5rem;
  }

  .beforeAfter {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .materialsList {
    grid-template-columns: 1fr;
  }

  .previewActions {
    flex-direction: column;
    align-items: center;
  }

  .downloadBtn,
  .newDesignBtn {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
}

/* Preview Modal */
.previewModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  overflow-y: auto;
}

.previewContent {
  background: white;
  border-radius: 20px;
  max-width: 1000px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.previewHeader {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20px 20px 0 0;
}

.previewHeader h2 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.closePreview {
  background: none;
  color: white;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.closePreview:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.previewBody {
  padding: 2rem;
}

.previewImageSection {
  margin-bottom: 2rem;
}

.beforeAfter {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.beforeImage,
.afterImage {
  text-align: center;
}

.beforeImage h4,
.afterImage h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.beforeImage img,
.afterImage img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.afterImage {
  position: relative;
}

.customizedBadge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.previewDetails h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 700;
}

.previewDetails p {
  margin: 0 0 2rem 0;
  color: #6c757d;
  font-size: 1.1rem;
  line-height: 1.6;
}

.appliedMaterials h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.materialsList {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.materialItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 12px;
  border: 2px solid #e9ecef;
}

.materialItem img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
}

.materialItem div {
  flex: 1;
}

.materialItem strong {
  display: block;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.materialPrice {
  color: #28a745;
  font-weight: 600;
  font-size: 0.9rem;
}

