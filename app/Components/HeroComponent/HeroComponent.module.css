.hero {
    position: relative;
    height: 100vh;
    width: 100%;
    overflow: hidden;
  }
  
  .video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
  }
  
  .overlay {
    position: relative;
    z-index: 1;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    padding: 0 20px;
    background: rgba(0, 0, 0, 0.3); /* Optional tint */
  }
  
  .heading {
    font-size: 2.5rem;
    margin-bottom: 20px;
    font-weight: bold;
  }
  
  .description {
    font-size: 1.25rem;
    max-width: 600px;
    line-height: 1.6;
    margin-bottom: 2rem;
  }

  /* CTA Buttons */
  .ctaButtons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .virtualShowroomBtn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .virtualShowroomBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
  }

  .browseProductsBtn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 1rem 2rem;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
  }

  .browseProductsBtn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
  }
  @media (max-width: 768px) {
    .heading {
      font-size: 1.75rem;
      margin-bottom: 16px;
      padding: 0 10px;
    }

    .description {
      font-size: 1rem;
      line-height: 1.5;
      padding: 0 10px;
      margin-bottom: 1.5rem;
    }

    .overlay {
      padding: 0 15px;
    }

    .ctaButtons {
      flex-direction: column;
      align-items: center;
      gap: 0.75rem;
      margin-top: 1.5rem;
    }

    .virtualShowroomBtn,
    .browseProductsBtn {
      width: 100%;
      max-width: 280px;
      padding: 0.875rem 1.5rem;
      font-size: 1rem;
    }
  }

  @media (max-width: 480px) {
    .heading {
      font-size: 1.5rem;
    }

    .virtualShowroomBtn,
    .browseProductsBtn {
      padding: 0.75rem 1.25rem;
      font-size: 0.95rem;
    }
  }

  /* Focus states for accessibility */
  .virtualShowroomBtn:focus,
  .browseProductsBtn:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .browseProductsBtn {
      border-color: white;
      background: rgba(0, 0, 0, 0.3);
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .virtualShowroomBtn,
    .browseProductsBtn {
      transition: none;
    }

    .virtualShowroomBtn:hover,
    .browseProductsBtn:hover {
      transform: none;
    }
  }
  