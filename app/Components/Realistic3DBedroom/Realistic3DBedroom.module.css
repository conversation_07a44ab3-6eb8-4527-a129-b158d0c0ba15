.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.header {
  text-align: center;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.header p {
  margin: 0;
  font-size: 1rem;
  opacity: 0.9;
}

.controlsPanel {
  position: absolute;
  top: 120px;
  left: 1rem;
  z-index: 100;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(15px);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 300px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.section {
  margin-bottom: 2rem;
}

.section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #fff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 0.5rem;
}

.inputGroup {
  margin-bottom: 1rem;
}

.inputGroup label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #e0e0e0;
}

.inputGroup input[type="range"] {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  -webkit-appearance: none;
}

.inputGroup input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4CAF50;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.inputGroup input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4CAF50;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.materialGroup {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.materialGroup label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #e0e0e0;
  min-width: 60px;
}

.materialGroup select {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.9rem;
}

.materialGroup select:focus {
  outline: none;
  border-color: #4CAF50;
  background: rgba(255, 255, 255, 0.15);
}

.materialGroup select option {
  background: #2c3e50;
  color: white;
}

.toggleControls {
  position: absolute;
  top: 120px;
  right: 1rem;
  z-index: 100;
  padding: 0.75rem 1.5rem;
  background: rgba(76, 175, 80, 0.9);
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.toggleControls:hover {
  background: rgba(76, 175, 80, 1);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.sceneContainer {
  flex: 1;
  position: relative;
  min-height: calc(100vh - 120px); /* Full height minus header */
  height: calc(100vh - 120px);
  width: 100%;
}

.canvas {
  width: 100% !important;
  height: 100% !important;
  border-radius: 0;
  display: block;
}

.sceneInfo {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 50;
}

.sceneInfo span {
  font-size: 0.9rem;
  font-weight: 500;
  color: #e0e0e0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .controlsPanel {
    position: relative;
    top: auto;
    left: auto;
    max-width: none;
    margin: 1rem;
    display: flex;
    gap: 2rem;
    overflow-x: auto;
  }
  
  .section {
    min-width: 250px;
    margin-bottom: 0;
  }
  
  .toggleControls {
    position: relative;
    top: auto;
    right: auto;
    margin: 1rem auto;
    display: block;
  }
}

@media (max-width: 768px) {
  .header h1 {
    font-size: 1.5rem;
  }
  
  .controlsPanel {
    flex-direction: column;
    gap: 1rem;
  }
  
  .section {
    min-width: auto;
  }
  
  .materialGroup {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .materialGroup label {
    min-width: auto;
  }
  
  .materialGroup select {
    width: 100%;
  }
  
  .sceneContainer {
    min-height: calc(100vh - 200px);
    height: calc(100vh - 200px);
  }
  
  .sceneInfo {
    padding: 0.5rem 1rem;
  }
  
  .sceneInfo span {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 1.3rem;
  }
  
  .controlsPanel {
    margin: 0.5rem;
    padding: 1rem;
  }
  
  .section h3 {
    font-size: 1rem;
  }
  
  .inputGroup label,
  .materialGroup label {
    font-size: 0.8rem;
  }
  
  .materialGroup select {
    font-size: 0.8rem;
    padding: 0.4rem;
  }
  
  .toggleControls {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
  
  .sceneContainer {
    min-height: calc(100vh - 250px);
    height: calc(100vh - 250px);
  }
}

/* Loading and Performance */
.canvas {
  background: linear-gradient(135deg, #87CEEB 0%, #98D8E8 100%);
}

/* Smooth transitions for all interactive elements */
.inputGroup input,
.materialGroup select,
.toggleControls {
  transition: all 0.3s ease;
}

/* Focus states for accessibility */
.inputGroup input:focus,
.materialGroup select:focus,
.toggleControls:focus {
  outline: 2px solid #4CAF50;
  outline-offset: 2px;
}
