'use client';

import React, { useRef, useState, Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import {
  OrbitControls,
  Box,
  Plane,
  Environment,
  ContactShadows
} from '@react-three/drei';
import * as THREE from 'three';
import styles from './Realistic3DBedroom.module.css';

interface RoomDimensions {
  length: number;
  width: number;
  height: number;
}

interface MaterialOptions {
  floor: string;
  walls: string;
  bed: string;
  ceiling: string;
  lighting: string;
}

interface MaterialConfig {
  color: string;
  roughness: number;
  metalness: number;
}

// Simplified material configurations for better performance and compatibility
const materialConfigs = {
  floor: {
    'oak-hardwood': { color: '#D2B48C', roughness: 0.8, metalness: 0.0 },
    'premium-carpet': { color: '#8B7355', roughness: 0.9, metalness: 0.0 },
    'white-marble': { color: '#F8F8FF', roughness: 0.1, metalness: 0.0 },
    'luxury-vinyl': { color: '#8B4513', roughness: 0.6, metalness: 0.0 }
  },
  walls: {
    'white-paint': { color: '#FFF8DC', roughness: 0.9, metalness: 0.0 },
    'wood-paneling': { color: '#8B4513', roughness: 0.7, metalness: 0.0 },
    'modern-wallpaper': { color: '#F5F5DC', roughness: 0.8, metalness: 0.0 },
    'exposed-brick': { color: '#A0522D', roughness: 0.9, metalness: 0.0 }
  },
  ceiling: {
    'smooth-gypsum': { color: '#FFFFFF', roughness: 0.8, metalness: 0.0 },
    'wood-planks': { color: '#8B4513', roughness: 0.7, metalness: 0.0 },
    'textured-gypsum': { color: '#F8F8FF', roughness: 0.9, metalness: 0.0 },
    'suspended-grid': { color: '#E0E0E0', roughness: 0.6, metalness: 0.1 }
  },
  bed: {
    'executive-leather': { color: '#654321', roughness: 0.3, metalness: 0.0 },
    'luxury-fabric': { color: '#2F4F4F', roughness: 0.8, metalness: 0.0 },
    'modern-wood': { color: '#8B4513', roughness: 0.6, metalness: 0.0 }
  }
};

// Realistic Material Component
function RealisticMaterial({ materialType, materialName }: { materialType: keyof typeof materialConfigs; materialName: string }) {
  const materialCategory = materialConfigs[materialType];
  const config = materialCategory[materialName as keyof typeof materialCategory] as MaterialConfig;

  if (!config) {
    return <meshStandardMaterial color="#888888" roughness={0.8} metalness={0.0} />;
  }

  // Use enhanced physical material for better realism
  return (
    <meshPhysicalMaterial
      color={config.color}
      roughness={config.roughness}
      metalness={config.metalness}
      clearcoat={materialType === 'floor' && materialName === 'white-marble' ? 0.3 : 0}
      clearcoatRoughness={0.1}
      reflectivity={materialType === 'floor' ? 0.2 : 0.1}
    />
  );
}

// Room Component - The main 3D room with realistic materials
function Room({ dimensions, materials }: { dimensions: RoomDimensions; materials: MaterialOptions }) {
  const roomRef = useRef<THREE.Group>(null);

  // Convert feet to meters for Three.js - Make it much bigger for better visualization
  const length = dimensions.length * 1.2; // Increased scale significantly
  const width = dimensions.width * 1.2;   // Increased scale significantly
  const height = dimensions.height * 1.2; // Increased scale significantly

  return (
    <group ref={roomRef}>
      {/* Floor */}
      <Plane
        args={[length, width]}
        rotation={[-Math.PI / 2, 0, 0]}
        position={[0, 0, 0]}
        receiveShadow
      >
        <RealisticMaterial materialType="floor" materialName={materials.floor} />
      </Plane>

      {/* Walls */}
      {/* Back Wall */}
      <Plane
        args={[length, height]}
        position={[0, height/2, -width/2]}
        receiveShadow
        castShadow
      >
        <RealisticMaterial materialType="walls" materialName={materials.walls} />
      </Plane>

      {/* Left Wall */}
      <Plane
        args={[width, height]}
        rotation={[0, Math.PI / 2, 0]}
        position={[-length/2, height/2, 0]}
        receiveShadow
        castShadow
      >
        <RealisticMaterial materialType="walls" materialName={materials.walls} />
      </Plane>

      {/* Right Wall */}
      <Plane
        args={[width, height]}
        rotation={[0, -Math.PI / 2, 0]}
        position={[length/2, height/2, 0]}
        receiveShadow
        castShadow
      >
        <RealisticMaterial materialType="walls" materialName={materials.walls} />
      </Plane>

      {/* Ceiling */}
      <Plane
        args={[length, width]}
        rotation={[Math.PI / 2, 0, 0]}
        position={[0, height, 0]}
        receiveShadow
      >
        <RealisticMaterial materialType="ceiling" materialName={materials.ceiling} />
      </Plane>

      {/* Note: Executive Bedroom Furniture is now rendered separately in the Canvas */}
    </group>
  );
}

// Executive Bedroom Furniture Component - Detailed and Realistic
function ExecutiveBedroomFurniture({ dimensions, materials }: { dimensions: RoomDimensions; materials: MaterialOptions }) {
  const length = dimensions.length * 1.2; // Match the room scale
  const width = dimensions.width * 1.2;   // Match the room scale

  return (
    <group>
      {/* Executive King Size Bed (centered) */}
      <group position={[0, 0, width * 0.05]}>
        {/* Bed Base/Platform */}
        <Box args={[7.0, 0.4, 5.0]} position={[0, 0.2, 0]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>

        {/* Mattress with realistic proportions */}
        <Box args={[6.7, 0.6, 4.7]} position={[0, 0.7, 0]} castShadow receiveShadow>
          <meshPhysicalMaterial
            color="#F8F8FF"
            roughness={0.8}
            metalness={0.0}
            clearcoat={0.1}
          />
        </Box>

        {/* Executive Headboard - Tall and Elegant */}
        <Box args={[7.5, 4.0, 0.4]} position={[0, 2.2, -2.5]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>

        {/* Headboard Padding/Upholstery */}
        <Box args={[7.0, 3.5, 0.2]} position={[0, 2.0, -2.3]} castShadow receiveShadow>
          <meshPhysicalMaterial
            color="#2F4F4F"
            roughness={0.7}
            metalness={0.0}
          />
        </Box>

        {/* Pillows */}
        <Box args={[1.2, 0.4, 0.8]} position={[-2.0, 1.2, -1.8]} castShadow receiveShadow>
          <meshPhysicalMaterial color="#FFFFFF" roughness={0.9} metalness={0.0} />
        </Box>
        <Box args={[1.2, 0.4, 0.8]} position={[2.0, 1.2, -1.8]} castShadow receiveShadow>
          <meshPhysicalMaterial color="#FFFFFF" roughness={0.9} metalness={0.0} />
        </Box>
      </group>

      {/* Executive Nightstands */}
      {/* Left Nightstand */}
      <group position={[-4.5, 0, width * 0.05]}>
        <Box args={[1.8, 2.2, 1.5]} position={[0, 1.1, 0]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>
        {/* Nightstand Top */}
        <Box args={[1.9, 0.1, 1.6]} position={[0, 2.25, 0]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>
        {/* Table Lamp */}
        <Box args={[0.3, 1.0, 0.3]} position={[0, 2.8, 0]} castShadow receiveShadow>
          <meshPhysicalMaterial color="#8B4513" roughness={0.3} metalness={0.1} />
        </Box>
        <Box args={[0.8, 0.6, 0.8]} position={[0, 3.5, 0]} castShadow receiveShadow>
          <meshPhysicalMaterial color="#F5DEB3" roughness={0.9} metalness={0.0} />
        </Box>
      </group>

      {/* Right Nightstand */}
      <group position={[4.5, 0, width * 0.05]}>
        <Box args={[1.8, 2.2, 1.5]} position={[0, 1.1, 0]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>
        {/* Nightstand Top */}
        <Box args={[1.9, 0.1, 1.6]} position={[0, 2.25, 0]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>
        {/* Table Lamp */}
        <Box args={[0.3, 1.0, 0.3]} position={[0, 2.8, 0]} castShadow receiveShadow>
          <meshPhysicalMaterial color="#8B4513" roughness={0.3} metalness={0.1} />
        </Box>
        <Box args={[0.8, 0.6, 0.8]} position={[0, 3.5, 0]} castShadow receiveShadow>
          <meshPhysicalMaterial color="#F5DEB3" roughness={0.9} metalness={0.0} />
        </Box>
      </group>

      {/* Executive Dresser */}
      <group position={[0, 0, -width * 0.4]}>
        <Box args={[5.0, 3.0, 1.8]} position={[0, 1.5, 0]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>
        {/* Dresser Top */}
        <Box args={[5.1, 0.1, 1.9]} position={[0, 3.05, 0]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>
        {/* Mirror */}
        <Box args={[3.5, 2.5, 0.1]} position={[0, 4.8, -0.1]} castShadow receiveShadow>
          <meshPhysicalMaterial
            color="#E6E6FA"
            roughness={0.0}
            metalness={0.9}
            clearcoat={1.0}
            clearcoatRoughness={0.0}
          />
        </Box>
        {/* Mirror Frame */}
        <Box args={[3.7, 2.7, 0.2]} position={[0, 4.8, -0.2]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>
      </group>

      {/* Executive Desk (Corner) */}
      <group position={[length * 0.35, 0, width * 0.35]}>
        {/* Desk Surface */}
        <Box args={[3.5, 0.1, 2.0]} position={[0, 2.4, 0]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>
        {/* Desk Legs */}
        <Box args={[0.2, 2.4, 0.2]} position={[-1.6, 1.2, -0.8]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>
        <Box args={[0.2, 2.4, 0.2]} position={[1.6, 1.2, -0.8]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>
        <Box args={[0.2, 2.4, 0.2]} position={[-1.6, 1.2, 0.8]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>
        <Box args={[0.2, 2.4, 0.2]} position={[1.6, 1.2, 0.8]} castShadow receiveShadow>
          <RealisticMaterial materialType="bed" materialName={materials.bed} />
        </Box>
      </group>

      {/* Executive Chair */}
      <group position={[length * 0.35, 0, width * 0.25]}>
        {/* Chair Base */}
        <Box args={[1.2, 0.1, 1.2]} position={[0, 1.8, 0]} castShadow receiveShadow>
          <meshPhysicalMaterial color="#2F4F4F" roughness={0.7} metalness={0.0} />
        </Box>
        {/* Chair Back */}
        <Box args={[1.0, 2.0, 0.2]} position={[0, 2.8, -0.4]} castShadow receiveShadow>
          <meshPhysicalMaterial color="#2F4F4F" roughness={0.7} metalness={0.0} />
        </Box>
        {/* Chair Pedestal */}
        <Box args={[0.3, 1.8, 0.3]} position={[0, 0.9, 0]} castShadow receiveShadow>
          <meshPhysicalMaterial color="#696969" roughness={0.3} metalness={0.7} />
        </Box>
      </group>
    </group>
  );
}

// Advanced Realistic Lighting Component
function RealisticLighting() {
  return (
    <>
      {/* HDR Environment Lighting */}
      <Environment preset="apartment" />

      {/* Ambient Light - Reduced for more realistic contrast */}
      <ambientLight intensity={0.2} color="#F0F8FF" />

      {/* Main Window Light - Natural daylight */}
      <directionalLight
        position={[20, 15, 10]}
        intensity={1.5}
        color="#FFFFFF"
        castShadow
        shadow-mapSize-width={4096}
        shadow-mapSize-height={4096}
        shadow-camera-near={0.1}
        shadow-camera-far={50}
        shadow-camera-left={-20}
        shadow-camera-right={20}
        shadow-camera-top={20}
        shadow-camera-bottom={-20}
        shadow-bias={-0.0001}
      />

      {/* Secondary Window Light - Softer fill */}
      <directionalLight
        position={[-10, 12, 8]}
        intensity={0.6}
        color="#E6F3FF"
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />

      {/* Ceiling Light - Warm interior lighting */}
      <pointLight
        position={[0, 10, 0]}
        intensity={0.8}
        color="#FFF8DC"
        distance={25}
        decay={2}
        castShadow
      />

      {/* Nightstand Lamps - Warm accent lighting */}
      <pointLight
        position={[-4.5, 4, 1.5]}
        intensity={0.6}
        color="#FFE4B5"
        distance={8}
        decay={2}
        castShadow
      />
      <pointLight
        position={[4.5, 4, 1.5]}
        intensity={0.6}
        color="#FFE4B5"
        distance={8}
        decay={2}
        castShadow
      />

      {/* Desk Lamp */}
      <spotLight
        position={[8, 5, 6]}
        intensity={0.8}
        color="#FFFFFF"
        angle={Math.PI / 6}
        penumbra={0.3}
        distance={15}
        decay={2}
        castShadow
      />

      {/* Accent Lighting - Under-bed LED strip effect */}
      <pointLight
        position={[0, 0.1, 0]}
        intensity={0.3}
        color="#4169E1"
        distance={12}
        decay={2}
      />
    </>
  );
}

export default function Realistic3DBedroom() {
  const [dimensions, setDimensions] = useState<RoomDimensions>({
    length: 16,
    width: 14,
    height: 10
  });

  const [materials, setMaterials] = useState<MaterialOptions>({
    floor: 'oak-hardwood',
    walls: 'white-paint',
    bed: 'executive-leather',
    ceiling: 'smooth-gypsum',
    lighting: 'led-recessed'
  });

  const [showControls, setShowControls] = useState(true);

  const handleDimensionChange = (field: keyof RoomDimensions, value: number) => {
    setDimensions(prev => ({ ...prev, [field]: value }));
  };

  const handleMaterialChange = (type: keyof MaterialOptions, value: string) => {
    setMaterials(prev => ({ ...prev, [type]: value }));
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h1>🏢 Executive Bedroom - Photorealistic</h1>
        <p>Luxury executive bedroom with advanced Three.js rendering and real-time customization</p>
      </div>

      {/* Controls */}
      {showControls && (
        <div className={styles.controlsPanel}>
          {/* Dimensions */}
          <div className={styles.section}>
            <h3>📏 Dimensions (feet)</h3>
            <div className={styles.inputGroup}>
              <label>Length: {dimensions.length}'</label>
              <input
                type="range"
                min="10"
                max="20"
                value={dimensions.length}
                onChange={(e) => handleDimensionChange('length', Number(e.target.value))}
              />
            </div>
            <div className={styles.inputGroup}>
              <label>Width: {dimensions.width}'</label>
              <input
                type="range"
                min="8"
                max="16"
                value={dimensions.width}
                onChange={(e) => handleDimensionChange('width', Number(e.target.value))}
              />
            </div>
            <div className={styles.inputGroup}>
              <label>Height: {dimensions.height}'</label>
              <input
                type="range"
                min="8"
                max="12"
                value={dimensions.height}
                onChange={(e) => handleDimensionChange('height', Number(e.target.value))}
              />
            </div>
          </div>

          {/* Materials */}
          <div className={styles.section}>
            <h3>🎨 Materials</h3>
            <div className={styles.materialGroup}>
              <label>Floor:</label>
              <select
                value={materials.floor}
                onChange={(e) => handleMaterialChange('floor', e.target.value)}
              >
                <option value="oak-hardwood">Oak Hardwood</option>
                <option value="premium-carpet">Premium Carpet</option>
                <option value="white-marble">White Marble</option>
                <option value="luxury-vinyl">Luxury Vinyl</option>
              </select>
            </div>
            <div className={styles.materialGroup}>
              <label>Walls:</label>
              <select
                value={materials.walls}
                onChange={(e) => handleMaterialChange('walls', e.target.value)}
              >
                <option value="white-paint">White Paint</option>
                <option value="wood-paneling">Wood Paneling</option>
                <option value="modern-wallpaper">Modern Wallpaper</option>
                <option value="exposed-brick">Exposed Brick</option>
              </select>
            </div>
            <div className={styles.materialGroup}>
              <label>Ceiling:</label>
              <select
                value={materials.ceiling}
                onChange={(e) => handleMaterialChange('ceiling', e.target.value)}
              >
                <option value="smooth-gypsum">Smooth Gypsum</option>
                <option value="wood-planks">Wood Planks</option>
                <option value="textured-gypsum">Textured Gypsum</option>
                <option value="suspended-grid">Suspended Grid</option>
              </select>
            </div>
            <div className={styles.materialGroup}>
              <label>Bed:</label>
              <select
                value={materials.bed}
                onChange={(e) => handleMaterialChange('bed', e.target.value)}
              >
                <option value="executive-leather">Executive Leather</option>
                <option value="luxury-fabric">Luxury Fabric</option>
                <option value="modern-wood">Modern Wood</option>
              </select>
            </div>
          </div>
        </div>
      )}

      <button 
        className={styles.toggleControls}
        onClick={() => setShowControls(!showControls)}
      >
        {showControls ? 'Hide Controls' : 'Show Controls'}
      </button>

      {/* 3D Scene */}
      <div className={styles.sceneContainer}>
        <Canvas
          camera={{ position: [18, 12, 18], fov: 60 }}
          shadows="soft"
          className={styles.canvas}
          gl={{
            antialias: true,
            toneMapping: THREE.ACESFilmicToneMapping,
            toneMappingExposure: 1.2
          }}
        >
          <Suspense fallback={null}>
            <RealisticLighting />
            <Room dimensions={dimensions} materials={materials} />
            <ExecutiveBedroomFurniture dimensions={dimensions} materials={materials} />
            <ContactShadows
              position={[0, 0, 0]}
              opacity={0.4}
              scale={30}
              blur={2}
              far={10}
            />
            <OrbitControls
              enablePan={true}
              enableZoom={true}
              enableRotate={true}
              minDistance={10}
              maxDistance={50}
              maxPolarAngle={Math.PI / 2.2}
              target={[0, 2, 0]}
            />
          </Suspense>
        </Canvas>
        
        <div className={styles.sceneInfo}>
          <span>🖱️ Drag to rotate • Scroll to zoom • Right-click to pan</span>
        </div>
      </div>
    </div>
  );
}
