.container {
  min-height: 100vh;
  background: #000;
  color: white;
  position: relative;
  overflow: hidden;
}

/* Loading Screen */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.loadingSpinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 2rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingContainer h2 {
  margin: 0 0 1rem 0;
  font-size: 2rem;
  font-weight: 700;
}

.loadingContainer p {
  margin: 0;
  opacity: 0.8;
  font-size: 1.1rem;
}

/* Header */
.header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
}

.backBtn,
.customizeBtn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.backBtn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.backBtn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.customizeBtn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.customizeBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
  flex: 1;
}

.roomInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.roomInfo span {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Real Photo Viewer */
.viewerContainer {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  overflow: hidden;
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 1200px;
  max-height: 800px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.roomPhoto {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.roomPhoto:hover {
  transform: scale(1.02);
}

/* Color Overlays for Real-time Customization */
.colorOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  transition: all 0.3s ease;
}

.wallOverlay {
  /* Targets wall areas - adjust based on room type */
  clip-path: polygon(0% 0%, 100% 0%, 100% 70%, 0% 70%);
}

.floorOverlay {
  /* Targets floor areas */
  clip-path: polygon(0% 70%, 100% 70%, 100% 100%, 0% 100%);
}

.ceilingOverlay {
  /* Targets ceiling areas */
  clip-path: polygon(0% 0%, 100% 0%, 100% 30%, 0% 30%);
}

/* Material Info Display */
.materialInfo {
  position: absolute;
  top: 1rem;
  left: 1rem;
  z-index: 60;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.materialTag {
  background: rgba(0, 0, 0, 0.8);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.materialTag span {
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Interactive Controls */
.interactiveControls {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 50;
}

.controlsInfo {
  background: rgba(0, 0, 0, 0.8);
  padding: 1rem 2rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.controlsInfo span {
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Customization Panel */
.customizationPanel {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 400px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 200;
  overflow-y: auto;
  transform: translateX(0);
  transition: transform 0.3s ease;
}

.panelHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.panelHeader h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
}

.panelHeader button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.panelHeader button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.materialSections {
  padding: 1.5rem;
}

.materialSection {
  margin-bottom: 2rem;
}

.materialSection h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
}

.materialGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.materialBtn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.materialBtn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.materialBtn.selected {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.2);
}

.materialPreview {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.materialBtn span {
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .customizationPanel {
    width: 350px;
  }
  
  .materialGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 1.2rem;
  }
  
  .backBtn,
  .customizeBtn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .customizationPanel {
    width: 100%;
    height: 50vh;
    top: auto;
    bottom: 0;
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .materialInfo {
    top: 0.5rem;
    left: 0.5rem;
  }

  .materialTag {
    padding: 0.4rem 0.8rem;
  }

  .materialTag span {
    font-size: 0.7rem;
  }

  .controlsInfo {
    padding: 0.75rem 1.5rem;
  }

  .controlsInfo span {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .header {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0.5rem;
  }
  
  .header h1 {
    order: -1;
    font-size: 1.1rem;
  }
  
  .materialSections {
    padding: 1rem;
  }
  
  .materialBtn {
    padding: 0.75rem;
  }
  
  .materialPreview {
    width: 30px;
    height: 30px;
  }
}
