'use client';

import React, { useState } from 'react';
import VR<PERSON><PERSON><PERSON>ie<PERSON> from './VRRoomViewer';
import styles from './VRGallery.module.css';

export interface RoomType {
  id: string;
  name: string;
  category: string;
  description: string;
  thumbnail: string;
  features: string[];
  defaultMaterials: {
    walls: string;
    ceiling: string;
    floor: string;
  };
}

const roomTypes: RoomType[] = [
  {
    id: 'modern-living',
    name: 'Modern Living Room',
    category: 'Residential',
    description: 'Contemporary living space with clean lines and modern aesthetics',
    thumbnail: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
    features: ['Open concept', 'Large windows', 'Modern furniture', 'Ambient lighting'],
    defaultMaterials: {
      walls: 'white-paint',
      ceiling: 'smooth-white',
      floor: 'oak-hardwood'
    }
  },
  {
    id: 'luxury-bedroom',
    name: 'Luxury Bedroom',
    category: 'Residential',
    description: 'Elegant bedroom with premium finishes and comfortable ambiance',
    thumbnail: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop',
    features: ['King size bed', 'Walk-in closet', 'Premium materials', 'Mood lighting'],
    defaultMaterials: {
      walls: 'textured-beige',
      ceiling: 'coffered-white',
      floor: 'marble-tiles'
    }
  },
  {
    id: 'cozy-bedroom',
    name: 'Cozy Bedroom',
    category: 'Residential',
    description: 'Warm and inviting bedroom with comfortable furnishings',
    thumbnail: 'https://images.unsplash.com/photo-1505693416388-ac5ce068fe85?w=400&h=300&fit=crop',
    features: ['Comfortable bed', 'Warm lighting', 'Cozy atmosphere', 'Natural materials'],
    defaultMaterials: {
      walls: 'warm-beige',
      ceiling: 'smooth-white',
      floor: 'oak-hardwood'
    }
  },
  {
    id: 'executive-office',
    name: 'Executive Office',
    category: 'Office',
    description: 'Professional workspace with executive-level amenities',
    thumbnail: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=400&h=300&fit=crop',
    features: ['Executive desk', 'Meeting area', 'Built-in storage', 'Professional lighting'],
    defaultMaterials: {
      walls: 'wood-paneling',
      ceiling: 'suspended-grid',
      floor: 'carpet-premium'
    }
  },
  {
    id: 'modern-office',
    name: 'Modern Office',
    category: 'Office',
    description: 'Contemporary office space with clean design and natural light',
    thumbnail: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=400&h=300&fit=crop',
    features: ['Modern desk', 'Natural light', 'Minimalist design', 'Tech integration'],
    defaultMaterials: {
      walls: 'white-modern',
      ceiling: 'smooth-white',
      floor: 'polished-concrete'
    }
  },
  {
    id: 'hotel-suite',
    name: 'Luxury Hotel Suite',
    category: 'Hospitality',
    description: 'Five-star hotel suite with premium amenities and elegant design',
    thumbnail: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=400&h=300&fit=crop',
    features: ['King bed', 'Sitting area', 'Mini bar', 'City view'],
    defaultMaterials: {
      walls: 'luxury-wallpaper',
      ceiling: 'tray-ceiling',
      floor: 'luxury-carpet'
    }
  },
  {
    id: 'modern-kitchen',
    name: 'Modern Kitchen',
    category: 'Residential',
    description: 'Sleek kitchen with contemporary appliances and clean lines',
    thumbnail: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop',
    features: ['Modern appliances', 'Island counter', 'Clean lines', 'Ample storage'],
    defaultMaterials: {
      walls: 'white-modern',
      ceiling: 'smooth-white',
      floor: 'polished-tile'
    }
  },
  {
    id: 'restaurant-dining',
    name: 'Fine Dining Restaurant',
    category: 'Restaurant',
    description: 'Upscale restaurant with sophisticated ambiance and elegant design',
    thumbnail: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop',
    features: ['Premium seating', 'Ambient lighting', 'Open kitchen view', 'Wine display'],
    defaultMaterials: {
      walls: 'brick-exposed',
      ceiling: 'wood-beams',
      floor: 'hardwood-dark'
    }
  }
];

export default function VRGallery() {
  const [selectedRoom, setSelectedRoom] = useState<RoomType | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  const categories = ['All', ...Array.from(new Set(roomTypes.map(room => room.category)))];
  
  const filteredRooms = selectedCategory === 'All' 
    ? roomTypes 
    : roomTypes.filter(room => room.category === selectedCategory);

  const handleRoomSelect = (room: RoomType) => {
    setSelectedRoom(room);
  };

  const handleBackToGallery = () => {
    setSelectedRoom(null);
  };

  if (selectedRoom) {
    return (
      <VRRoomViewer 
        room={selectedRoom} 
        onBack={handleBackToGallery}
      />
    );
  }

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <h1>🏛️ Explore VR Gallery</h1>
        <p>Step into immersive 3D rooms and customize them in real-time</p>
      </div>

      {/* Category Filter */}
      <div className={styles.categoryFilter}>
        {categories.map(category => (
          <button
            key={category}
            className={`${styles.categoryBtn} ${selectedCategory === category ? styles.active : ''}`}
            onClick={() => setSelectedCategory(category)}
          >
            {category}
          </button>
        ))}
      </div>

      {/* Room Grid */}
      <div className={styles.roomGrid}>
        {filteredRooms.map(room => (
          <div
            key={room.id}
            className={styles.roomCard}
            onClick={() => handleRoomSelect(room)}
          >
            <div className={styles.roomThumbnail}>
              <img
                src={room.thumbnail}
                alt={room.name}
                className={styles.roomImage}
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling.style.display = 'flex';
                }}
              />
              <div className={styles.placeholderImage} style={{ display: 'none' }}>
                🏠 {room.name}
              </div>
              <div className={styles.roomCategory}>{room.category}</div>
            </div>
            
            <div className={styles.roomInfo}>
              <h3>{room.name}</h3>
              <p>{room.description}</p>
              
              <div className={styles.roomFeatures}>
                {room.features.slice(0, 3).map((feature, index) => (
                  <span key={index} className={styles.feature}>
                    {feature}
                  </span>
                ))}
              </div>
              
              <button className={styles.exploreBtn}>
                🚀 Explore in VR
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Info Section */}
      <div className={styles.infoSection}>
        <div className={styles.infoCard}>
          <h3>🎮 Interactive Experience</h3>
          <p>Navigate through rooms like a video game with WASD controls or mouse navigation</p>
        </div>
        <div className={styles.infoCard}>
          <h3>🎨 Real-time Customization</h3>
          <p>Change wall colors, ceiling designs, and materials instantly</p>
        </div>
        <div className={styles.infoCard}>
          <h3>📱 Immersive Design</h3>
          <p>Experience realistic lighting, shadows, and materials</p>
        </div>
      </div>
    </div>
  );
}
