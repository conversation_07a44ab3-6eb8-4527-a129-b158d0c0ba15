'use client';

import React from 'react';
import { RoomType } from './VRGallery';
import RealisticRoomBuilder from '../RealisticRoomBuilder/RealisticRoomBuilder';
import AIRoomEditor from '../AIRoomEditor/AIRoomEditor';
import styles from './VRRoomViewer.module.css';

interface VRRoomViewerProps {
  room: RoomType;
  onBack: () => void;
}

export default function VRRoomViewer({ room, onBack }: VRRoomViewerProps) {
  // Use specific builder based on room type
  const renderRoomBuilder = () => {
    if (room.id === 'cozy-bedroom') {
      return <AIRoomEditor />;
    }
    // Default to general room builder for other room types
    return <RealisticRoomBuilder />;
  };

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <button className={styles.backBtn} onClick={onBack}>
          ← Back to Gallery
        </button>
        <h1>{room.name} - {room.id === 'cozy-bedroom' ? 'AI Room Editor' : 'Realistic Builder'}</h1>
        <div className={styles.roomInfo}>
          <span>{room.category}</span>
        </div>
      </div>

      {/* Room Builder */}
      {renderRoomBuilder()}
    </div>
  );
}
