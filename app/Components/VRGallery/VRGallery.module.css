.container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  color: white;
}

.header h1 {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
}

/* Category Filter */
.categoryFilter {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.categoryBtn {
  padding: 0.75rem 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.categoryBtn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.categoryBtn.active {
  background: white;
  color: #667eea;
  border-color: white;
}

/* Room Grid */
.roomGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.roomCard {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.roomCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.roomThumbnail {
  position: relative;
  height: 200px;
  background: linear-gradient(45deg, #f8f9fa, #e9ecef);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.roomImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0;
  transition: transform 0.3s ease;
}

.roomImage:hover {
  transform: scale(1.05);
}

.placeholderImage {
  font-size: 1.2rem;
  color: #6c757d;
  text-align: center;
  font-weight: 600;
}

.roomCategory {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(102, 126, 234, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.roomInfo {
  padding: 1.5rem;
}

.roomInfo h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  color: #2c3e50;
  font-weight: 700;
}

.roomInfo p {
  margin: 0 0 1rem 0;
  color: #6c757d;
  line-height: 1.5;
}

.roomFeatures {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.feature {
  background: #f8f9fa;
  color: #495057;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.exploreBtn {
  width: 100%;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.exploreBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

/* Info Section */
.infoSection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.infoCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  color: white;
}

.infoCard h3 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  font-weight: 700;
}

.infoCard p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header h1 {
    font-size: 2rem;
  }

  .header p {
    font-size: 1rem;
  }

  .roomGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .categoryFilter {
    gap: 0.5rem;
  }

  .categoryBtn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .infoSection {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .infoCard {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .roomCard {
    margin: 0 0.5rem;
  }

  .roomInfo {
    padding: 1rem;
  }

  .roomInfo h3 {
    font-size: 1.1rem;
  }

  .exploreBtn {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
}
