using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// BedroomSceneSetup - Automatically creates a complete cozy bedroom scene
/// Run this script to generate the entire bedroom with furniture and lighting
/// </summary>
public class BedroomSceneSetup : MonoBehaviour
{
    [Header("Room Dimensions")]
    [SerializeField] private float roomWidth = 5f;
    [SerializeField] private float roomDepth = 4f;
    [SerializeField] private float roomHeight = 2.8f;
    [SerializeField] private float wallThickness = 0.1f;
    
    [Header("Materials")]
    [SerializeField] private Material defaultFloorMaterial;
    [SerializeField] private Material defaultWallMaterial;
    [SerializeField] private Material defaultCeilingMaterial;
    [SerializeField] private Material defaultFurnitureMaterial;
    
    [Header("Auto Setup")]
    [SerializeField] private bool createOnStart = false;
    [SerializeField] private bool setupLighting = true;
    [SerializeField] private bool setupCamera = true;
    [SerializeField] private bool setupUI = true;
    
    private GameObject roomParent;
    private GameObject furnitureParent;
    private GameObject lightingParent;
    
    private void Start()
    {
        if (createOnStart)
        {
            CreateCompleteBedroomScene();
        }
    }
    
    [ContextMenu("Create Complete Bedroom Scene")]
    public void CreateCompleteBedroomScene()
    {
        Debug.Log("🛏️ Creating complete cozy bedroom scene...");
        
        // Create parent objects
        CreateParentObjects();
        
        // Create room structure
        CreateRoomStructure();
        
        // Create furniture
        CreateBedroomFurniture();
        
        // Setup lighting
        if (setupLighting)
        {
            SetupBedroomLighting();
        }
        
        // Setup camera
        if (setupCamera)
        {
            SetupBedroomCamera();
        }
        
        // Setup UI
        if (setupUI)
        {
            SetupBedroomUI();
        }
        
        Debug.Log("✅ Cozy bedroom scene created successfully!");
    }
    
    private void CreateParentObjects()
    {
        // Create main bedroom parent
        roomParent = new GameObject("CozyBedroom");
        roomParent.transform.position = Vector3.zero;
        
        // Create sub-parents
        GameObject roomStructure = new GameObject("RoomStructure");
        roomStructure.transform.SetParent(roomParent.transform);
        
        furnitureParent = new GameObject("Furniture");
        furnitureParent.transform.SetParent(roomParent.transform);
        
        lightingParent = new GameObject("Lighting");
        lightingParent.transform.SetParent(roomParent.transform);
    }
    
    private void CreateRoomStructure()
    {
        GameObject roomStructure = GameObject.Find("RoomStructure");
        
        // Create floor
        GameObject floor = CreateFloor();
        floor.transform.SetParent(roomStructure.transform);
        
        // Create walls
        GameObject[] walls = CreateWalls();
        foreach (GameObject wall in walls)
        {
            wall.transform.SetParent(roomStructure.transform);
        }
        
        // Create ceiling
        GameObject ceiling = CreateCeiling();
        ceiling.transform.SetParent(roomStructure.transform);
    }
    
    private GameObject CreateFloor()
    {
        GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
        floor.name = "Floor";
        floor.transform.position = Vector3.zero;
        floor.transform.localScale = new Vector3(roomWidth / 10f, 1f, roomDepth / 10f);
        
        if (defaultFloorMaterial != null)
        {
            floor.GetComponent<Renderer>().material = defaultFloorMaterial;
        }
        
        return floor;
    }
    
    private GameObject[] CreateWalls()
    {
        GameObject[] walls = new GameObject[4];
        
        // North Wall
        walls[0] = CreateWall("Wall_North", 
            new Vector3(0, roomHeight / 2f, roomDepth / 2f), 
            new Vector3(roomWidth, roomHeight, wallThickness));
        
        // South Wall
        walls[1] = CreateWall("Wall_South", 
            new Vector3(0, roomHeight / 2f, -roomDepth / 2f), 
            new Vector3(roomWidth, roomHeight, wallThickness));
        
        // East Wall
        walls[2] = CreateWall("Wall_East", 
            new Vector3(roomWidth / 2f, roomHeight / 2f, 0), 
            new Vector3(wallThickness, roomHeight, roomDepth));
        
        // West Wall
        walls[3] = CreateWall("Wall_West", 
            new Vector3(-roomWidth / 2f, roomHeight / 2f, 0), 
            new Vector3(wallThickness, roomHeight, roomDepth));
        
        return walls;
    }
    
    private GameObject CreateWall(string name, Vector3 position, Vector3 scale)
    {
        GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        wall.name = name;
        wall.transform.position = position;
        wall.transform.localScale = scale;
        
        if (defaultWallMaterial != null)
        {
            wall.GetComponent<Renderer>().material = defaultWallMaterial;
        }
        
        return wall;
    }
    
    private GameObject CreateCeiling()
    {
        GameObject ceiling = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ceiling.name = "Ceiling";
        ceiling.transform.position = new Vector3(0, roomHeight, 0);
        ceiling.transform.rotation = Quaternion.Euler(180, 0, 0);
        ceiling.transform.localScale = new Vector3(roomWidth / 10f, 1f, roomDepth / 10f);
        
        if (defaultCeilingMaterial != null)
        {
            ceiling.GetComponent<Renderer>().material = defaultCeilingMaterial;
        }
        
        return ceiling;
    }
    
    private void CreateBedroomFurniture()
    {
        // Create bed
        CreateBed();
        
        // Create nightstands
        CreateNightstands();
        
        // Create dresser
        CreateDresser();
        
        // Create chair
        CreateChair();
        
        // Create decorations
        CreateDecorations();
    }
    
    private void CreateBed()
    {
        GameObject bedParent = new GameObject("Bed");
        bedParent.transform.SetParent(furnitureParent.transform);
        bedParent.transform.position = new Vector3(0, 0, 0.5f);
        
        // Bed frame
        GameObject bedFrame = GameObject.CreatePrimitive(PrimitiveType.Cube);
        bedFrame.name = "BedFrame";
        bedFrame.transform.SetParent(bedParent.transform);
        bedFrame.transform.localPosition = Vector3.zero;
        bedFrame.transform.localScale = new Vector3(2f, 0.3f, 1.5f);
        
        // Mattress
        GameObject mattress = GameObject.CreatePrimitive(PrimitiveType.Cube);
        mattress.name = "Mattress";
        mattress.transform.SetParent(bedParent.transform);
        mattress.transform.localPosition = new Vector3(0, 0.2f, 0);
        mattress.transform.localScale = new Vector3(1.9f, 0.2f, 1.4f);
        
        // Pillows
        for (int i = 0; i < 2; i++)
        {
            GameObject pillow = GameObject.CreatePrimitive(PrimitiveType.Cube);
            pillow.name = $"Pillow_{i + 1}";
            pillow.transform.SetParent(bedParent.transform);
            pillow.transform.localPosition = new Vector3((i - 0.5f) * 0.6f, 0.35f, 0.5f);
            pillow.transform.localScale = new Vector3(0.4f, 0.1f, 0.3f);
            
            if (defaultFurnitureMaterial != null)
            {
                pillow.GetComponent<Renderer>().material = defaultFurnitureMaterial;
            }
        }
        
        if (defaultFurnitureMaterial != null)
        {
            bedFrame.GetComponent<Renderer>().material = defaultFurnitureMaterial;
            mattress.GetComponent<Renderer>().material = defaultFurnitureMaterial;
        }
    }
    
    private void CreateNightstands()
    {
        // Left nightstand
        GameObject leftNightstand = GameObject.CreatePrimitive(PrimitiveType.Cube);
        leftNightstand.name = "Nightstand_Left";
        leftNightstand.transform.SetParent(furnitureParent.transform);
        leftNightstand.transform.position = new Vector3(-1.5f, 0.25f, 0.5f);
        leftNightstand.transform.localScale = new Vector3(0.5f, 0.5f, 0.4f);
        
        // Right nightstand
        GameObject rightNightstand = GameObject.CreatePrimitive(PrimitiveType.Cube);
        rightNightstand.name = "Nightstand_Right";
        rightNightstand.transform.SetParent(furnitureParent.transform);
        rightNightstand.transform.position = new Vector3(1.5f, 0.25f, 0.5f);
        rightNightstand.transform.localScale = new Vector3(0.5f, 0.5f, 0.4f);
        
        if (defaultFurnitureMaterial != null)
        {
            leftNightstand.GetComponent<Renderer>().material = defaultFurnitureMaterial;
            rightNightstand.GetComponent<Renderer>().material = defaultFurnitureMaterial;
        }
    }
    
    private void CreateDresser()
    {
        GameObject dresser = GameObject.CreatePrimitive(PrimitiveType.Cube);
        dresser.name = "Dresser";
        dresser.transform.SetParent(furnitureParent.transform);
        dresser.transform.position = new Vector3(-2f, 0.4f, -1.5f);
        dresser.transform.localScale = new Vector3(1.2f, 0.8f, 0.5f);
        
        if (defaultFurnitureMaterial != null)
        {
            dresser.GetComponent<Renderer>().material = defaultFurnitureMaterial;
        }
    }
    
    private void CreateChair()
    {
        GameObject chair = GameObject.CreatePrimitive(PrimitiveType.Cube);
        chair.name = "Chair";
        chair.transform.SetParent(furnitureParent.transform);
        chair.transform.position = new Vector3(2f, 0.25f, -1.2f);
        chair.transform.localScale = new Vector3(0.6f, 0.5f, 0.6f);
        
        if (defaultFurnitureMaterial != null)
        {
            chair.GetComponent<Renderer>().material = defaultFurnitureMaterial;
        }
    }
    
    private void CreateDecorations()
    {
        // Wall art (simple cubes as placeholders)
        GameObject painting = GameObject.CreatePrimitive(PrimitiveType.Cube);
        painting.name = "WallArt";
        painting.transform.SetParent(furnitureParent.transform);
        painting.transform.position = new Vector3(0, 1.5f, 1.95f);
        painting.transform.localScale = new Vector3(0.8f, 0.6f, 0.05f);
        
        // Plant
        GameObject plant = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        plant.name = "Plant";
        plant.transform.SetParent(furnitureParent.transform);
        plant.transform.position = new Vector3(-2.2f, 0.3f, 1.5f);
        plant.transform.localScale = new Vector3(0.2f, 0.6f, 0.2f);
        
        if (defaultFurnitureMaterial != null)
        {
            painting.GetComponent<Renderer>().material = defaultFurnitureMaterial;
            plant.GetComponent<Renderer>().material = defaultFurnitureMaterial;
        }
    }
    
    private void SetupBedroomLighting()
    {
        // Main directional light (window light)
        GameObject mainLight = new GameObject("MainLight");
        mainLight.transform.SetParent(lightingParent.transform);
        mainLight.transform.position = new Vector3(2, 3, 2);
        mainLight.transform.rotation = Quaternion.Euler(45f, -45f, 0);
        
        Light directionalLight = mainLight.AddComponent<Light>();
        directionalLight.type = LightType.Directional;
        directionalLight.color = new Color(1f, 0.95f, 0.8f); // Warm white
        directionalLight.intensity = 1.2f;
        directionalLight.shadows = LightShadows.Soft;
        
        // Ceiling light
        GameObject ceilingLight = new GameObject("CeilingLight");
        ceilingLight.transform.SetParent(lightingParent.transform);
        ceilingLight.transform.position = new Vector3(0, roomHeight - 0.1f, 0);
        
        Light pointLight = ceilingLight.AddComponent<Light>();
        pointLight.type = LightType.Point;
        pointLight.color = new Color(1f, 1f, 0.9f); // Cool white
        pointLight.intensity = 1.5f;
        pointLight.range = 6f;
        pointLight.shadows = LightShadows.Soft;
        
        // Bedside lamp
        GameObject bedsideLamp = new GameObject("BedsideLamp");
        bedsideLamp.transform.SetParent(lightingParent.transform);
        bedsideLamp.transform.position = new Vector3(1.5f, 0.8f, 0.5f);
        
        Light spotLight = bedsideLamp.AddComponent<Light>();
        spotLight.type = LightType.Spot;
        spotLight.color = new Color(1f, 0.9f, 0.7f); // Warm yellow
        spotLight.intensity = 2f;
        spotLight.range = 4f;
        spotLight.spotAngle = 45f;
        spotLight.shadows = LightShadows.Soft;
    }
    
    private void SetupBedroomCamera()
    {
        GameObject cameraObj = GameObject.Find("Main Camera");
        if (cameraObj == null)
        {
            cameraObj = new GameObject("Main Camera");
            cameraObj.AddComponent<Camera>();
            cameraObj.tag = "MainCamera";
        }
        
        // Add camera controller
        BedroomCameraController cameraController = cameraObj.GetComponent<BedroomCameraController>();
        if (cameraController == null)
        {
            cameraController = cameraObj.AddComponent<BedroomCameraController>();
        }
        
        // Position camera
        cameraObj.transform.position = new Vector3(3, 2, -3);
        cameraObj.transform.LookAt(new Vector3(0, 1, 0));
    }
    
    private void SetupBedroomUI()
    {
        // Create UI Canvas
        GameObject canvasObj = new GameObject("BedroomUI");
        Canvas canvas = canvasObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 100;
        
        CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        
        canvasObj.AddComponent<GraphicRaycaster>();
        
        // Add material manager
        GameObject materialManagerObj = new GameObject("MaterialManager");
        BedroomMaterialManager materialManager = materialManagerObj.AddComponent<BedroomMaterialManager>();
        
        // Setup references (these would need to be assigned in inspector)
        Debug.Log("⚠️ Remember to assign material and UI references in the inspector!");
    }
    
    [ContextMenu("Clear Bedroom Scene")]
    public void ClearBedroomScene()
    {
        if (roomParent != null)
        {
            DestroyImmediate(roomParent);
        }
        
        Debug.Log("🧹 Bedroom scene cleared");
    }
}
