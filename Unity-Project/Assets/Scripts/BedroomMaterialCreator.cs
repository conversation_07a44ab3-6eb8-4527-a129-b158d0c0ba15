using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// BedroomMaterialCreator - Automatically creates all bedroom materials
/// Run this to generate realistic materials for floor, walls, and ceiling
/// </summary>
public class BedroomMaterialCreator : MonoBehaviour
{
    [Header("Material Settings")]
    [SerializeField] private bool createOnStart = false;
    [SerializeField] private string materialFolderPath = "Assets/Materials/Bedroom/";
    
    [Header("Floor Material Colors")]
    [SerializeField] private Color hardwoodOakColor = new Color(0.55f, 0.27f, 0.07f); // #8B4513
    [SerializeField] private Color cozyCarpetColor = new Color(0.96f, 0.87f, 0.70f); // #F5DEB3
    [SerializeField] private Color modernLaminateColor = new Color(0.41f, 0.41f, 0.41f); // #696969
    [SerializeField] private Color bambooFlooringColor = new Color(0.87f, 0.72f, 0.53f); // #DEB887
    
    [Header("Wall Material Colors")]
    [SerializeField] private Color warmCreamColor = new Color(0.96f, 0.96f, 0.86f); // #F5F5DC
    [SerializeField] private Color floralWallpaperColor = new Color(1.0f, 0.71f, 0.76f); // #FFB6C1
    [SerializeField] private Color woodPanelingColor = new Color(0.55f, 0.45f, 0.33f); // #8B7355
    [SerializeField] private Color calmingBlueColor = new Color(0.53f, 0.81f, 0.92f); // #87CEEB
    
    [Header("Ceiling Material Colors")]
    [SerializeField] private Color classicWhiteColor = new Color(1.0f, 1.0f, 1.0f); // #FFFFFF
    [SerializeField] private Color exposedBeamsColor = new Color(0.87f, 0.72f, 0.53f); // #DEB887
    [SerializeField] private Color texturedCreamColor = new Color(1.0f, 0.97f, 0.86f); // #FFF8DC
    [SerializeField] private Color cofferedDesignColor = new Color(0.94f, 0.90f, 0.55f); // #F0E68C
    
    private void Start()
    {
        if (createOnStart)
        {
            CreateAllBedroomMaterials();
        }
    }
    
    [ContextMenu("Create All Bedroom Materials")]
    public void CreateAllBedroomMaterials()
    {
        Debug.Log("🎨 Creating all bedroom materials...");
        
#if UNITY_EDITOR
        // Ensure material folders exist
        CreateMaterialFolders();
        
        // Create floor materials
        CreateFloorMaterials();
        
        // Create wall materials
        CreateWallMaterials();
        
        // Create ceiling materials
        CreateCeilingMaterials();
        
        // Create furniture materials
        CreateFurnitureMaterials();
        
        // Refresh asset database
        AssetDatabase.Refresh();
        
        Debug.Log("✅ All bedroom materials created successfully!");
#else
        Debug.LogWarning("⚠️ Material creation only works in Unity Editor");
#endif
    }
    
#if UNITY_EDITOR
    private void CreateMaterialFolders()
    {
        string[] folders = {
            materialFolderPath,
            materialFolderPath + "Floors/",
            materialFolderPath + "Walls/",
            materialFolderPath + "Ceilings/",
            materialFolderPath + "Furniture/"
        };
        
        foreach (string folder in folders)
        {
            if (!AssetDatabase.IsValidFolder(folder))
            {
                string parentFolder = System.IO.Path.GetDirectoryName(folder);
                string folderName = System.IO.Path.GetFileName(folder);
                AssetDatabase.CreateFolder(parentFolder, folderName);
            }
        }
    }
    
    private void CreateFloorMaterials()
    {
        Debug.Log("🏗️ Creating floor materials...");
        
        // Hardwood Oak
        Material hardwoodOak = CreateBasicMaterial("HardwoodOak", hardwoodOakColor);
        hardwoodOak.SetFloat("_Metallic", 0.1f);
        hardwoodOak.SetFloat("_Smoothness", 0.7f);
        SaveMaterial(hardwoodOak, "Floors/HardwoodOak");
        
        // Cozy Carpet
        Material cozyCarpet = CreateBasicMaterial("CozyCarpet", cozyCarpetColor);
        cozyCarpet.SetFloat("_Metallic", 0.0f);
        cozyCarpet.SetFloat("_Smoothness", 0.2f);
        SaveMaterial(cozyCarpet, "Floors/CozyCarpet");
        
        // Modern Laminate
        Material modernLaminate = CreateBasicMaterial("ModernLaminate", modernLaminateColor);
        modernLaminate.SetFloat("_Metallic", 0.0f);
        modernLaminate.SetFloat("_Smoothness", 0.8f);
        SaveMaterial(modernLaminate, "Floors/ModernLaminate");
        
        // Bamboo Flooring
        Material bambooFlooring = CreateBasicMaterial("BambooFlooring", bambooFlooringColor);
        bambooFlooring.SetFloat("_Metallic", 0.1f);
        bambooFlooring.SetFloat("_Smoothness", 0.6f);
        SaveMaterial(bambooFlooring, "Floors/BambooFlooring");
    }
    
    private void CreateWallMaterials()
    {
        Debug.Log("🏠 Creating wall materials...");
        
        // Warm Cream
        Material warmCream = CreateBasicMaterial("WarmCream", warmCreamColor);
        warmCream.SetFloat("_Metallic", 0.0f);
        warmCream.SetFloat("_Smoothness", 0.3f);
        SaveMaterial(warmCream, "Walls/WarmCream");
        
        // Floral Wallpaper
        Material floralWallpaper = CreateBasicMaterial("FloralWallpaper", floralWallpaperColor);
        floralWallpaper.SetFloat("_Metallic", 0.0f);
        floralWallpaper.SetFloat("_Smoothness", 0.4f);
        SaveMaterial(floralWallpaper, "Walls/FloralWallpaper");
        
        // Wood Paneling
        Material woodPaneling = CreateBasicMaterial("WoodPaneling", woodPanelingColor);
        woodPaneling.SetFloat("_Metallic", 0.1f);
        woodPaneling.SetFloat("_Smoothness", 0.6f);
        SaveMaterial(woodPaneling, "Walls/WoodPaneling");
        
        // Calming Blue
        Material calmingBlue = CreateBasicMaterial("CalmingBlue", calmingBlueColor);
        calmingBlue.SetFloat("_Metallic", 0.0f);
        calmingBlue.SetFloat("_Smoothness", 0.3f);
        SaveMaterial(calmingBlue, "Walls/CalmingBlue");
    }
    
    private void CreateCeilingMaterials()
    {
        Debug.Log("🏘️ Creating ceiling materials...");
        
        // Classic White
        Material classicWhite = CreateBasicMaterial("ClassicWhite", classicWhiteColor);
        classicWhite.SetFloat("_Metallic", 0.0f);
        classicWhite.SetFloat("_Smoothness", 0.4f);
        SaveMaterial(classicWhite, "Ceilings/ClassicWhite");
        
        // Exposed Beams
        Material exposedBeams = CreateBasicMaterial("ExposedBeams", exposedBeamsColor);
        exposedBeams.SetFloat("_Metallic", 0.1f);
        exposedBeams.SetFloat("_Smoothness", 0.5f);
        SaveMaterial(exposedBeams, "Ceilings/ExposedBeams");
        
        // Textured Cream
        Material texturedCream = CreateBasicMaterial("TexturedCream", texturedCreamColor);
        texturedCream.SetFloat("_Metallic", 0.0f);
        texturedCream.SetFloat("_Smoothness", 0.3f);
        SaveMaterial(texturedCream, "Ceilings/TexturedCream");
        
        // Coffered Design
        Material cofferedDesign = CreateBasicMaterial("CofferedDesign", cofferedDesignColor);
        cofferedDesign.SetFloat("_Metallic", 0.1f);
        cofferedDesign.SetFloat("_Smoothness", 0.6f);
        SaveMaterial(cofferedDesign, "Ceilings/CofferedDesign");
    }
    
    private void CreateFurnitureMaterials()
    {
        Debug.Log("🪑 Creating furniture materials...");
        
        // Dark Wood (for bed frame, nightstands, dresser)
        Material darkWood = CreateBasicMaterial("DarkWood", new Color(0.24f, 0.14f, 0.08f));
        darkWood.SetFloat("_Metallic", 0.2f);
        darkWood.SetFloat("_Smoothness", 0.8f);
        SaveMaterial(darkWood, "Furniture/DarkWood");
        
        // Fabric (for mattress, pillows)
        Material fabric = CreateBasicMaterial("Fabric", new Color(0.9f, 0.9f, 0.85f));
        fabric.SetFloat("_Metallic", 0.0f);
        fabric.SetFloat("_Smoothness", 0.2f);
        SaveMaterial(fabric, "Furniture/Fabric");
        
        // Metal (for lamp, handles)
        Material metal = CreateBasicMaterial("Metal", new Color(0.75f, 0.75f, 0.75f));
        metal.SetFloat("_Metallic", 0.9f);
        metal.SetFloat("_Smoothness", 0.8f);
        SaveMaterial(metal, "Furniture/Metal");
        
        // Plant Green
        Material plantGreen = CreateBasicMaterial("PlantGreen", new Color(0.2f, 0.6f, 0.2f));
        plantGreen.SetFloat("_Metallic", 0.0f);
        plantGreen.SetFloat("_Smoothness", 0.3f);
        SaveMaterial(plantGreen, "Furniture/PlantGreen");
    }
    
    private Material CreateBasicMaterial(string materialName, Color baseColor)
    {
        Material material = new Material(Shader.Find("Universal Render Pipeline/Lit"));
        material.name = materialName;
        material.SetColor("_BaseColor", baseColor);
        material.SetFloat("_Metallic", 0.0f);
        material.SetFloat("_Smoothness", 0.5f);
        
        return material;
    }
    
    private void SaveMaterial(Material material, string relativePath)
    {
        string fullPath = materialFolderPath + relativePath + ".mat";
        AssetDatabase.CreateAsset(material, fullPath);
        Debug.Log($"💾 Created material: {material.name} at {fullPath}");
    }
    
    [ContextMenu("Create Material Folders Only")]
    public void CreateMaterialFoldersOnly()
    {
        CreateMaterialFolders();
        AssetDatabase.Refresh();
        Debug.Log("📁 Material folders created");
    }
    
    [ContextMenu("Delete All Materials")]
    public void DeleteAllMaterials()
    {
        if (AssetDatabase.IsValidFolder(materialFolderPath))
        {
            AssetDatabase.DeleteAsset(materialFolderPath);
            AssetDatabase.Refresh();
            Debug.Log("🗑️ All bedroom materials deleted");
        }
    }
#endif
    
    // Runtime material creation (simplified)
    public Material[] CreateRuntimeFloorMaterials()
    {
        Material[] materials = new Material[4];
        
        materials[0] = CreateBasicMaterial("HardwoodOak", hardwoodOakColor);
        materials[1] = CreateBasicMaterial("CozyCarpet", cozyCarpetColor);
        materials[2] = CreateBasicMaterial("ModernLaminate", modernLaminateColor);
        materials[3] = CreateBasicMaterial("BambooFlooring", bambooFlooringColor);
        
        return materials;
    }
    
    public Material[] CreateRuntimeWallMaterials()
    {
        Material[] materials = new Material[4];
        
        materials[0] = CreateBasicMaterial("WarmCream", warmCreamColor);
        materials[1] = CreateBasicMaterial("FloralWallpaper", floralWallpaperColor);
        materials[2] = CreateBasicMaterial("WoodPaneling", woodPanelingColor);
        materials[3] = CreateBasicMaterial("CalmingBlue", calmingBlueColor);
        
        return materials;
    }
    
    public Material[] CreateRuntimeCeilingMaterials()
    {
        Material[] materials = new Material[4];
        
        materials[0] = CreateBasicMaterial("ClassicWhite", classicWhiteColor);
        materials[1] = CreateBasicMaterial("ExposedBeams", exposedBeamsColor);
        materials[2] = CreateBasicMaterial("TexturedCream", texturedCreamColor);
        materials[3] = CreateBasicMaterial("CofferedDesign", cofferedDesignColor);
        
        return materials;
    }
}
