using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// BedroomCameraController - Complete camera system for cozy bedroom
/// Includes smooth controls, presets, and built-in UI
/// </summary>
public class BedroomCameraController : MonoBehaviour
{
    [Header("Camera Settings")]
    [SerializeField] private Transform target;
    [SerializeField] private float distance = 5f;
    [SerializeField] private float minDistance = 2f;
    [SerializeField] private float maxDistance = 10f;
    
    [Head<PERSON>("Rotation Settings")]
    [SerializeField] private float rotationSpeed = 2f;
    [SerializeField] private float minVerticalAngle = 5f;
    [SerializeField] private float maxVerticalAngle = 80f;
    
    [Header("Zoom Settings")]
    [SerializeField] private float zoomSpeed = 2f;
    [SerializeField] private float zoomSmoothTime = 0.3f;
    
    [Header("Smoothing")]
    [SerializeField] private float rotationSmoothTime = 0.1f;
    [SerializeField] private bool enableSmoothing = true;
    
    [Header("Camera Presets")]
    [SerializeField] private CameraPreset[] cameraPresets = {
        new CameraPreset("Overview", 45f, 30f, 6f),
        new CameraPreset("Bed Focus", 30f, 20f, 3f),
        new CameraPreset("Corner View", 60f, 35f, 5f),
        new CameraPreset("Detail View", 0f, 15f, 2f)
    };
    
    [Header("UI Controls")]
    [SerializeField] private Canvas cameraUICanvas;
    [SerializeField] private Transform cameraUIParent;
    [SerializeField] private Text statusText;
    
    // Private variables
    private float currentX = 45f;
    private float currentY = 30f;
    private float targetDistance;
    private float distanceVelocity;
    
    // Smoothing variables
    private Vector2 rotationVelocity;
    private Vector2 currentRotation;
    private Vector2 targetRotation;
    
    // Input tracking
    private bool isRotating = false;
    private Vector3 lastMousePosition;
    
    // UI Components
    private GameObject cameraPanel;
    private Slider zoomSlider;
    private Toggle smoothingToggle;
    private Button[] presetButtons;
    private bool isUIVisible = true;
    
    // Default values
    private float defaultX = 45f;
    private float defaultY = 30f;
    private float defaultDistance = 5f;
    
    private void Start()
    {
        // Initialize camera
        if (target == null)
        {
            GameObject targetGO = new GameObject("BedroomCameraTarget");
            targetGO.transform.position = new Vector3(0, 1.2f, 0);
            target = targetGO.transform;
        }
        
        targetDistance = distance;
        currentRotation = new Vector2(currentX, currentY);
        targetRotation = currentRotation;
        
        CreateCameraUI();
        UpdateCameraPosition();
        UpdateStatusText("Camera ready - Use mouse to rotate, scroll to zoom");
        
        Debug.Log("🎥 Bedroom Camera Controller initialized");
    }
    
    private void CreateCameraUI()
    {
        if (cameraUICanvas == null)
        {
            Debug.LogError("❌ Camera UI Canvas not assigned!");
            return;
        }
        
        // Create camera panel
        cameraPanel = new GameObject("CameraPanel");
        cameraPanel.transform.SetParent(cameraUIParent != null ? cameraUIParent : cameraUICanvas.transform, false);
        
        // Setup panel background
        Image panelBg = cameraPanel.AddComponent<Image>();
        panelBg.color = new Color(0, 0, 0, 0.8f);
        panelBg.raycastTarget = true;
        
        // Setup panel rect transform (bottom right)
        RectTransform panelRect = cameraPanel.GetComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0.65f, 0);
        panelRect.anchorMax = new Vector2(1f, 0.25f);
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // Add layout
        VerticalLayoutGroup layout = cameraPanel.AddComponent<VerticalLayoutGroup>();
        layout.padding = new RectOffset(15, 15, 15, 15);
        layout.spacing = 12;
        layout.childControlHeight = false;
        layout.childControlWidth = true;
        layout.childForceExpandWidth = true;
        
        // Create UI elements
        CreateCameraTitle("📷 Camera Controls", cameraPanel.transform);
        CreatePresetButtons();
        CreateZoomSlider();
        CreateControlButtons();
        
        Debug.Log("✅ Camera UI created successfully");
    }
    
    private void CreateCameraTitle(string titleText, Transform parent)
    {
        GameObject titleObj = new GameObject("CameraTitle");
        titleObj.transform.SetParent(parent, false);
        
        Text title = titleObj.AddComponent<Text>();
        title.text = titleText;
        title.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        title.fontSize = 18;
        title.fontStyle = FontStyle.Bold;
        title.color = new Color(1f, 0.84f, 0f);
        title.alignment = TextAnchor.MiddleCenter;
        
        Outline outline = titleObj.AddComponent<Outline>();
        outline.effectColor = Color.black;
        outline.effectDistance = new Vector2(1, 1);
        
        RectTransform titleRect = titleObj.GetComponent<RectTransform>();
        titleRect.sizeDelta = new Vector2(0, 25);
    }
    
    private void CreatePresetButtons()
    {
        // Create preset container
        GameObject presetContainer = new GameObject("PresetContainer");
        presetContainer.transform.SetParent(cameraPanel.transform, false);
        
        GridLayoutGroup gridLayout = presetContainer.AddComponent<GridLayoutGroup>();
        gridLayout.cellSize = new Vector2(80, 30);
        gridLayout.spacing = new Vector2(5, 5);
        gridLayout.constraint = GridLayoutGroup.Constraint.FixedColumnCount;
        gridLayout.constraintCount = 2;
        
        RectTransform presetRect = presetContainer.GetComponent<RectTransform>();
        presetRect.sizeDelta = new Vector2(0, 70);
        
        presetButtons = new Button[cameraPresets.Length];
        
        for (int i = 0; i < cameraPresets.Length; i++)
        {
            int presetIndex = i;
            GameObject buttonObj = new GameObject($"Preset{i}");
            buttonObj.transform.SetParent(presetContainer.transform, false);
            
            Image buttonBg = buttonObj.AddComponent<Image>();
            buttonBg.color = new Color(0.3f, 0.5f, 0.8f, 0.9f);
            buttonBg.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/UISprite.psd");
            buttonBg.type = Image.Type.Sliced;
            
            Button button = buttonObj.AddComponent<Button>();
            button.targetGraphic = buttonBg;
            button.onClick.AddListener(() => ApplyCameraPreset(presetIndex));
            
            presetButtons[i] = button;
            
            // Button text
            GameObject textObj = new GameObject("Text");
            textObj.transform.SetParent(buttonObj.transform, false);
            
            Text text = textObj.AddComponent<Text>();
            text.text = $"{i + 1}. {cameraPresets[i].presetName}";
            text.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            text.fontSize = 10;
            text.fontStyle = FontStyle.Bold;
            text.color = Color.white;
            text.alignment = TextAnchor.MiddleCenter;
            
            Outline textOutline = textObj.AddComponent<Outline>();
            textOutline.effectColor = Color.black;
            textOutline.effectDistance = new Vector2(1, 1);
            
            RectTransform textRect = textObj.GetComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;
        }
    }
    
    private void CreateZoomSlider()
    {
        // Create zoom container
        GameObject zoomContainer = new GameObject("ZoomContainer");
        zoomContainer.transform.SetParent(cameraPanel.transform, false);
        
        HorizontalLayoutGroup zoomLayout = zoomContainer.AddComponent<HorizontalLayoutGroup>();
        zoomLayout.spacing = 10;
        zoomLayout.childControlWidth = false;
        zoomLayout.childForceExpandWidth = false;
        
        RectTransform zoomRect = zoomContainer.GetComponent<RectTransform>();
        zoomRect.sizeDelta = new Vector2(0, 25);
        
        // Zoom label
        GameObject zoomLabel = new GameObject("ZoomLabel");
        zoomLabel.transform.SetParent(zoomContainer.transform, false);
        
        Text label = zoomLabel.AddComponent<Text>();
        label.text = "Zoom:";
        label.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        label.fontSize = 12;
        label.color = Color.white;
        label.alignment = TextAnchor.MiddleLeft;
        
        RectTransform labelRect = zoomLabel.GetComponent<RectTransform>();
        labelRect.sizeDelta = new Vector2(50, 25);
        
        // Zoom slider
        GameObject sliderObj = new GameObject("ZoomSlider");
        sliderObj.transform.SetParent(zoomContainer.transform, false);
        
        zoomSlider = sliderObj.AddComponent<Slider>();
        zoomSlider.minValue = minDistance;
        zoomSlider.maxValue = maxDistance;
        zoomSlider.value = distance;
        zoomSlider.onValueChanged.AddListener(OnZoomSliderChanged);
        
        // Slider background
        Image sliderBg = sliderObj.AddComponent<Image>();
        sliderBg.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);
        sliderBg.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/Background.psd");
        sliderBg.type = Image.Type.Sliced;
        
        // Slider handle
        GameObject handleSlideArea = new GameObject("Handle Slide Area");
        handleSlideArea.transform.SetParent(sliderObj.transform, false);
        
        RectTransform handleAreaRect = handleSlideArea.GetComponent<RectTransform>();
        handleAreaRect.sizeDelta = new Vector2(-20, 0);
        handleAreaRect.anchorMin = new Vector2(0, 0);
        handleAreaRect.anchorMax = new Vector2(1, 1);
        
        GameObject handle = new GameObject("Handle");
        handle.transform.SetParent(handleSlideArea.transform, false);
        
        Image handleImage = handle.AddComponent<Image>();
        handleImage.color = new Color(1f, 0.84f, 0f);
        handleImage.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/Knob.psd");
        
        zoomSlider.handleRect = handle.GetComponent<RectTransform>();
        zoomSlider.targetGraphic = handleImage;
        
        RectTransform sliderRect = sliderObj.GetComponent<RectTransform>();
        sliderRect.sizeDelta = new Vector2(120, 20);
    }
    
    private void CreateControlButtons()
    {
        // Create button container
        GameObject buttonContainer = new GameObject("CameraButtonContainer");
        buttonContainer.transform.SetParent(cameraPanel.transform, false);
        
        HorizontalLayoutGroup buttonLayout = buttonContainer.AddComponent<HorizontalLayoutGroup>();
        buttonLayout.spacing = 8;
        buttonLayout.childControlWidth = true;
        buttonLayout.childForceExpandWidth = true;
        
        RectTransform buttonContainerRect = buttonContainer.GetComponent<RectTransform>();
        buttonContainerRect.sizeDelta = new Vector2(0, 35);
        
        // Reset button
        CreateCameraButton("🔄 Reset", buttonContainer.transform, ResetCamera, new Color(0.8f, 0.3f, 0.3f));
        
        // Toggle smoothing button
        CreateCameraButton("⚙️ Smooth", buttonContainer.transform, ToggleSmoothing, new Color(0.3f, 0.7f, 0.3f));
    }
    
    private void CreateCameraButton(string buttonText, Transform parent, UnityEngine.Events.UnityAction onClick, Color buttonColor)
    {
        GameObject buttonObj = new GameObject("CameraButton_" + buttonText);
        buttonObj.transform.SetParent(parent, false);
        
        Image buttonBg = buttonObj.AddComponent<Image>();
        buttonBg.color = buttonColor;
        buttonBg.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/UISprite.psd");
        buttonBg.type = Image.Type.Sliced;
        
        Button button = buttonObj.AddComponent<Button>();
        button.targetGraphic = buttonBg;
        button.onClick.AddListener(onClick);
        
        // Button text
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(buttonObj.transform, false);
        
        Text text = textObj.AddComponent<Text>();
        text.text = buttonText;
        text.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        text.fontSize = 11;
        text.fontStyle = FontStyle.Bold;
        text.color = Color.white;
        text.alignment = TextAnchor.MiddleCenter;
        
        Outline textOutline = textObj.AddComponent<Outline>();
        textOutline.effectColor = Color.black;
        textOutline.effectDistance = new Vector2(1, 1);
        
        RectTransform textRect = textObj.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        RectTransform buttonRect = buttonObj.GetComponent<RectTransform>();
        buttonRect.sizeDelta = new Vector2(0, 30);
    }
    
    private void Update()
    {
        HandleInput();
        UpdateCameraMovement();
    }
    
    private void HandleInput()
    {
        // Mouse rotation
        if (Input.GetMouseButtonDown(0))
        {
            isRotating = true;
            lastMousePosition = Input.mousePosition;
        }
        
        if (Input.GetMouseButtonUp(0))
        {
            isRotating = false;
        }
        
        if (isRotating && Input.GetMouseButton(0))
        {
            Vector3 mouseDelta = Input.mousePosition - lastMousePosition;
            
            targetRotation.x += mouseDelta.x * rotationSpeed * 0.1f;
            targetRotation.y -= mouseDelta.y * rotationSpeed * 0.1f;
            targetRotation.y = Mathf.Clamp(targetRotation.y, minVerticalAngle, maxVerticalAngle);
            
            lastMousePosition = Input.mousePosition;
        }
        
        // Mouse wheel zoom
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (Mathf.Abs(scroll) > 0.01f)
        {
            targetDistance -= scroll * zoomSpeed;
            targetDistance = Mathf.Clamp(targetDistance, minDistance, maxDistance);
            
            if (zoomSlider != null)
            {
                zoomSlider.value = targetDistance;
            }
        }
        
        // Keyboard shortcuts
        HandleKeyboardInput();
    }
    
    private void HandleKeyboardInput()
    {
        // Number keys for presets
        for (int i = 1; i <= cameraPresets.Length && i <= 4; i++)
        {
            if (Input.GetKeyDown(KeyCode.Alpha0 + i))
            {
                ApplyCameraPreset(i - 1);
            }
        }
        
        if (Input.GetKeyDown(KeyCode.R))
        {
            ResetCamera();
        }
        
        if (Input.GetKeyDown(KeyCode.C))
        {
            ToggleCameraUI();
        }
    }
    
    private void UpdateCameraMovement()
    {
        if (enableSmoothing)
        {
            currentRotation = Vector2.SmoothDamp(currentRotation, targetRotation, ref rotationVelocity, rotationSmoothTime);
        }
        else
        {
            currentRotation = targetRotation;
        }
        
        currentX = currentRotation.x;
        currentY = currentRotation.y;
        
        distance = Mathf.SmoothDamp(distance, targetDistance, ref distanceVelocity, zoomSmoothTime);
        
        UpdateCameraPosition();
    }
    
    private void UpdateCameraPosition()
    {
        if (target == null) return;
        
        Quaternion rotation = Quaternion.Euler(currentY, currentX, 0);
        Vector3 direction = rotation * Vector3.back;
        Vector3 position = target.position + direction * distance;
        
        transform.position = position;
        transform.LookAt(target.position);
    }
    
    // UI Event Handlers
    private void OnZoomSliderChanged(float value)
    {
        targetDistance = value;
    }
    
    public void ApplyCameraPreset(int presetIndex)
    {
        if (presetIndex >= 0 && presetIndex < cameraPresets.Length)
        {
            CameraPreset preset = cameraPresets[presetIndex];
            
            targetRotation.x = preset.rotationX;
            targetRotation.y = preset.rotationY;
            targetDistance = preset.distance;
            
            if (zoomSlider != null)
            {
                zoomSlider.value = targetDistance;
            }
            
            UpdateStatusText($"Camera preset: {preset.presetName}");
            Debug.Log($"📷 Applied camera preset: {preset.presetName}");
        }
    }
    
    public void ResetCamera()
    {
        targetRotation.x = defaultX;
        targetRotation.y = defaultY;
        targetDistance = defaultDistance;
        
        if (zoomSlider != null)
        {
            zoomSlider.value = targetDistance;
        }
        
        UpdateStatusText("Camera reset to default position");
        Debug.Log("🔄 Camera reset to default position");
    }
    
    public void ToggleSmoothing()
    {
        enableSmoothing = !enableSmoothing;
        UpdateStatusText($"Camera smoothing: {(enableSmoothing ? "ON" : "OFF")}");
        Debug.Log($"⚙️ Camera smoothing: {(enableSmoothing ? "ON" : "OFF")}");
    }
    
    public void ToggleCameraUI()
    {
        isUIVisible = !isUIVisible;
        if (cameraPanel != null)
        {
            cameraPanel.SetActive(isUIVisible);
        }
        UpdateStatusText(isUIVisible ? "Camera UI shown" : "Camera UI hidden");
    }
    
    private void UpdateStatusText(string message)
    {
        if (statusText != null)
        {
            statusText.text = message;
        }
    }
}

[System.Serializable]
public class CameraPreset
{
    public string presetName;
    public float rotationX;
    public float rotationY;
    public float distance;
    
    public CameraPreset(string name, float x, float y, float dist)
    {
        presetName = name;
        rotationX = x;
        rotationY = y;
        distance = dist;
    }
}
