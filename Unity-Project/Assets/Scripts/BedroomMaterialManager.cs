using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// BedroomMaterialManager - Complete material management system for cozy bedroom
/// Handles all material changes with built-in Unity UI controls
/// </summary>
public class BedroomMaterialManager : MonoBehaviour
{
    [Header("Room Renderers")]
    [SerializeField] private Renderer floorRenderer;
    [SerializeField] private Renderer[] wallRenderers;
    [SerializeField] private Renderer ceilingRenderer;
    [SerializeField] private Renderer[] furnitureRenderers;
    
    [Header("Floor Materials")]
    [SerializeField] private Material[] floorMaterials;
    [SerializeField] private string[] floorMaterialNames = {
        "Hardwood Oak", "Cozy Carpet", "Modern Laminate", "Bamboo Flooring"
    };
    
    [Header("Wall Materials")]
    [SerializeField] private Material[] wallMaterials;
    [SerializeField] private string[] wallMaterialNames = {
        "Warm Cream", "Floral Wallpaper", "Wood Paneling", "Calming Blue"
    };
    
    [Header("Ceiling Materials")]
    [SerializeField] private Material[] ceilingMaterials;
    [SerializeField] private string[] ceilingMaterialNames = {
        "Classic White", "Exposed Beams", "Textured Cream", "Coffered Design"
    };
    
    [Header("UI References")]
    [SerializeField] private Canvas materialUICanvas;
    [SerializeField] private Transform uiParent;
    [SerializeField] private Button toggleUIButton;
    [SerializeField] private Text statusText;
    
    // UI Components
    private Dropdown floorDropdown;
    private Dropdown wallDropdown;
    private Dropdown ceilingDropdown;
    private GameObject materialPanel;
    private bool isUIVisible = true;
    
    // Current material indices
    private int currentFloorIndex = 0;
    private int currentWallIndex = 0;
    private int currentCeilingIndex = 0;
    
    private void Start()
    {
        Debug.Log("🛏️ Bedroom Material Manager initialized");
        CreateMaterialUI();
        InitializeDefaultMaterials();
        
        if (toggleUIButton != null)
        {
            toggleUIButton.onClick.AddListener(ToggleUI);
        }
        
        UpdateStatusText("Bedroom materials ready!");
    }
    
    private void CreateMaterialUI()
    {
        if (materialUICanvas == null)
        {
            Debug.LogError("❌ Material UI Canvas not assigned!");
            return;
        }
        
        // Create main material panel
        materialPanel = new GameObject("MaterialPanel");
        materialPanel.transform.SetParent(uiParent != null ? uiParent : materialUICanvas.transform, false);
        
        // Setup panel background
        Image panelBg = materialPanel.AddComponent<Image>();
        panelBg.color = new Color(0, 0, 0, 0.85f);
        panelBg.raycastTarget = true;
        
        // Setup panel rect transform
        RectTransform panelRect = materialPanel.GetComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0, 0);
        panelRect.anchorMax = new Vector2(0.3f, 1);
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // Add content size fitter
        ContentSizeFitter sizeFitter = materialPanel.AddComponent<ContentSizeFitter>();
        sizeFitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;
        
        // Add vertical layout
        VerticalLayoutGroup layout = materialPanel.AddComponent<VerticalLayoutGroup>();
        layout.padding = new RectOffset(20, 20, 20, 20);
        layout.spacing = 20;
        layout.childControlHeight = false;
        layout.childControlWidth = true;
        layout.childForceExpandWidth = true;
        layout.childAlignment = TextAnchor.UpperCenter;
        
        // Create UI elements
        CreateUITitle("🛏️ Bedroom Materials", materialPanel.transform);
        floorDropdown = CreateMaterialDropdown("Floor Material", floorMaterialNames, materialPanel.transform, OnFloorMaterialChanged);
        wallDropdown = CreateMaterialDropdown("Wall Material", wallMaterialNames, materialPanel.transform, OnWallMaterialChanged);
        ceilingDropdown = CreateMaterialDropdown("Ceiling Material", ceilingMaterialNames, materialPanel.transform, OnCeilingMaterialChanged);
        CreateControlButtons();
        
        Debug.Log("✅ Material UI created successfully");
    }
    
    private void CreateUITitle(string titleText, Transform parent)
    {
        GameObject titleObj = new GameObject("Title");
        titleObj.transform.SetParent(parent, false);
        
        Text title = titleObj.AddComponent<Text>();
        title.text = titleText;
        title.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        title.fontSize = 22;
        title.fontStyle = FontStyle.Bold;
        title.color = new Color(1f, 0.84f, 0f); // Gold
        title.alignment = TextAnchor.MiddleCenter;
        
        // Add outline for better visibility
        Outline outline = titleObj.AddComponent<Outline>();
        outline.effectColor = Color.black;
        outline.effectDistance = new Vector2(1, 1);
        
        RectTransform titleRect = titleObj.GetComponent<RectTransform>();
        titleRect.sizeDelta = new Vector2(0, 35);
    }
    
    private Dropdown CreateMaterialDropdown(string labelText, string[] options, Transform parent, UnityEngine.Events.UnityAction<int> onValueChanged)
    {
        // Create container
        GameObject container = new GameObject(labelText + "Container");
        container.transform.SetParent(parent, false);
        
        VerticalLayoutGroup containerLayout = container.AddComponent<VerticalLayoutGroup>();
        containerLayout.spacing = 8;
        containerLayout.childControlHeight = false;
        containerLayout.childControlWidth = true;
        containerLayout.childForceExpandWidth = true;
        
        RectTransform containerRect = container.GetComponent<RectTransform>();
        containerRect.sizeDelta = new Vector2(0, 80);
        
        // Create label
        GameObject labelObj = new GameObject("Label");
        labelObj.transform.SetParent(container.transform, false);
        
        Text label = labelObj.AddComponent<Text>();
        label.text = labelText;
        label.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        label.fontSize = 16;
        label.fontStyle = FontStyle.Bold;
        label.color = new Color(1f, 0.84f, 0f); // Gold
        label.alignment = TextAnchor.MiddleLeft;
        
        // Add outline
        Outline labelOutline = labelObj.AddComponent<Outline>();
        labelOutline.effectColor = Color.black;
        labelOutline.effectDistance = new Vector2(1, 1);
        
        RectTransform labelRect = labelObj.GetComponent<RectTransform>();
        labelRect.sizeDelta = new Vector2(0, 25);
        
        // Create dropdown
        GameObject dropdownObj = new GameObject("Dropdown");
        dropdownObj.transform.SetParent(container.transform, false);
        
        Image dropdownBg = dropdownObj.AddComponent<Image>();
        dropdownBg.color = new Color(0.2f, 0.2f, 0.2f, 0.95f);
        dropdownBg.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/UISprite.psd");
        dropdownBg.type = Image.Type.Sliced;
        
        Dropdown dropdown = dropdownObj.AddComponent<Dropdown>();
        
        // Create dropdown arrow
        GameObject arrow = new GameObject("Arrow");
        arrow.transform.SetParent(dropdownObj.transform, false);
        
        Image arrowImage = arrow.AddComponent<Image>();
        arrowImage.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/DropdownArrow.psd");
        arrowImage.color = Color.white;
        
        RectTransform arrowRect = arrow.GetComponent<RectTransform>();
        arrowRect.anchorMin = new Vector2(1, 0.5f);
        arrowRect.anchorMax = new Vector2(1, 0.5f);
        arrowRect.sizeDelta = new Vector2(20, 20);
        arrowRect.anchoredPosition = new Vector2(-15, 0);
        
        // Create dropdown label
        GameObject dropdownLabel = new GameObject("Label");
        dropdownLabel.transform.SetParent(dropdownObj.transform, false);
        
        Text dropdownLabelText = dropdownLabel.AddComponent<Text>();
        dropdownLabelText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        dropdownLabelText.fontSize = 14;
        dropdownLabelText.color = Color.white;
        dropdownLabelText.alignment = TextAnchor.MiddleLeft;
        
        RectTransform dropdownLabelRect = dropdownLabel.GetComponent<RectTransform>();
        dropdownLabelRect.anchorMin = Vector2.zero;
        dropdownLabelRect.anchorMax = Vector2.one;
        dropdownLabelRect.offsetMin = new Vector2(10, 0);
        dropdownLabelRect.offsetMax = new Vector2(-25, 0);
        
        dropdown.captionText = dropdownLabelText;
        
        // Setup dropdown options
        dropdown.options.Clear();
        foreach (string option in options)
        {
            dropdown.options.Add(new Dropdown.OptionData(option));
        }
        
        dropdown.onValueChanged.AddListener(onValueChanged);
        dropdown.value = 0;
        dropdown.RefreshShownValue();
        
        RectTransform dropdownRect = dropdownObj.GetComponent<RectTransform>();
        dropdownRect.sizeDelta = new Vector2(0, 40);
        
        return dropdown;
    }
    
    private void CreateControlButtons()
    {
        // Create button container
        GameObject buttonContainer = new GameObject("ButtonContainer");
        buttonContainer.transform.SetParent(materialPanel.transform, false);
        
        HorizontalLayoutGroup buttonLayout = buttonContainer.AddComponent<HorizontalLayoutGroup>();
        buttonLayout.spacing = 10;
        buttonLayout.childControlWidth = true;
        buttonLayout.childForceExpandWidth = true;
        
        RectTransform buttonContainerRect = buttonContainer.GetComponent<RectTransform>();
        buttonContainerRect.sizeDelta = new Vector2(0, 50);
        
        // Reset button
        CreateButton("🔄 Reset", buttonContainer.transform, ResetToDefaults, new Color(0.8f, 0.2f, 0.2f));
        
        // Random button
        CreateButton("🎲 Random", buttonContainer.transform, RandomizeMaterials, new Color(0.2f, 0.6f, 0.8f));
    }
    
    private void CreateButton(string buttonText, Transform parent, UnityEngine.Events.UnityAction onClick, Color buttonColor)
    {
        GameObject buttonObj = new GameObject("Button_" + buttonText);
        buttonObj.transform.SetParent(parent, false);
        
        Image buttonBg = buttonObj.AddComponent<Image>();
        buttonBg.color = buttonColor;
        buttonBg.sprite = Resources.GetBuiltinResource<Sprite>("UI/Skin/UISprite.psd");
        buttonBg.type = Image.Type.Sliced;
        
        Button button = buttonObj.AddComponent<Button>();
        button.targetGraphic = buttonBg;
        button.onClick.AddListener(onClick);
        
        // Button text
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(buttonObj.transform, false);
        
        Text text = textObj.AddComponent<Text>();
        text.text = buttonText;
        text.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        text.fontSize = 12;
        text.fontStyle = FontStyle.Bold;
        text.color = Color.white;
        text.alignment = TextAnchor.MiddleCenter;
        
        // Add outline
        Outline textOutline = textObj.AddComponent<Outline>();
        textOutline.effectColor = Color.black;
        textOutline.effectDistance = new Vector2(1, 1);
        
        RectTransform textRect = textObj.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        RectTransform buttonRect = buttonObj.GetComponent<RectTransform>();
        buttonRect.sizeDelta = new Vector2(0, 40);
    }
    
    private void InitializeDefaultMaterials()
    {
        if (floorMaterials.Length > 0) ApplyFloorMaterial(0);
        if (wallMaterials.Length > 0) ApplyWallMaterial(0);
        if (ceilingMaterials.Length > 0) ApplyCeilingMaterial(0);
        
        UpdateStatusText("Default materials applied");
    }
    
    // Material change handlers
    private void OnFloorMaterialChanged(int index)
    {
        ApplyFloorMaterial(index);
        currentFloorIndex = index;
        UpdateStatusText($"Floor changed to: {floorMaterialNames[index]}");
        Debug.Log($"🛏️ Floor material changed to: {floorMaterialNames[index]}");
    }
    
    private void OnWallMaterialChanged(int index)
    {
        ApplyWallMaterial(index);
        currentWallIndex = index;
        UpdateStatusText($"Walls changed to: {wallMaterialNames[index]}");
        Debug.Log($"🛏️ Wall material changed to: {wallMaterialNames[index]}");
    }
    
    private void OnCeilingMaterialChanged(int index)
    {
        ApplyCeilingMaterial(index);
        currentCeilingIndex = index;
        UpdateStatusText($"Ceiling changed to: {ceilingMaterialNames[index]}");
        Debug.Log($"🛏️ Ceiling material changed to: {ceilingMaterialNames[index]}");
    }
    
    // Material application methods
    private void ApplyFloorMaterial(int index)
    {
        if (index >= 0 && index < floorMaterials.Length && floorRenderer != null)
        {
            floorRenderer.material = floorMaterials[index];
        }
    }
    
    private void ApplyWallMaterial(int index)
    {
        if (index >= 0 && index < wallMaterials.Length && wallRenderers != null)
        {
            foreach (Renderer wallRenderer in wallRenderers)
            {
                if (wallRenderer != null)
                {
                    wallRenderer.material = wallMaterials[index];
                }
            }
        }
    }
    
    private void ApplyCeilingMaterial(int index)
    {
        if (index >= 0 && index < ceilingMaterials.Length && ceilingRenderer != null)
        {
            ceilingRenderer.material = ceilingMaterials[index];
        }
    }
    
    // UI Control methods
    public void ToggleUI()
    {
        isUIVisible = !isUIVisible;
        if (materialPanel != null)
        {
            materialPanel.SetActive(isUIVisible);
        }
        UpdateStatusText(isUIVisible ? "UI shown" : "UI hidden");
        Debug.Log($"🎮 Material UI toggled: {(isUIVisible ? "Visible" : "Hidden")}");
    }
    
    public void ResetToDefaults()
    {
        if (floorDropdown != null) floorDropdown.value = 0;
        if (wallDropdown != null) wallDropdown.value = 0;
        if (ceilingDropdown != null) ceilingDropdown.value = 0;
        
        ApplyFloorMaterial(0);
        ApplyWallMaterial(0);
        ApplyCeilingMaterial(0);
        
        currentFloorIndex = 0;
        currentWallIndex = 0;
        currentCeilingIndex = 0;
        
        UpdateStatusText("Materials reset to defaults");
        Debug.Log("🔄 Materials reset to defaults");
    }
    
    public void RandomizeMaterials()
    {
        int randomFloor = Random.Range(0, floorMaterials.Length);
        int randomWall = Random.Range(0, wallMaterials.Length);
        int randomCeiling = Random.Range(0, ceilingMaterials.Length);
        
        if (floorDropdown != null) floorDropdown.value = randomFloor;
        if (wallDropdown != null) wallDropdown.value = randomWall;
        if (ceilingDropdown != null) ceilingDropdown.value = randomCeiling;
        
        ApplyFloorMaterial(randomFloor);
        ApplyWallMaterial(randomWall);
        ApplyCeilingMaterial(randomCeiling);
        
        UpdateStatusText("Random materials applied!");
        Debug.Log("🎲 Random materials applied");
    }
    
    private void UpdateStatusText(string message)
    {
        if (statusText != null)
        {
            statusText.text = message;
        }
    }
    
    // Keyboard shortcuts
    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.T))
        {
            ToggleUI();
        }
        
        if (Input.GetKeyDown(KeyCode.R))
        {
            ResetToDefaults();
        }
        
        if (Input.GetKeyDown(KeyCode.Space))
        {
            RandomizeMaterials();
        }
    }
}
