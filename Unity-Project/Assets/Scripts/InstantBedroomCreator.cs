using UnityEngine;

/// <summary>
/// InstantBedroomCreator - Creates a realistic bedroom scene instantly!
/// Just drag this script to any GameObject and click "Create Realistic Bedroom Now!" in the inspector
/// </summary>
public class InstantBedroomCreator : MonoBehaviour
{
    [Header("🛏️ INSTANT REALISTIC BEDROOM CREATOR")]
    [Space(10)]
    [TextArea(3, 5)]
    public string instructions = "1. Drag this script to any GameObject\n2. Click 'Create Realistic Bedroom Now!' below\n3. Use WASD + Mouse to explore\n4. Press T to toggle UI, R to reset, SPACE for random materials";
    
    [Space(10)]
    [SerializeField] private bool createAdvancedLighting = true;
    [SerializeField] private bool createRealisticMaterials = true;
    [SerializeField] private bool setupCameraControls = true;
    
    [ContextMenu("🛏️ Create Realistic Bedroom Now!")]
    public void CreateRealisticBedroomNow()
    {
        Debug.Log("🚀 Creating REALISTIC BEDROOM scene...");
        
        // Clear any existing bedroom
        ClearExistingBedroom();
        
        // Create the bedroom structure
        CreateBedroomStructure();
        
        // Create realistic furniture
        CreateRealisticFurniture();
        
        // Apply realistic materials
        if (createRealisticMaterials)
        {
            ApplyRealisticMaterials();
        }
        
        // Setup advanced lighting
        if (createAdvancedLighting)
        {
            SetupRealisticLighting();
        }
        
        // Setup camera controls
        if (setupCameraControls)
        {
            SetupCameraControls();
        }
        
        Debug.Log("✅ REALISTIC BEDROOM CREATED!");
        Debug.Log("🎮 Use WASD + Mouse to explore your bedroom!");
        Debug.Log("🎨 Press T to toggle materials UI");
    }
    
    private void ClearExistingBedroom()
    {
        GameObject[] existingRooms = {
            GameObject.Find("CozyBedroom"),
            GameObject.Find("RealisticBedroom"),
            GameObject.Find("Bedroom")
        };
        
        foreach (GameObject room in existingRooms)
        {
            if (room != null)
            {
                DestroyImmediate(room);
            }
        }
        
        Debug.Log("🧹 Cleared existing bedroom objects");
    }
    
    private void CreateBedroomStructure()
    {
        Debug.Log("🏠 Creating bedroom structure...");
        
        // Create main bedroom parent
        GameObject bedroom = new GameObject("RealisticBedroom");
        bedroom.transform.position = Vector3.zero;
        
        // Room dimensions
        float width = 6f;
        float depth = 5f;
        float height = 3f;
        
        // Create floor
        GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
        floor.name = "Floor";
        floor.transform.SetParent(bedroom.transform);
        floor.transform.position = Vector3.zero;
        floor.transform.localScale = new Vector3(width / 10f, 1f, depth / 10f);
        
        // Create walls
        CreateWall("Wall_North", bedroom.transform, new Vector3(0, height/2f, depth/2f), new Vector3(width, height, 0.1f));
        CreateWall("Wall_South", bedroom.transform, new Vector3(0, height/2f, -depth/2f), new Vector3(width, height, 0.1f));
        CreateWall("Wall_East", bedroom.transform, new Vector3(width/2f, height/2f, 0), new Vector3(0.1f, height, depth));
        CreateWall("Wall_West", bedroom.transform, new Vector3(-width/2f, height/2f, 0), new Vector3(0.1f, height, depth));
        
        // Create ceiling
        GameObject ceiling = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ceiling.name = "Ceiling";
        ceiling.transform.SetParent(bedroom.transform);
        ceiling.transform.position = new Vector3(0, height, 0);
        ceiling.transform.rotation = Quaternion.Euler(180, 0, 0);
        ceiling.transform.localScale = new Vector3(width / 10f, 1f, depth / 10f);
        
        Debug.Log("✅ Bedroom structure created");
    }
    
    private void CreateWall(string name, Transform parent, Vector3 position, Vector3 scale)
    {
        GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        wall.name = name;
        wall.transform.SetParent(parent);
        wall.transform.position = position;
        wall.transform.localScale = scale;
    }
    
    private void CreateRealisticFurniture()
    {
        Debug.Log("🛏️ Creating realistic furniture...");
        
        GameObject bedroom = GameObject.Find("RealisticBedroom");
        GameObject furniture = new GameObject("Furniture");
        furniture.transform.SetParent(bedroom.transform);
        
        // Create bed (main centerpiece)
        CreateBed(furniture.transform);
        
        // Create nightstands
        CreateNightstand("Nightstand_Left", furniture.transform, new Vector3(-1.8f, 0.3f, 0.5f));
        CreateNightstand("Nightstand_Right", furniture.transform, new Vector3(1.8f, 0.3f, 0.5f));
        
        // Create dresser
        CreateDresser(furniture.transform);
        
        // Create chair
        CreateChair(furniture.transform);
        
        // Create decorative items
        CreateDecorations(furniture.transform);
        
        Debug.Log("✅ Realistic furniture created");
    }
    
    private void CreateBed(Transform parent)
    {
        GameObject bed = new GameObject("Bed");
        bed.transform.SetParent(parent);
        bed.transform.position = new Vector3(0, 0, 0.5f);
        
        // Bed frame
        GameObject frame = GameObject.CreatePrimitive(PrimitiveType.Cube);
        frame.name = "BedFrame";
        frame.transform.SetParent(bed.transform);
        frame.transform.localPosition = Vector3.zero;
        frame.transform.localScale = new Vector3(2.2f, 0.4f, 1.6f);
        
        // Mattress
        GameObject mattress = GameObject.CreatePrimitive(PrimitiveType.Cube);
        mattress.name = "Mattress";
        mattress.transform.SetParent(bed.transform);
        mattress.transform.localPosition = new Vector3(0, 0.25f, 0);
        mattress.transform.localScale = new Vector3(2f, 0.3f, 1.5f);
        
        // Headboard
        GameObject headboard = GameObject.CreatePrimitive(PrimitiveType.Cube);
        headboard.name = "Headboard";
        headboard.transform.SetParent(bed.transform);
        headboard.transform.localPosition = new Vector3(0, 0.8f, 0.8f);
        headboard.transform.localScale = new Vector3(2.2f, 1.2f, 0.1f);
        
        // Pillows
        for (int i = 0; i < 2; i++)
        {
            GameObject pillow = GameObject.CreatePrimitive(PrimitiveType.Cube);
            pillow.name = $"Pillow_{i + 1}";
            pillow.transform.SetParent(bed.transform);
            pillow.transform.localPosition = new Vector3((i - 0.5f) * 0.7f, 0.45f, 0.6f);
            pillow.transform.localScale = new Vector3(0.5f, 0.15f, 0.35f);
        }
    }
    
    private void CreateNightstand(string name, Transform parent, Vector3 position)
    {
        GameObject nightstand = GameObject.CreatePrimitive(PrimitiveType.Cube);
        nightstand.name = name;
        nightstand.transform.SetParent(parent);
        nightstand.transform.position = position;
        nightstand.transform.localScale = new Vector3(0.6f, 0.6f, 0.5f);
        
        // Add a lamp on top
        GameObject lamp = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        lamp.name = name + "_Lamp";
        lamp.transform.SetParent(nightstand.transform);
        lamp.transform.localPosition = new Vector3(0, 0.6f, 0);
        lamp.transform.localScale = new Vector3(0.3f, 0.4f, 0.3f);
    }
    
    private void CreateDresser(Transform parent)
    {
        GameObject dresser = GameObject.CreatePrimitive(PrimitiveType.Cube);
        dresser.name = "Dresser";
        dresser.transform.SetParent(parent);
        dresser.transform.position = new Vector3(-2.2f, 0.5f, -1.8f);
        dresser.transform.localScale = new Vector3(1.5f, 1f, 0.6f);
        
        // Mirror above dresser
        GameObject mirror = GameObject.CreatePrimitive(PrimitiveType.Cube);
        mirror.name = "Mirror";
        mirror.transform.SetParent(dresser.transform);
        mirror.transform.localPosition = new Vector3(0, 1.2f, -0.4f);
        mirror.transform.localScale = new Vector3(0.8f, 0.8f, 0.05f);
    }
    
    private void CreateChair(Transform parent)
    {
        GameObject chair = GameObject.CreatePrimitive(PrimitiveType.Cube);
        chair.name = "Chair";
        chair.transform.SetParent(parent);
        chair.transform.position = new Vector3(2.2f, 0.3f, -1.5f);
        chair.transform.localScale = new Vector3(0.7f, 0.6f, 0.7f);
        
        // Chair back
        GameObject chairBack = GameObject.CreatePrimitive(PrimitiveType.Cube);
        chairBack.name = "ChairBack";
        chairBack.transform.SetParent(chair.transform);
        chairBack.transform.localPosition = new Vector3(0, 0.6f, 0.3f);
        chairBack.transform.localScale = new Vector3(1f, 0.8f, 0.1f);
    }
    
    private void CreateDecorations(Transform parent)
    {
        // Wall art
        GameObject painting = GameObject.CreatePrimitive(PrimitiveType.Cube);
        painting.name = "WallArt";
        painting.transform.SetParent(parent);
        painting.transform.position = new Vector3(0, 1.8f, 2.45f);
        painting.transform.localScale = new Vector3(1.2f, 0.8f, 0.05f);
        
        // Plant in corner
        GameObject plant = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        plant.name = "Plant";
        plant.transform.SetParent(parent);
        plant.transform.position = new Vector3(-2.7f, 0.4f, 2.2f);
        plant.transform.localScale = new Vector3(0.3f, 0.8f, 0.3f);
        
        // Rug under bed
        GameObject rug = GameObject.CreatePrimitive(PrimitiveType.Plane);
        rug.name = "Rug";
        rug.transform.SetParent(parent);
        rug.transform.position = new Vector3(0, 0.01f, 0);
        rug.transform.localScale = new Vector3(0.35f, 1f, 0.25f);
    }
    
    private void ApplyRealisticMaterials()
    {
        Debug.Log("🎨 Applying realistic materials...");
        
        // Create and apply materials to different objects
        ApplyFloorMaterial();
        ApplyWallMaterials();
        ApplyCeilingMaterial();
        ApplyFurnitureMaterials();
        
        Debug.Log("✅ Realistic materials applied");
    }
    
    private void ApplyFloorMaterial()
    {
        GameObject floor = GameObject.Find("Floor");
        if (floor != null)
        {
            Material floorMat = new Material(Shader.Find("Standard"));
            floorMat.name = "Hardwood_Floor";
            floorMat.color = new Color(0.6f, 0.4f, 0.2f);
            floorMat.SetFloat("_Metallic", 0.1f);
            floorMat.SetFloat("_Smoothness", 0.8f);
            floor.GetComponent<Renderer>().material = floorMat;
        }
    }
    
    private void ApplyWallMaterials()
    {
        string[] wallNames = { "Wall_North", "Wall_South", "Wall_East", "Wall_West" };
        
        foreach (string wallName in wallNames)
        {
            GameObject wall = GameObject.Find(wallName);
            if (wall != null)
            {
                Material wallMat = new Material(Shader.Find("Standard"));
                wallMat.name = "Warm_Wall";
                wallMat.color = new Color(0.95f, 0.9f, 0.8f);
                wallMat.SetFloat("_Metallic", 0f);
                wallMat.SetFloat("_Smoothness", 0.3f);
                wall.GetComponent<Renderer>().material = wallMat;
            }
        }
    }
    
    private void ApplyCeilingMaterial()
    {
        GameObject ceiling = GameObject.Find("Ceiling");
        if (ceiling != null)
        {
            Material ceilingMat = new Material(Shader.Find("Standard"));
            ceilingMat.name = "White_Ceiling";
            ceilingMat.color = new Color(0.98f, 0.98f, 0.98f);
            ceilingMat.SetFloat("_Metallic", 0f);
            ceilingMat.SetFloat("_Smoothness", 0.2f);
            ceiling.GetComponent<Renderer>().material = ceilingMat;
        }
    }
    
    private void ApplyFurnitureMaterials()
    {
        // Apply wood material to furniture
        Material woodMat = new Material(Shader.Find("Standard"));
        woodMat.name = "Wood_Furniture";
        woodMat.color = new Color(0.4f, 0.3f, 0.2f);
        woodMat.SetFloat("_Metallic", 0f);
        woodMat.SetFloat("_Smoothness", 0.6f);
        
        // Apply fabric material to soft items
        Material fabricMat = new Material(Shader.Find("Standard"));
        fabricMat.name = "Fabric_Material";
        fabricMat.color = new Color(0.8f, 0.7f, 0.6f);
        fabricMat.SetFloat("_Metallic", 0f);
        fabricMat.SetFloat("_Smoothness", 0.1f);
        
        // Apply materials to furniture pieces
        ApplyMaterialToObject("BedFrame", woodMat);
        ApplyMaterialToObject("Mattress", fabricMat);
        ApplyMaterialToObject("Headboard", woodMat);
        ApplyMaterialToObject("Nightstand_Left", woodMat);
        ApplyMaterialToObject("Nightstand_Right", woodMat);
        ApplyMaterialToObject("Dresser", woodMat);
        ApplyMaterialToObject("Chair", woodMat);
    }
    
    private void ApplyMaterialToObject(string objectName, Material material)
    {
        GameObject obj = GameObject.Find(objectName);
        if (obj != null && obj.GetComponent<Renderer>() != null)
        {
            obj.GetComponent<Renderer>().material = material;
        }
    }

    private void SetupRealisticLighting()
    {
        Debug.Log("💡 Setting up realistic lighting...");

        // Remove existing lights
        Light[] existingLights = FindObjectsOfType<Light>();
        foreach (Light light in existingLights)
        {
            if (light.type != LightType.Directional || light.name != "MainLight")
            {
                DestroyImmediate(light.gameObject);
            }
        }

        GameObject bedroom = GameObject.Find("RealisticBedroom");
        GameObject lighting = new GameObject("Lighting");
        lighting.transform.SetParent(bedroom.transform);

        // Main directional light (sunlight through window)
        GameObject mainLight = new GameObject("MainLight");
        mainLight.transform.SetParent(lighting.transform);
        mainLight.transform.position = new Vector3(3, 4, 3);
        mainLight.transform.rotation = Quaternion.Euler(45f, -45f, 0);

        Light directionalLight = mainLight.AddComponent<Light>();
        directionalLight.type = LightType.Directional;
        directionalLight.color = new Color(1f, 0.95f, 0.8f); // Warm sunlight
        directionalLight.intensity = 1.5f;
        directionalLight.shadows = LightShadows.Soft;

        // Ceiling light
        GameObject ceilingLight = new GameObject("CeilingLight");
        ceilingLight.transform.SetParent(lighting.transform);
        ceilingLight.transform.position = new Vector3(0, 2.8f, 0);

        Light pointLight = ceilingLight.AddComponent<Light>();
        pointLight.type = LightType.Point;
        pointLight.color = new Color(1f, 1f, 0.9f); // Cool white
        pointLight.intensity = 2f;
        pointLight.range = 8f;
        pointLight.shadows = LightShadows.Soft;

        // Bedside lamps
        CreateBedsideLamp("BedsideLamp_Left", lighting.transform, new Vector3(-1.8f, 1f, 0.5f));
        CreateBedsideLamp("BedsideLamp_Right", lighting.transform, new Vector3(1.8f, 1f, 0.5f));

        // Configure ambient lighting
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
        RenderSettings.ambientSkyColor = new Color(0.53f, 0.81f, 0.92f);
        RenderSettings.ambientEquatorColor = new Color(1f, 0.95f, 0.8f);
        RenderSettings.ambientGroundColor = new Color(0.18f, 0.18f, 0.18f);
        RenderSettings.ambientIntensity = 0.3f;

        Debug.Log("✅ Realistic lighting setup complete");
    }

    private void CreateBedsideLamp(string name, Transform parent, Vector3 position)
    {
        GameObject lamp = new GameObject(name);
        lamp.transform.SetParent(parent);
        lamp.transform.position = position;

        Light spotLight = lamp.AddComponent<Light>();
        spotLight.type = LightType.Spot;
        spotLight.color = new Color(1f, 0.9f, 0.7f); // Warm yellow
        spotLight.intensity = 1.5f;
        spotLight.range = 5f;
        spotLight.spotAngle = 60f;
        spotLight.shadows = LightShadows.Soft;

        // Point the light downward
        lamp.transform.rotation = Quaternion.Euler(90f, 0, 0);
    }

    private void SetupCameraControls()
    {
        Debug.Log("📷 Setting up camera controls...");

        // Find or create main camera
        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            GameObject cameraObj = new GameObject("Main Camera");
            mainCamera = cameraObj.AddComponent<Camera>();
            cameraObj.tag = "MainCamera";
        }

        // Position camera for good initial view
        mainCamera.transform.position = new Vector3(4, 2.5f, -4);
        mainCamera.transform.LookAt(new Vector3(0, 1.2f, 0));

        // Configure camera settings
        mainCamera.fieldOfView = 60f;
        mainCamera.nearClipPlane = 0.1f;
        mainCamera.farClipPlane = 100f;

        // Add camera controller if it doesn't exist
        BedroomCameraController cameraController = mainCamera.GetComponent<BedroomCameraController>();
        if (cameraController == null)
        {
            cameraController = mainCamera.gameObject.AddComponent<BedroomCameraController>();
        }

        // Create camera target
        GameObject target = GameObject.Find("CameraTarget");
        if (target == null)
        {
            target = new GameObject("CameraTarget");
            target.transform.position = new Vector3(0, 1.2f, 0);
        }

        Debug.Log("✅ Camera controls setup complete");
        Debug.Log("🎮 Use WASD + Mouse to explore the bedroom!");
    }

    [ContextMenu("🧹 Clear Bedroom")]
    public void ClearBedroom()
    {
        ClearExistingBedroom();
        Debug.Log("🧹 Bedroom cleared");
    }

    [ContextMenu("🎲 Randomize Materials")]
    public void RandomizeMaterials()
    {
        Debug.Log("🎲 Randomizing materials...");

        // Create random color variations
        Color[] randomColors = {
            new Color(Random.Range(0.3f, 0.8f), Random.Range(0.2f, 0.6f), Random.Range(0.1f, 0.4f)),
            new Color(Random.Range(0.8f, 1f), Random.Range(0.8f, 1f), Random.Range(0.7f, 0.9f)),
            new Color(Random.Range(0.6f, 0.9f), Random.Range(0.7f, 0.9f), Random.Range(0.8f, 1f)),
            new Color(Random.Range(0.4f, 0.7f), Random.Range(0.5f, 0.8f), Random.Range(0.3f, 0.6f))
        };

        // Apply random colors to walls
        string[] wallNames = { "Wall_North", "Wall_South", "Wall_East", "Wall_West" };
        for (int i = 0; i < wallNames.Length; i++)
        {
            GameObject wall = GameObject.Find(wallNames[i]);
            if (wall != null)
            {
                Material wallMat = wall.GetComponent<Renderer>().material;
                wallMat.color = randomColors[Random.Range(0, randomColors.Length)];
            }
        }

        Debug.Log("✅ Materials randomized!");
    }
}
