Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"

/Users/<USER>/Desktop/Pionare/Unity-Project    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"

-target:library
-out:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll"
-refout:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll"
-define:UNITY_6000_1_6
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_OSX
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_OSX
-define:UNITY_STANDALONE
-define:ENABLE_GAMECENTER
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:ENABLE_SPATIALTRACKING
-define:PLATFORM_HAS_CUSTOM_MUTEX
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_OSX
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/AnswerData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/IOnboardingSection.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/IOnboardingSectionAnalyticsProvider.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/Preset.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/SelectedSolutionsData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/StyleConstants.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"

/pathmap:"/Users/<USER>/Desktop/Pionare/Unity-Project"=.    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"

Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_3DB644191B1B2EED.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_C5AE142EFA110487.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B2AE0B1790CDF67C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_0616146022ED9BC8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_0E0F21B100678371.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_86010DDA4934CAC2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E9BD22091C4C0F55.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_BD7F14C36A74D17C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7DC73530330A44CA.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C9F0A84BBDC69A30.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E8AA20EA0A63A7CC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_F3A9ABDC1CD3E43E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_B6C44A0EA7B17958.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7B262E92DA7273A4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7346C78C14A9B361.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_87801BB1B66F01B9.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C72E3DF734A0A54C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_C6A2362C94D57308.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_D88C42BCAA83CDB7.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_ECC4DD4F879CE3E5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2EAD006E17020B66.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_72824D3B12484B29.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_08D6B63D8BE14123.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_2D3248B6F4439116.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0C4991A809505204.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_B1F6AA9409CC806F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4B2961EB2BFFCE92.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_15472E073F752D2D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_2E73808CC0CC7C67.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_605B46FA91B01D9A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_1D265465A4618B6E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_070B58BE5D5B2CD3.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_5999718AF5CA3D33.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_A181A7FC854DF651.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_55ECB3E35BAD035D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_A607C03F75180DDC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_47691717A411A494.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_BEB010A19CDB4C31.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_C7CF74A887622C95.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_9D5F48569240A071.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_74871FCCC1AC330C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_3319E59F4DD98A07.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_09492F27254B688B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_A237BB9029659CE6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_DD4EBDF18344FA39.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_9F8C88B8933E69C0.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3EEEFD3F09C679A1.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_5CF69290777FC509.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_1BA83129DB600F47.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_41CC43B4EF068807.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_16695793D49D23D2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_4CBFE92EEE47F615.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_7E71F5EAF2300F39.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_E55A52E5CC4CEE1D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_88E5A43CD6D33B51.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_C32BD734DA2FDF00.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_E5D6426FBCCA9E02.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_0CDDF55BC4517A80.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A26A42CD31C19E18.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_28490B37780E689D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FC94EF241C3A2715.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_2D9A6CA0EA6F39BE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_45F57B44A1A10038.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F532DB9E132EAD23.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_8419FB82A771BD09.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_EA4F1149DCA61077.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B6099A197873D297.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C31501BACA39A389.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_2F1DD4E55C5496F4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_B6D19551B861A31A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_48A7D55E01C19CF4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_DE05044D8639AC06.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_E4E7BD9B686833DD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9F06867B0901DA2B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_C7326E8F6E4F2E4A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_F52887C3D2702367.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_CC6805BBA70F838D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_F57C8C199FD329A6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_9F16FB8791438F3F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_8E7233A590037F9C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_DF3F95F9ECABCB2A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7330BA83148C6474.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_F6C06255AAD22865.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B8EE34001D6E068D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_608810156C97DC07.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_B32F13BAD3829244.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_2F21EDD4C31095B2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_66211689A3EA2E1F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F66131053E8E3F45.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D30C454E89E13D53.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_99CF543691587DC5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_F9861168C26A1418.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_F5F734ECB9162433.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D7F8ECDE08C7FBFE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_1FE1234F984BB51C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E22DA60AD4A58682.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_988278E9DA26F521.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_2D7AC574C2A8417E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_9E0DB59E9484B9BC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_586A30BDD3C6CAB5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_F946A2037F56BAAC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_3A55B8CBAB2C2D9D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_151BFCE34BFA7BA2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_F6A8B3361FD36936.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_74E8E4E65F1A79F4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_3049D5BF1BA78862.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_62BACDD1DF5987F3.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_17134F6617AE59D7.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_40BCDC8F67493173.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9FCDF264C0CBA08C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_EAA368AB880B7188.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A00C04B6F2383838.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_AE595DE39915AAEB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_A37B7A55B4C22ADE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_734F0E585E36251E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_AB3C57437581502D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_832CD0845587F8E9.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_DDFB1A47FEBB9D8B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_5812C766893C0699.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_CBB50F54AA1DCCC6.mvfrm    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"

/Users/<USER>/Desktop/Pionare/Unity-Project    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"

-target:library
-out:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll"
-refout:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll"
-define:UNITY_6000_1_6
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_OSX
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_OSX
-define:UNITY_STANDALONE
-define:ENABLE_GAMECENTER
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:ENABLE_SPATIALTRACKING
-define:PLATFORM_HAS_CUSTOM_MUTEX
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_OSX
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-define:UNITY_EDITOR_ONLY_COMPILATION
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AMDModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/AnalyticsData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/AnalyticsUtils.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/DebugAnalytics.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/MultiplayerCenterAnalytics.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/MultiplayerCenterAnalyticsFactory.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/OnboardingSectionAnalyticsProvider.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/AssemblyInfo.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Features/PackageManagement.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/MultiplayerCenterWindow.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/RecommendationTabView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/RecommendationViewBottomBar.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/TabGroup.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/QuestionnaireView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/QuestionSection.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/QuestionViewFactory.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/PackageSelectionView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/RecommendationItemView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/RecommendationView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/SectionHeader.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/SolutionSelectionView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/StyleClasses.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/ViewUtils.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/OnBoarding/GettingStartedTabView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/OnBoarding/QuickstartPackageHandling.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/OnBoarding/SectionsFinder.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/Logic.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/PresetData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/QuestionnaireData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/QuestionnaireEditor.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/QuestionnaireObject.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/UserChoicesObject.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/PreReleaseHandling.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationAuthoringData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationType.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationUtils.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationViewData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommenderSystem.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommenderSystemData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommenderSystemDataObject.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/Scoring.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"

/pathmap:"/Users/<USER>/Desktop/Pionare/Unity-Project"=.    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"

Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_3DB644191B1B2EED.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_C5AE142EFA110487.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B2AE0B1790CDF67C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_0616146022ED9BC8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_0E0F21B100678371.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_86010DDA4934CAC2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E9BD22091C4C0F55.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_BD7F14C36A74D17C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7DC73530330A44CA.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C9F0A84BBDC69A30.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E8AA20EA0A63A7CC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_F3A9ABDC1CD3E43E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_B6C44A0EA7B17958.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7B262E92DA7273A4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7346C78C14A9B361.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_87801BB1B66F01B9.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C72E3DF734A0A54C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_C6A2362C94D57308.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_D88C42BCAA83CDB7.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_ECC4DD4F879CE3E5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2EAD006E17020B66.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_72824D3B12484B29.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_08D6B63D8BE14123.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_2D3248B6F4439116.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0C4991A809505204.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_B1F6AA9409CC806F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4B2961EB2BFFCE92.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_15472E073F752D2D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_2E73808CC0CC7C67.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_605B46FA91B01D9A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_1D265465A4618B6E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_070B58BE5D5B2CD3.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_5999718AF5CA3D33.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_A181A7FC854DF651.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_55ECB3E35BAD035D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_A607C03F75180DDC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_47691717A411A494.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_BEB010A19CDB4C31.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_C7CF74A887622C95.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_9D5F48569240A071.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_74871FCCC1AC330C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_3319E59F4DD98A07.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_09492F27254B688B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_A237BB9029659CE6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_DD4EBDF18344FA39.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_9F8C88B8933E69C0.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3EEEFD3F09C679A1.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_5CF69290777FC509.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_2EEB020F9B77C62B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_1BA83129DB600F47.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_41CC43B4EF068807.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_16695793D49D23D2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_4CBFE92EEE47F615.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_7E71F5EAF2300F39.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_E55A52E5CC4CEE1D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_88E5A43CD6D33B51.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_C32BD734DA2FDF00.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_E5D6426FBCCA9E02.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_0CDDF55BC4517A80.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A26A42CD31C19E18.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_28490B37780E689D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FC94EF241C3A2715.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_2D9A6CA0EA6F39BE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_45F57B44A1A10038.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F532DB9E132EAD23.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_8419FB82A771BD09.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_EA4F1149DCA61077.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B6099A197873D297.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C31501BACA39A389.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_2F1DD4E55C5496F4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_B6D19551B861A31A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_48A7D55E01C19CF4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_DE05044D8639AC06.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_E4E7BD9B686833DD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9F06867B0901DA2B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_C7326E8F6E4F2E4A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_F52887C3D2702367.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_CC6805BBA70F838D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_4BBAC3C37A70FEEC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_F57C8C199FD329A6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_9F16FB8791438F3F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_8E7233A590037F9C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_DF3F95F9ECABCB2A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7330BA83148C6474.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_F6C06255AAD22865.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B8EE34001D6E068D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_608810156C97DC07.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_B32F13BAD3829244.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_2F21EDD4C31095B2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_66211689A3EA2E1F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F66131053E8E3F45.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D30C454E89E13D53.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_99CF543691587DC5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_F9861168C26A1418.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_F5F734ECB9162433.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D7F8ECDE08C7FBFE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_1FE1234F984BB51C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E22DA60AD4A58682.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_988278E9DA26F521.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_2D7AC574C2A8417E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_9E0DB59E9484B9BC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_586A30BDD3C6CAB5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_F946A2037F56BAAC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_3A55B8CBAB2C2D9D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_151BFCE34BFA7BA2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_F6A8B3361FD36936.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_74E8E4E65F1A79F4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_3049D5BF1BA78862.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_62BACDD1DF5987F3.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_17134F6617AE59D7.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_40BCDC8F67493173.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9FCDF264C0CBA08C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_EAA368AB880B7188.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A00C04B6F2383838.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_AE595DE39915AAEB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_A37B7A55B4C22ADE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_734F0E585E36251E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_AB3C57437581502D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_832CD0845587F8E9.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_DDFB1A47FEBB9D8B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_5812C766893C0699.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_CBB50F54AA1DCCC6.mvfrm    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"

/Users/<USER>/Desktop/Pionare/Unity-Project    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"

-target:library
-out:"Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.ref.dll"
-define:UNITY_6000_1_6
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_OSX
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_OSX
-define:UNITY_STANDALONE
-define:ENABLE_GAMECENTER
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:ENABLE_SPATIALTRACKING
-define:PLATFORM_HAS_CUSTOM_MUTEX
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_OSX
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Assets/Scripts/BedroomCameraController.cs"
"Assets/Scripts/BedroomMaterialCreator.cs"
"Assets/Scripts/BedroomMaterialManager.cs"
"Assets/Scripts/BedroomSceneSetup.cs"
"Assets/Scripts/InstantBedroomCreator.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp2"

    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"

Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm
Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_6DAF32D8644B89D6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B2AE0B1790CDF67C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_0616146022ED9BC8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_0E0F21B100678371.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_86010DDA4934CAC2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E9BD22091C4C0F55.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_BD7F14C36A74D17C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7DC73530330A44CA.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C9F0A84BBDC69A30.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E8AA20EA0A63A7CC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_F3A9ABDC1CD3E43E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_B6C44A0EA7B17958.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7B262E92DA7273A4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7346C78C14A9B361.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_87801BB1B66F01B9.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C72E3DF734A0A54C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_C6A2362C94D57308.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_D88C42BCAA83CDB7.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_ECC4DD4F879CE3E5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2EAD006E17020B66.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_72824D3B12484B29.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_08D6B63D8BE14123.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_2D3248B6F4439116.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0C4991A809505204.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_B1F6AA9409CC806F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4B2961EB2BFFCE92.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_15472E073F752D2D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_2E73808CC0CC7C67.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_605B46FA91B01D9A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_1D265465A4618B6E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_070B58BE5D5B2CD3.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_5999718AF5CA3D33.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_A181A7FC854DF651.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_55ECB3E35BAD035D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_A607C03F75180DDC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_47691717A411A494.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_BEB010A19CDB4C31.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_C7CF74A887622C95.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_9D5F48569240A071.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_74871FCCC1AC330C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_3319E59F4DD98A07.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_09492F27254B688B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_A237BB9029659CE6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_DD4EBDF18344FA39.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_9F8C88B8933E69C0.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3EEEFD3F09C679A1.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_5CF69290777FC509.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_1BA83129DB600F47.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_41CC43B4EF068807.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_16695793D49D23D2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_4CBFE92EEE47F615.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_7E71F5EAF2300F39.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_E55A52E5CC4CEE1D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_88E5A43CD6D33B51.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_C32BD734DA2FDF00.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_E5D6426FBCCA9E02.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_0CDDF55BC4517A80.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A26A42CD31C19E18.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_28490B37780E689D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FC94EF241C3A2715.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_2D9A6CA0EA6F39BE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_45F57B44A1A10038.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F532DB9E132EAD23.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_8419FB82A771BD09.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_EA4F1149DCA61077.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B6099A197873D297.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C31501BACA39A389.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_2F1DD4E55C5496F4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_B6D19551B861A31A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_48A7D55E01C19CF4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_DE05044D8639AC06.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_E4E7BD9B686833DD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9F06867B0901DA2B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_C7326E8F6E4F2E4A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_F52887C3D2702367.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_CC6805BBA70F838D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_F57C8C199FD329A6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_9F16FB8791438F3F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_8E7233A590037F9C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_DF3F95F9ECABCB2A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7330BA83148C6474.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_F6C06255AAD22865.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B8EE34001D6E068D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_608810156C97DC07.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_B32F13BAD3829244.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_2F21EDD4C31095B2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_66211689A3EA2E1F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F66131053E8E3F45.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D30C454E89E13D53.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_99CF543691587DC5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_F9861168C26A1418.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_F5F734ECB9162433.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D7F8ECDE08C7FBFE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_1FE1234F984BB51C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E22DA60AD4A58682.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_988278E9DA26F521.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_2D7AC574C2A8417E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_9E0DB59E9484B9BC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_586A30BDD3C6CAB5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_F946A2037F56BAAC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_3A55B8CBAB2C2D9D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_151BFCE34BFA7BA2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_F6A8B3361FD36936.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_74E8E4E65F1A79F4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_3049D5BF1BA78862.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_62BACDD1DF5987F3.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_17134F6617AE59D7.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_40BCDC8F67493173.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9FCDF264C0CBA08C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_EAA368AB880B7188.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A00C04B6F2383838.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_AE595DE39915AAEB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_A37B7A55B4C22ADE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_734F0E585E36251E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_AB3C57437581502D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_832CD0845587F8E9.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_DDFB1A47FEBB9D8B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_5812C766893C0699.mvfrm    

