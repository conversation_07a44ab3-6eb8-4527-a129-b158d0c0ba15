{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 84491, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 84491, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 84491, "tid": 21, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 84491, "tid": 21, "ts": 1749204350744618, "dur": 1018, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 84491, "tid": 21, "ts": 1749204350749349, "dur": 494, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 84491, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 84491, "tid": 1, "ts": 1749204350548688, "dur": 4250, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 84491, "tid": 1, "ts": 1749204350552940, "dur": 8199, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 84491, "tid": 1, "ts": 1749204350561143, "dur": 6777, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 84491, "tid": 21, "ts": 1749204350749847, "dur": 33, "ph": "X", "name": "", "args": {}}, {"pid": 84491, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350547153, "dur": 3803, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350550958, "dur": 183972, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350552372, "dur": 4401, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350556777, "dur": 1151, "ph": "X", "name": "ProcessMessages 8105", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350557932, "dur": 62, "ph": "X", "name": "ReadAsync 8105", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350557996, "dur": 4, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350558001, "dur": 81, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350558086, "dur": 1, "ph": "X", "name": "ProcessMessages 1354", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350558089, "dur": 1313, "ph": "X", "name": "ReadAsync 1354", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350559404, "dur": 16, "ph": "X", "name": "ProcessMessages 8108", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350559422, "dur": 47, "ph": "X", "name": "ReadAsync 8108", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350559470, "dur": 1, "ph": "X", "name": "ProcessMessages 1410", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350559472, "dur": 35, "ph": "X", "name": "ReadAsync 1410", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350559509, "dur": 64, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350559582, "dur": 77, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350559714, "dur": 16, "ph": "X", "name": "ProcessMessages 1638", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350559735, "dur": 114, "ph": "X", "name": "ReadAsync 1638", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350559851, "dur": 3, "ph": "X", "name": "ProcessMessages 4060", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350559855, "dur": 57, "ph": "X", "name": "ReadAsync 4060", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350559914, "dur": 1, "ph": "X", "name": "ProcessMessages 1172", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350559917, "dur": 425, "ph": "X", "name": "ReadAsync 1172", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350560343, "dur": 5, "ph": "X", "name": "ProcessMessages 8174", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350560349, "dur": 39, "ph": "X", "name": "ReadAsync 8174", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350560390, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350560393, "dur": 41, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350560437, "dur": 413, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350560852, "dur": 491, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350561353, "dur": 74, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350561428, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350561429, "dur": 106, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350561539, "dur": 75, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350561691, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350561698, "dur": 354, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350562054, "dur": 138, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350562196, "dur": 1248, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350563446, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350563448, "dur": 98, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350563549, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350563717, "dur": 28, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350563746, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350563780, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350563782, "dur": 269, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350564055, "dur": 3393, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350567463, "dur": 4, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350567473, "dur": 4115, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350571590, "dur": 281, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350571873, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350572005, "dur": 362, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350572369, "dur": 70, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350572440, "dur": 899, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350573340, "dur": 113, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350573461, "dur": 87, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350573552, "dur": 157850, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350731418, "dur": 52, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350731471, "dur": 1488, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350732984, "dur": 352, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204350733339, "dur": 980, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 84491, "tid": 21, "ts": 1749204350749882, "dur": 157, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 84491, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 84491, "tid": 8589934592, "ts": 1749204350544371, "dur": 23568, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 84491, "tid": 8589934592, "ts": 1749204350567940, "dur": 17, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 84491, "tid": 8589934592, "ts": 1749204350567959, "dur": 2392, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 84491, "tid": 21, "ts": 1749204350750041, "dur": 14, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 84491, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 84491, "tid": 4294967296, "ts": 1749204350518759, "dur": 217961, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 84491, "tid": 4294967296, "ts": 1749204350523456, "dur": 16187, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 84491, "tid": 4294967296, "ts": 1749204350738689, "dur": 3952, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 84491, "tid": 4294967296, "ts": 1749204350740524, "dur": 1323, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 84491, "tid": 4294967296, "ts": 1749204350742686, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 84491, "tid": 21, "ts": 1749204350750067, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749204350545982, "dur": 1018, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749204350547033, "dur": 369, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749204350547494, "dur": 350, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749204350547907, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1749204350548222, "dur": 8210, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4B2961EB2BFFCE92.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749204350556804, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_41CC43B4EF068807.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749204350556946, "dur": 1529, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_E55A52E5CC4CEE1D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749204350558584, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_28490B37780E689D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749204350558820, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C31501BACA39A389.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749204350559252, "dur": 610, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_B32F13BAD3829244.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749204350559941, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_99CF543691587DC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749204350547851, "dur": 13023, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749204350560875, "dur": 172089, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749204350733195, "dur": 698, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749204350547650, "dur": 13251, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350560905, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C31501BACA39A389.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204350561076, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C31501BACA39A389.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204350561223, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_AE595DE39915AAEB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204350561446, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350561576, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_151BFCE34BFA7BA2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204350561776, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350561968, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_1FE1234F984BB51C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204350562320, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350562567, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_B32F13BAD3829244.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204350562927, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350563146, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_CC6805BBA70F838D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204350563457, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350563846, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_8419FB82A771BD09.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204350564090, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350564216, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_E5D6426FBCCA9E02.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204350564359, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350564582, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_9F8C88B8933E69C0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204350564762, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350564846, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_A607C03F75180DDC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204350564965, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350565054, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0C4991A809505204.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204350565305, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350565417, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_C5AE142EFA110487.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204350565598, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350565738, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350565955, "dur": 2649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350568604, "dur": 3308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350571912, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350572317, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204350572829, "dur": 160139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350547718, "dur": 13198, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350560926, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7B262E92DA7273A4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350561112, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C9F0A84BBDC69A30.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350561172, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_734F0E585E36251E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350561401, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350561532, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_F6A8B3361FD36936.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350561711, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350561891, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_F6A8B3361FD36936.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350561958, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E22DA60AD4A58682.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350562309, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350562475, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_66211689A3EA2E1F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350562880, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350563098, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_66211689A3EA2E1F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350563187, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_C7326E8F6E4F2E4A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350563421, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350563632, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_DE05044D8639AC06.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350564038, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350564102, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_2D9A6CA0EA6F39BE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350564307, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350564384, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_4CBFE92EEE47F615.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350564713, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350564785, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_3319E59F4DD98A07.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350564912, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350564987, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_070B58BE5D5B2CD3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350565177, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350565337, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_72824D3B12484B29.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204350565483, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350565620, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350565755, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350565983, "dur": 2670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350568653, "dur": 3263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350571916, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350572312, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204350572789, "dur": 175, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749204350572964, "dur": 159976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350547738, "dur": 13197, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350560937, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B2AE0B1790CDF67C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204350561098, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_F3A9ABDC1CD3E43E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204350561258, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350561441, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_EAA368AB880B7188.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204350561627, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350561771, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_3A55B8CBAB2C2D9D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204350562129, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350562328, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_F9861168C26A1418.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204350562705, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350562964, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_F9861168C26A1418.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204350563022, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7330BA83148C6474.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204350563323, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350563636, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_48A7D55E01C19CF4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204350564040, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350564131, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FC94EF241C3A2715.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204350564325, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350564425, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_16695793D49D23D2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204350564742, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350564824, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_C7CF74A887622C95.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204350564942, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350565017, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_2E73808CC0CC7C67.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204350565256, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350565368, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_ECC4DD4F879CE3E5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204350565547, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350565666, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350565926, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350566037, "dur": 2639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350568676, "dur": 3198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350571875, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204350572491, "dur": 345, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749204350572841, "dur": 160065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350547756, "dur": 13197, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350560956, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_0616146022ED9BC8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204350561115, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1749204350561184, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_A37B7A55B4C22ADE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204350561351, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350561508, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_62BACDD1DF5987F3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204350561685, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350561845, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_9E0DB59E9484B9BC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204350562179, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350562412, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D30C454E89E13D53.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204350562820, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350563032, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_DF3F95F9ECABCB2A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204350563384, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350563701, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_B6D19551B861A31A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204350564050, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350564151, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_28490B37780E689D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204350564334, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350564441, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_41CC43B4EF068807.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204350564738, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350564815, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_74871FCCC1AC330C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204350564933, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350565006, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_1D265465A4618B6E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204350565244, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350565345, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2EAD006E17020B66.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204350565534, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350565659, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350565873, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350566034, "dur": 2680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350568714, "dur": 2183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350571134, "dur": 356, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 4, "ts": 1749204350570901, "dur": 590, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350571491, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350571923, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204350572512, "dur": 340, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749204350572853, "dur": 160071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350547828, "dur": 13181, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350561099, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350561169, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_AB3C57437581502D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204350561399, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350561518, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_3049D5BF1BA78862.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204350561717, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350561926, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_988278E9DA26F521.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204350562283, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350562592, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_608810156C97DC07.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204350562961, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350563186, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_F52887C3D2702367.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204350563563, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350563843, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_EA4F1149DCA61077.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204350564096, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350564232, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_88E5A43CD6D33B51.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204350564363, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350564637, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_DD4EBDF18344FA39.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204350564765, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350564857, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_55ECB3E35BAD035D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204350564961, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350565043, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_B1F6AA9409CC806F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204350565292, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350565410, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_87801BB1B66F01B9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204350565586, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350565713, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350565940, "dur": 2525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350568465, "dur": 3327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350571792, "dur": 130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350571922, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350572304, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204350572825, "dur": 160149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350547834, "dur": 13189, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350561058, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E9BD22091C4C0F55.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204350561136, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_DDFB1A47FEBB9D8B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204350561374, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350561489, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_17134F6617AE59D7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204350561698, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350561906, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_2D7AC574C2A8417E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204350562300, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350562512, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_2F21EDD4C31095B2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204350562920, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350563131, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_F57C8C199FD329A6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204350563450, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350563817, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_B6C44A0EA7B17958.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204350564088, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350564196, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_0CDDF55BC4517A80.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204350564355, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350564509, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_5CF69290777FC509.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204350564760, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350564842, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_47691717A411A494.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204350564957, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350565037, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4B2961EB2BFFCE92.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204350565290, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350565396, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C72E3DF734A0A54C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204350565581, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350565704, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_4BBAC3C37A70FEEC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204350565988, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350566146, "dur": 2758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350568904, "dur": 2124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350571028, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350571840, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350572253, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204350572857, "dur": 220, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1749204350573078, "dur": 159871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204350547810, "dur": 13162, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204350560974, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_0E0F21B100678371.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350561073, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_0E0F21B100678371.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350561152, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_832CD0845587F8E9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350561400, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204350561525, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_74E8E4E65F1A79F4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350561745, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204350562033, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_F5F734ECB9162433.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350562338, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204350562641, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_F6C06255AAD22865.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350562977, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204350563218, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_E4E7BD9B686833DD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350563618, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204350563923, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_45F57B44A1A10038.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350564103, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204350564249, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_7E71F5EAF2300F39.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350564392, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204350564676, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_09492F27254B688B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350564790, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204350564887, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_5999718AF5CA3D33.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350564989, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204350565114, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_08D6B63D8BE14123.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350565331, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204350565444, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_3DB644191B1B2EED.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350565631, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204350565784, "dur": 6160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350571958, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749204350572241, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204350572375, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749204350572859, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749204350573228, "dur": 158299, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749204350547837, "dur": 13208, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350561109, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7346C78C14A9B361.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204350561318, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350561476, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_40BCDC8F67493173.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204350561732, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350562019, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D7F8ECDE08C7FBFE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204350562333, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350562624, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B8EE34001D6E068D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204350562954, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350563191, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9F06867B0901DA2B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204350563612, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350563907, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F532DB9E132EAD23.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204350564098, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350564237, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_E55A52E5CC4CEE1D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204350564369, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350564645, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_A237BB9029659CE6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204350564764, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350564873, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_A181A7FC854DF651.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204350564984, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350565082, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_2D3248B6F4439116.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204350565309, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350565413, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_CBB50F54AA1DCCC6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204350565591, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350565727, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350565949, "dur": 2701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350568650, "dur": 3281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350571931, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350572323, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204350572835, "dur": 160151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350547844, "dur": 13243, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350561088, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E8AA20EA0A63A7CC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204350561232, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350561399, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A00C04B6F2383838.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204350561621, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350561811, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_F946A2037F56BAAC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204350562144, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350562379, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_99CF543691587DC5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204350562796, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350563050, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_99CF543691587DC5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204350563105, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_8E7233A590037F9C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204350563395, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350563736, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_2F1DD4E55C5496F4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204350564062, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350564178, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A26A42CD31C19E18.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204350564354, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350564466, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_1BA83129DB600F47.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204350564740, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350564819, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_9D5F48569240A071.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204350564940, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350565010, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_605B46FA91B01D9A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204350565265, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350565376, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_D88C42BCAA83CDB7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204350565560, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350565680, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350565929, "dur": 2544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350568474, "dur": 3422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350571896, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350572296, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204350572821, "dur": 160042, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350547859, "dur": 13274, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350561134, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_5812C766893C0699.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204350561351, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350561466, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9FCDF264C0CBA08C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204350561678, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350561824, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_586A30BDD3C6CAB5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204350562205, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350562468, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F66131053E8E3F45.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204350562874, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350563068, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F66131053E8E3F45.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204350563129, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_9F16FB8791438F3F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204350563455, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350563833, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B6099A197873D297.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204350564092, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350564226, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_C32BD734DA2FDF00.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204350564358, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350564532, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3EEEFD3F09C679A1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204350564759, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350564838, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_BEB010A19CDB4C31.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204350564946, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350565028, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_15472E073F752D2D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204350565269, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350565390, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_C6A2362C94D57308.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204350565565, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350565699, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_2EEB020F9B77C62B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204350565987, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350566163, "dur": 2519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350568682, "dur": 2637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350571319, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350571924, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350572331, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204350572837, "dur": 160037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749204350734259, "dur": 219, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 84491, "tid": 21, "ts": 1749204350750357, "dur": 1102, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 84491, "tid": 21, "ts": 1749204350751507, "dur": 1247, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 84491, "tid": 21, "ts": 1749204350748054, "dur": 5261, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}