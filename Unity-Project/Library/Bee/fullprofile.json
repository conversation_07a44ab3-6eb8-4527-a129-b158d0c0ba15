{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 84491, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 84491, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 84491, "tid": 44, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 84491, "tid": 44, "ts": 1749204402620405, "dur": 965, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 84491, "tid": 44, "ts": 1749204402625101, "dur": 3530, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 84491, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 84491, "tid": 1, "ts": 1749204402394102, "dur": 5446, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 84491, "tid": 1, "ts": 1749204402399550, "dur": 10674, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 84491, "tid": 1, "ts": 1749204402410230, "dur": 4692, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 84491, "tid": 44, "ts": 1749204402628636, "dur": 39, "ph": "X", "name": "", "args": {}}, {"pid": 84491, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402392644, "dur": 326, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402392972, "dur": 213327, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402393654, "dur": 3584, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402397247, "dur": 1118, "ph": "X", "name": "ProcessMessages 8105", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398394, "dur": 51, "ph": "X", "name": "ReadAsync 8105", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398450, "dur": 6, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398457, "dur": 104, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398563, "dur": 1, "ph": "X", "name": "ProcessMessages 1816", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398566, "dur": 87, "ph": "X", "name": "ReadAsync 1816", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398657, "dur": 2, "ph": "X", "name": "ProcessMessages 1614", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398660, "dur": 64, "ph": "X", "name": "ReadAsync 1614", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398730, "dur": 1, "ph": "X", "name": "ProcessMessages 1616", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398733, "dur": 25, "ph": "X", "name": "ReadAsync 1616", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398760, "dur": 55, "ph": "X", "name": "ReadAsync 1256", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398816, "dur": 1, "ph": "X", "name": "ProcessMessages 1570", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398818, "dur": 41, "ph": "X", "name": "ReadAsync 1570", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398879, "dur": 28, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398909, "dur": 56, "ph": "X", "name": "ReadAsync 1582", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398966, "dur": 1, "ph": "X", "name": "ProcessMessages 1771", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402398967, "dur": 35, "ph": "X", "name": "ReadAsync 1771", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402399003, "dur": 1, "ph": "X", "name": "ProcessMessages 1112", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402399005, "dur": 107, "ph": "X", "name": "ReadAsync 1112", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402399113, "dur": 1, "ph": "X", "name": "ProcessMessages 1936", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402399116, "dur": 40, "ph": "X", "name": "ReadAsync 1936", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402399157, "dur": 1, "ph": "X", "name": "ProcessMessages 1269", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402399180, "dur": 43, "ph": "X", "name": "ReadAsync 1269", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402399225, "dur": 10, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402399236, "dur": 235, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402399472, "dur": 3, "ph": "X", "name": "ProcessMessages 3325", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402399476, "dur": 103, "ph": "X", "name": "ReadAsync 3325", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402399580, "dur": 1, "ph": "X", "name": "ProcessMessages 2180", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402399582, "dur": 1762, "ph": "X", "name": "ReadAsync 2180", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402401345, "dur": 256, "ph": "X", "name": "ProcessMessages 5962", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402401603, "dur": 79, "ph": "X", "name": "ReadAsync 5962", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402401684, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402401686, "dur": 551, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402402239, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402402240, "dur": 175, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402402424, "dur": 33, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402402485, "dur": 115, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402402608, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402402684, "dur": 264, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402402955, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402402960, "dur": 214, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402403178, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402403269, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402403270, "dur": 64, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402403337, "dur": 160, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402403500, "dur": 222, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402403726, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402403729, "dur": 108, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402403840, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402403843, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402403884, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402403926, "dur": 150, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402404078, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402404211, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402404230, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402404301, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402404303, "dur": 83, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402404390, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402404490, "dur": 223, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402404716, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402404792, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402404794, "dur": 84, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402404880, "dur": 10, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402404891, "dur": 134, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402405026, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402405028, "dur": 118, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402405149, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402405152, "dur": 274, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402405429, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402405431, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402405521, "dur": 174, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402405697, "dur": 202, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402405902, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402406085, "dur": 90, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402406178, "dur": 111, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402406299, "dur": 394, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402406696, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402406699, "dur": 256, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402406959, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402406962, "dur": 59, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402407023, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402407039, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402407132, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402407194, "dur": 138, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402407335, "dur": 3879, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402411217, "dur": 2118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402413337, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402413338, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402413377, "dur": 499, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402413877, "dur": 188416, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402602301, "dur": 1044, "ph": "X", "name": "ProcessMessages 2713", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402603347, "dur": 768, "ph": "X", "name": "ReadAsync 2713", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402604125, "dur": 697, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 84491, "tid": 12884901888, "ts": 1749204402604825, "dur": 710, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 84491, "tid": 44, "ts": 1749204402628678, "dur": 333, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 84491, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 84491, "tid": 8589934592, "ts": 1749204402390302, "dur": 24636, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 84491, "tid": 8589934592, "ts": 1749204402414939, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 84491, "tid": 8589934592, "ts": 1749204402414945, "dur": 2658, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 84491, "tid": 44, "ts": 1749204402629029, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 84491, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 84491, "tid": 4294967296, "ts": 1749204402359729, "dur": 248276, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 84491, "tid": 4294967296, "ts": 1749204402364142, "dur": 21894, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 84491, "tid": 4294967296, "ts": 1749204402614680, "dur": 3746, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 84491, "tid": 4294967296, "ts": 1749204402616166, "dur": 1358, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 84491, "tid": 4294967296, "ts": 1749204402618475, "dur": 7, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 84491, "tid": 44, "ts": 1749204402629034, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749204402381982, "dur": 1953, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749204402383946, "dur": 1721, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749204402385690, "dur": 266, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749204402386428, "dur": 10433, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4B2961EB2BFFCE92.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749204402397155, "dur": 1522, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_E55A52E5CC4CEE1D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749204402399388, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_17134F6617AE59D7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749204402399882, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_6DAF32D8644B89D6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749204402385963, "dur": 14810, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749204402400775, "dur": 203124, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749204402604183, "dur": 350, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749204402385872, "dur": 14909, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402400782, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C31501BACA39A389.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204402401299, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402401521, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_DDFB1A47FEBB9D8B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204402401751, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402401881, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_40BCDC8F67493173.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204402402137, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402402268, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_9E0DB59E9484B9BC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204402402467, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402402666, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_B32F13BAD3829244.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204402402893, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402403124, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_F52887C3D2702367.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204402403343, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402403493, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_B6C44A0EA7B17958.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204402403721, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402404040, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_0CDDF55BC4517A80.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204402404260, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402404461, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_5CF69290777FC509.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204402404813, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402405151, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_A607C03F75180DDC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204402405626, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402405794, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4B2961EB2BFFCE92.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204402406099, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402406371, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C72E3DF734A0A54C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204402406714, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402406965, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_4BBAC3C37A70FEEC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749204402407202, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402407447, "dur": 2118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402409566, "dur": 1728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402411294, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402411753, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749204402412100, "dur": 191723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402385885, "dur": 14905, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402400794, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7B262E92DA7273A4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204402401257, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402401480, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402401680, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_A37B7A55B4C22ADE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204402401865, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402402020, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_F6A8B3361FD36936.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204402402224, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402402379, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D7F8ECDE08C7FBFE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204402402509, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402402575, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_99CF543691587DC5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204402402803, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402403006, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_DF3F95F9ECABCB2A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204402403267, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402403469, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_2F1DD4E55C5496F4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204402403670, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402403945, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_28490B37780E689D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204402404238, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402404448, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_1BA83129DB600F47.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204402404801, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402405095, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_C7CF74A887622C95.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204402405577, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402405704, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_605B46FA91B01D9A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204402405994, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402406219, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2EAD006E17020B66.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749204402406645, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402406794, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749204402407137, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402407350, "dur": 2197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402409547, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402411299, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402411755, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749204402412107, "dur": 191726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402385910, "dur": 14888, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402400802, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B2AE0B1790CDF67C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204402401320, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402401548, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_AB3C57437581502D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204402401744, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402401810, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A00C04B6F2383838.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204402402040, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402402164, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_3A55B8CBAB2C2D9D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204402402375, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402402500, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_F9861168C26A1418.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204402402762, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402402955, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7330BA83148C6474.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204402403231, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402403385, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_DE05044D8639AC06.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204402403623, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402403776, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_2D9A6CA0EA6F39BE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204402404181, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402404320, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_4CBFE92EEE47F615.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204402404674, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402404935, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_3319E59F4DD98A07.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204402405368, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402405653, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_070B58BE5D5B2CD3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204402405951, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402406190, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_72824D3B12484B29.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749204402406597, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402406754, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749204402406836, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402407059, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749204402407196, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402407373, "dur": 2216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402409589, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402410724, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402411301, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402411756, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749204402412109, "dur": 191816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402385924, "dur": 14882, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402400830, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_0616146022ED9BC8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204402401288, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402401344, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E8AA20EA0A63A7CC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204402401711, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402401826, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_EAA368AB880B7188.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204402402080, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402402197, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_F946A2037F56BAAC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204402402416, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402402596, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D30C454E89E13D53.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204402402832, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402403019, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_8E7233A590037F9C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204402403263, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402403421, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_48A7D55E01C19CF4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204402403628, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402403805, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_FC94EF241C3A2715.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204402404218, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402404394, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_16695793D49D23D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204402404770, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402405062, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_9D5F48569240A071.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204402405602, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402405765, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_2E73808CC0CC7C67.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204402406064, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402406314, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_C6A2362C94D57308.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749204402406683, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402406877, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402407084, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749204402407182, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402407381, "dur": 2205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402409587, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402410725, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402411354, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402411759, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749204402412114, "dur": 191845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402385931, "dur": 14883, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402400817, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_0E0F21B100678371.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204402401260, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402401415, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_F3A9ABDC1CD3E43E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204402401726, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402401873, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9FCDF264C0CBA08C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204402402149, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402402282, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_2D7AC574C2A8417E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204402402488, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402402659, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_2F21EDD4C31095B2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204402402884, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402403080, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_F57C8C199FD329A6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204402403297, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402403451, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_B6D19551B861A31A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204402403659, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402403974, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A26A42CD31C19E18.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204402404230, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402404432, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_41CC43B4EF068807.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204402404747, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402405021, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_74871FCCC1AC330C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204402405503, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402405690, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_1D265465A4618B6E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204402406009, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402406276, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_ECC4DD4F879CE3E5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749204402406652, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402406846, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402407060, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402407225, "dur": 2320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402409545, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402411303, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402411758, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749204402412111, "dur": 191731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402385932, "dur": 14891, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402400826, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_86010DDA4934CAC2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402401248, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402401370, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749204402401520, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402401720, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_AE595DE39915AAEB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402401926, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402402088, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_151BFCE34BFA7BA2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402402298, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402402448, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_F5F734ECB9162433.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402402669, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402402824, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_F6C06255AAD22865.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402403141, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402403271, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_E4E7BD9B686833DD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402403543, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402403666, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_45F57B44A1A10038.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402404056, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402404212, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_7E71F5EAF2300F39.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402404480, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402404756, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_09492F27254B688B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402405190, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402405560, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_5999718AF5CA3D33.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402405869, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402406072, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_08D6B63D8BE14123.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402406544, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402406750, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_3DB644191B1B2EED.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402407135, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749204402407337, "dur": 4042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402411390, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749204402411777, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749204402412089, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749204402412178, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749204402413093, "dur": 189268, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749204402385940, "dur": 14891, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402400833, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E9BD22091C4C0F55.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204402401322, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402401542, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_832CD0845587F8E9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204402401756, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402401886, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_17134F6617AE59D7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204402402138, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402402265, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_586A30BDD3C6CAB5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204402402453, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402402640, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_66211689A3EA2E1F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204402402882, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402403091, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_CC6805BBA70F838D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204402403366, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402403534, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_EA4F1149DCA61077.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204402403754, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402404112, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_88E5A43CD6D33B51.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204402404332, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402404573, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_DD4EBDF18344FA39.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204402404939, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402405241, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_55ECB3E35BAD035D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204402405671, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402405884, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_2D3248B6F4439116.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204402406194, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402406483, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_CBB50F54AA1DCCC6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749204402406741, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402407027, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402407188, "dur": 2278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402409466, "dur": 1746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402411212, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402411734, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749204402412091, "dur": 191765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402385944, "dur": 14894, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402400840, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_BD7F14C36A74D17C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204402401326, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402401584, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_734F0E585E36251E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204402401790, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402401957, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_3049D5BF1BA78862.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204402402167, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402402332, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_1FE1234F984BB51C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204402402526, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402402726, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B8EE34001D6E068D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204402403013, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402403174, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9F06867B0901DA2B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204402403386, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402403553, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F532DB9E132EAD23.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204402403789, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402404126, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_E55A52E5CC4CEE1D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204402404373, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402404606, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_A237BB9029659CE6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204402404970, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402405247, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_A181A7FC854DF651.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204402405663, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402405850, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0C4991A809505204.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204402406127, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402406447, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_87801BB1B66F01B9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749204402406735, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402407006, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749204402407213, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402407452, "dur": 2033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402409485, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402411264, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402411750, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402412084, "dur": 6232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749204402418316, "dur": 185547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402385945, "dur": 14899, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402400846, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7DC73530330A44CA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204402401271, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402401445, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7DC73530330A44CA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204402401503, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7346C78C14A9B361.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204402401763, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402401961, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_74E8E4E65F1A79F4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204402402167, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402402304, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E22DA60AD4A58682.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204402402515, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402402629, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F66131053E8E3F45.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204402402850, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402403069, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_9F16FB8791438F3F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204402403353, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402403515, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B6099A197873D297.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204402403725, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402404065, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_E5D6426FBCCA9E02.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204402404285, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402404502, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3EEEFD3F09C679A1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204402404871, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402405138, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_BEB010A19CDB4C31.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204402405618, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402405843, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_B1F6AA9409CC806F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204402406205, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402406502, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_C5AE142EFA110487.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749204402406776, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402406992, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749204402407066, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402407252, "dur": 2290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402409543, "dur": 1749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402411292, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402411752, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749204402412096, "dur": 191839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402385984, "dur": 14867, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402400851, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C9F0A84BBDC69A30.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402401265, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402401511, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_5812C766893C0699.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402401764, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402401930, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_62BACDD1DF5987F3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402402153, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402402292, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_988278E9DA26F521.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402402492, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402402683, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_608810156C97DC07.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402402906, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402403166, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_C7326E8F6E4F2E4A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402403382, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402403548, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_8419FB82A771BD09.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402403742, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402404085, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_C32BD734DA2FDF00.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402404293, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402404537, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_9F8C88B8933E69C0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402404883, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402405141, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_47691717A411A494.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402405610, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402405780, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_15472E073F752D2D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402406079, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402406298, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_D88C42BCAA83CDB7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402406695, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402406902, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_2EEB020F9B77C62B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402407192, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402407344, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_2EEB020F9B77C62B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749204402407404, "dur": 2168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402409573, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402410851, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402410955, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402411730, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402412076, "dur": 5650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402417951, "dur": 359, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.1.6f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 10, "ts": 1749204402417726, "dur": 586, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749204402418313, "dur": 185655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749204402604979, "dur": 218, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 84491, "tid": 44, "ts": 1749204402629540, "dur": 1743, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 84491, "tid": 44, "ts": 1749204402631344, "dur": 1192, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 84491, "tid": 44, "ts": 1749204402623801, "dur": 9352, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}