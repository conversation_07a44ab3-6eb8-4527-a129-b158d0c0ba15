using UnityEngine;
using UnityEngine.UI;
#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// QuickSetup - One-click setup for the entire cozy bedroom project
/// This script automates the complete setup process
/// </summary>
public class QuickSetup : MonoBehaviour
{
    [Header("Quick Setup Options")]
    [SerializeField] private bool setupEverything = true;
    [SerializeField] private bool createMaterials = true;
    [SerializeField] private bool createScene = true;
    [SerializeField] private bool setupUI = true;
    [SerializeField] private bool configureCamera = true;
    [SerializeField] private bool setupLighting = true;
    
    [ContextMenu("🚀 COMPLETE BEDROOM SETUP")]
    public void CompleteBedroomSetup()
    {
        Debug.Log("🚀 Starting complete cozy bedroom setup...");
        
        if (setupEverything)
        {
            Step1_CreateMaterials();
            Step2_CreateScene();
            Step3_SetupUI();
            Step4_ConfigureCamera();
            Step5_SetupLighting();
            Step6_FinalConfiguration();
            
            Debug.Log("✅ COMPLETE BEDROOM SETUP FINISHED!");
            Debug.Log("🎮 Your cozy bedroom is ready! Press Play to test.");
            Debug.Log("📋 Next: Build for WebGL (File > Build Settings > WebGL)");
        }
    }
    
    private void Step1_CreateMaterials()
    {
        if (!createMaterials) return;
        
        Debug.Log("🎨 Step 1: Creating materials...");
        
        // Find or create material creator
        BedroomMaterialCreator materialCreator = FindObjectOfType<BedroomMaterialCreator>();
        if (materialCreator == null)
        {
            GameObject creatorObj = new GameObject("MaterialCreator");
            materialCreator = creatorObj.AddComponent<BedroomMaterialCreator>();
        }
        
        // Create all materials
        materialCreator.CreateAllBedroomMaterials();
        
        Debug.Log("✅ Step 1 Complete: Materials created");
    }
    
    private void Step2_CreateScene()
    {
        if (!createScene) return;
        
        Debug.Log("🏠 Step 2: Creating bedroom scene...");
        
        // Find or create scene setup
        BedroomSceneSetup sceneSetup = FindObjectOfType<BedroomSceneSetup>();
        if (sceneSetup == null)
        {
            GameObject setupObj = new GameObject("SceneSetup");
            sceneSetup = setupObj.AddComponent<BedroomSceneSetup>();
        }
        
        // Create complete bedroom
        sceneSetup.CreateCompleteBedroomScene();
        
        Debug.Log("✅ Step 2 Complete: Bedroom scene created");
    }
    
    private void Step3_SetupUI()
    {
        if (!setupUI) return;
        
        Debug.Log("🖥️ Step 3: Setting up UI...");
        
        // Create Material UI Canvas
        GameObject materialCanvas = CreateUICanvas("MaterialUI", 10);
        
        // Create Camera UI Canvas
        GameObject cameraCanvas = CreateUICanvas("CameraUI", 5);
        
        // Setup Material Manager
        SetupMaterialManager(materialCanvas);
        
        // Setup Camera Controller
        SetupCameraController(cameraCanvas);
        
        Debug.Log("✅ Step 3 Complete: UI setup finished");
    }
    
    private GameObject CreateUICanvas(string canvasName, int sortOrder)
    {
        GameObject canvasObj = GameObject.Find(canvasName);
        if (canvasObj == null)
        {
            canvasObj = new GameObject(canvasName);
            
            Canvas canvas = canvasObj.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = sortOrder;
            
            CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            
            canvasObj.AddComponent<GraphicRaycaster>();
        }
        
        return canvasObj;
    }
    
    private void SetupMaterialManager(GameObject materialCanvas)
    {
        BedroomMaterialManager materialManager = FindObjectOfType<BedroomMaterialManager>();
        if (materialManager == null)
        {
            GameObject managerObj = new GameObject("MaterialManager");
            materialManager = managerObj.AddComponent<BedroomMaterialManager>();
        }
        
        // Auto-assign references
        AssignMaterialManagerReferences(materialManager, materialCanvas);
    }
    
    private void AssignMaterialManagerReferences(BedroomMaterialManager manager, GameObject canvas)
    {
#if UNITY_EDITOR
        SerializedObject serializedManager = new SerializedObject(manager);
        
        // Assign UI Canvas
        serializedManager.FindProperty("materialUICanvas").objectReferenceValue = canvas.GetComponent<Canvas>();
        
        // Find and assign renderers
        GameObject floor = GameObject.Find("Floor");
        if (floor != null)
        {
            serializedManager.FindProperty("floorRenderer").objectReferenceValue = floor.GetComponent<Renderer>();
        }
        
        GameObject ceiling = GameObject.Find("Ceiling");
        if (ceiling != null)
        {
            serializedManager.FindProperty("ceilingRenderer").objectReferenceValue = ceiling.GetComponent<Renderer>();
        }
        
        // Find wall renderers
        GameObject[] walls = {
            GameObject.Find("Wall_North"),
            GameObject.Find("Wall_South"),
            GameObject.Find("Wall_East"),
            GameObject.Find("Wall_West")
        };
        
        SerializedProperty wallRenderersProperty = serializedManager.FindProperty("wallRenderers");
        wallRenderersProperty.arraySize = 4;
        
        for (int i = 0; i < walls.Length; i++)
        {
            if (walls[i] != null)
            {
                wallRenderersProperty.GetArrayElementAtIndex(i).objectReferenceValue = walls[i].GetComponent<Renderer>();
            }
        }
        
        // Load and assign materials
        AssignMaterials(serializedManager);
        
        serializedManager.ApplyModifiedProperties();
#endif
    }
    
#if UNITY_EDITOR
    private void AssignMaterials(SerializedObject serializedManager)
    {
        string materialPath = "Assets/Materials/Bedroom/";
        
        // Floor materials
        string[] floorMaterialNames = { "HardwoodOak", "CozyCarpet", "ModernLaminate", "BambooFlooring" };
        AssignMaterialArray(serializedManager, "floorMaterials", materialPath + "Floors/", floorMaterialNames);
        
        // Wall materials
        string[] wallMaterialNames = { "WarmCream", "FloralWallpaper", "WoodPaneling", "CalmingBlue" };
        AssignMaterialArray(serializedManager, "wallMaterials", materialPath + "Walls/", wallMaterialNames);
        
        // Ceiling materials
        string[] ceilingMaterialNames = { "ClassicWhite", "ExposedBeams", "TexturedCream", "CofferedDesign" };
        AssignMaterialArray(serializedManager, "ceilingMaterials", materialPath + "Ceilings/", ceilingMaterialNames);
    }
    
    private void AssignMaterialArray(SerializedObject serializedObject, string propertyName, string basePath, string[] materialNames)
    {
        SerializedProperty materialsProperty = serializedObject.FindProperty(propertyName);
        materialsProperty.arraySize = materialNames.Length;
        
        for (int i = 0; i < materialNames.Length; i++)
        {
            string materialPath = basePath + materialNames[i] + ".mat";
            Material material = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
            
            if (material != null)
            {
                materialsProperty.GetArrayElementAtIndex(i).objectReferenceValue = material;
            }
            else
            {
                Debug.LogWarning($"⚠️ Material not found: {materialPath}");
            }
        }
    }
#endif
    
    private void SetupCameraController(GameObject cameraCanvas)
    {
        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            GameObject cameraObj = new GameObject("Main Camera");
            mainCamera = cameraObj.AddComponent<Camera>();
            cameraObj.tag = "MainCamera";
        }
        
        BedroomCameraController cameraController = mainCamera.GetComponent<BedroomCameraController>();
        if (cameraController == null)
        {
            cameraController = mainCamera.gameObject.AddComponent<BedroomCameraController>();
        }
        
        // Auto-assign camera references
        AssignCameraControllerReferences(cameraController, cameraCanvas);
    }
    
    private void AssignCameraControllerReferences(BedroomCameraController controller, GameObject canvas)
    {
#if UNITY_EDITOR
        SerializedObject serializedController = new SerializedObject(controller);
        
        // Assign UI Canvas
        serializedController.FindProperty("cameraUICanvas").objectReferenceValue = canvas.GetComponent<Canvas>();
        
        // Create and assign camera target
        GameObject target = GameObject.Find("CameraTarget");
        if (target == null)
        {
            target = new GameObject("CameraTarget");
            target.transform.position = new Vector3(0, 1.2f, 0);
        }
        
        serializedController.FindProperty("target").objectReferenceValue = target.transform;
        
        serializedController.ApplyModifiedProperties();
#endif
    }
    
    private void Step4_ConfigureCamera()
    {
        if (!configureCamera) return;
        
        Debug.Log("📷 Step 4: Configuring camera...");
        
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            // Position camera for good initial view
            mainCamera.transform.position = new Vector3(3, 2.5f, -3);
            mainCamera.transform.LookAt(new Vector3(0, 1, 0));
            
            // Configure camera settings
            mainCamera.fieldOfView = 60f;
            mainCamera.nearClipPlane = 0.1f;
            mainCamera.farClipPlane = 100f;
        }
        
        Debug.Log("✅ Step 4 Complete: Camera configured");
    }
    
    private void Step5_SetupLighting()
    {
        if (!setupLighting) return;
        
        Debug.Log("💡 Step 5: Setting up lighting...");
        
        // Configure ambient lighting
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
        RenderSettings.ambientSkyColor = new Color(0.53f, 0.81f, 0.92f); // Light blue
        RenderSettings.ambientEquatorColor = new Color(1f, 0.95f, 0.8f); // Warm white
        RenderSettings.ambientGroundColor = new Color(0.18f, 0.18f, 0.18f); // Dark gray
        RenderSettings.ambientIntensity = 0.4f;
        
        // Configure fog for atmosphere
        RenderSettings.fog = true;
        RenderSettings.fogColor = new Color(0.9f, 0.9f, 0.85f);
        RenderSettings.fogMode = FogMode.ExponentialSquared;
        RenderSettings.fogDensity = 0.01f;
        
        Debug.Log("✅ Step 5 Complete: Lighting configured");
    }
    
    private void Step6_FinalConfiguration()
    {
        Debug.Log("🔧 Step 6: Final configuration...");
        
        // Set quality settings for better performance
        QualitySettings.shadows = ShadowQuality.All;
        QualitySettings.shadowResolution = ShadowResolution.Medium;
        QualitySettings.shadowDistance = 20f;
        
        // Configure physics for better performance
        Physics.defaultSolverIterations = 4;
        Physics.defaultSolverVelocityIterations = 1;
        
        Debug.Log("✅ Step 6 Complete: Final configuration done");
        
        // Save scene
#if UNITY_EDITOR
        UnityEditor.SceneManagement.EditorSceneManager.SaveOpenScenes();
#endif
    }
    
    [ContextMenu("🧹 Clean Up Scene")]
    public void CleanUpScene()
    {
        // Remove setup objects
        GameObject materialCreator = GameObject.Find("MaterialCreator");
        if (materialCreator != null) DestroyImmediate(materialCreator);
        
        GameObject sceneSetup = GameObject.Find("SceneSetup");
        if (sceneSetup != null) DestroyImmediate(sceneSetup);
        
        // Remove this script's GameObject
        DestroyImmediate(this.gameObject);
        
        Debug.Log("🧹 Scene cleaned up - setup objects removed");
    }
}
