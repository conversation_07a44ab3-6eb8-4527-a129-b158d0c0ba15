using UnityEngine;

/// <summary>
/// RealisticCameraController - Smooth WASD + Mouse camera controls for bedroom exploration
/// Professional camera movement with realistic constraints
/// </summary>
public class RealisticCameraController : MonoBehaviour
{
    [Header("🎮 Camera Controls")]
    [SerializeField] private float moveSpeed = 5f;
    [SerializeField] private float fastMoveSpeed = 10f;
    [SerializeField] private float mouseSensitivity = 2f;
    [SerializeField] private float smoothTime = 0.1f;
    
    [Header("Movement Constraints")]
    [SerializeField] private float minHeight = 0.5f;
    [SerializeField] private float maxHeight = 4f;
    [SerializeField] private Vector3 roomBounds = new Vector3(8f, 5f, 8f);
    
    [Header("Look Constraints")]
    [SerializeField] private float minLookAngle = -80f;
    [SerializeField] private float maxLookAngle = 80f;
    
    // Private variables
    private Vector3 velocity;
    private Vector3 targetPosition;
    private float rotationX = 0f;
    private float rotationY = 0f;
    private bool isControlEnabled = true;
    
    // Input tracking
    private Vector3 moveInput;
    private Vector2 mouseInput;
    
    void Start()
    {
        // Lock cursor for better camera control
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
        
        // Initialize target position
        targetPosition = transform.position;
        
        // Get initial rotation
        Vector3 currentRotation = transform.eulerAngles;
        rotationY = currentRotation.y;
        rotationX = currentRotation.x;
        
        Debug.Log("🎮 Realistic Camera Controller initialized");
        Debug.Log("📋 Controls: WASD = Move | Mouse = Look | Shift = Fast | Ctrl = Slow | Esc = Toggle Cursor");
    }
    
    void Update()
    {
        if (!isControlEnabled) return;
        
        HandleInput();
        UpdateMovement();
        UpdateRotation();
        HandleCursorToggle();
    }
    
    private void HandleInput()
    {
        // Movement input
        float horizontal = Input.GetAxis("Horizontal"); // A/D
        float vertical = Input.GetAxis("Vertical");     // W/S
        float upDown = 0f;
        
        // Up/Down movement
        if (Input.GetKey(KeyCode.Q)) upDown = -1f; // Down
        if (Input.GetKey(KeyCode.E)) upDown = 1f;  // Up
        
        moveInput = new Vector3(horizontal, upDown, vertical);
        
        // Mouse input
        if (Cursor.lockState == CursorLockMode.Locked)
        {
            mouseInput.x = Input.GetAxis("Mouse X") * mouseSensitivity;
            mouseInput.y = Input.GetAxis("Mouse Y") * mouseSensitivity;
        }
        else
        {
            mouseInput = Vector2.zero;
        }
    }
    
    private void UpdateMovement()
    {
        if (moveInput.magnitude > 0.1f)
        {
            // Calculate movement speed
            float currentSpeed = moveSpeed;
            
            if (Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift))
            {
                currentSpeed = fastMoveSpeed; // Fast movement
            }
            else if (Input.GetKey(KeyCode.LeftControl) || Input.GetKey(KeyCode.RightControl))
            {
                currentSpeed = moveSpeed * 0.3f; // Slow movement
            }
            
            // Calculate movement direction relative to camera
            Vector3 forward = transform.forward;
            Vector3 right = transform.right;
            Vector3 up = Vector3.up;
            
            // Remove Y component from forward and right for ground movement
            forward.y = 0f;
            right.y = 0f;
            forward.Normalize();
            right.Normalize();
            
            // Calculate target movement
            Vector3 movement = (forward * moveInput.z + right * moveInput.x + up * moveInput.y) * currentSpeed * Time.deltaTime;
            
            // Update target position
            targetPosition += movement;
            
            // Apply room bounds
            targetPosition.x = Mathf.Clamp(targetPosition.x, -roomBounds.x/2f, roomBounds.x/2f);
            targetPosition.y = Mathf.Clamp(targetPosition.y, minHeight, maxHeight);
            targetPosition.z = Mathf.Clamp(targetPosition.z, -roomBounds.z/2f, roomBounds.z/2f);
        }
        
        // Smooth movement
        transform.position = Vector3.SmoothDamp(transform.position, targetPosition, ref velocity, smoothTime);
    }
    
    private void UpdateRotation()
    {
        if (mouseInput.magnitude > 0.1f)
        {
            // Update rotation values
            rotationY += mouseInput.x;
            rotationX -= mouseInput.y;
            
            // Clamp vertical rotation
            rotationX = Mathf.Clamp(rotationX, minLookAngle, maxLookAngle);
            
            // Apply rotation
            transform.rotation = Quaternion.Euler(rotationX, rotationY, 0f);
        }
    }
    
    private void HandleCursorToggle()
    {
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            ToggleCursor();
        }
    }
    
    public void ToggleCursor()
    {
        if (Cursor.lockState == CursorLockMode.Locked)
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
            Debug.Log("🖱️ Cursor unlocked - Press ESC to lock again");
        }
        else
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
            Debug.Log("🖱️ Cursor locked - Use mouse to look around");
        }
    }
    
    public void SetControlEnabled(bool enabled)
    {
        isControlEnabled = enabled;
        
        if (!enabled)
        {
            moveInput = Vector3.zero;
            mouseInput = Vector2.zero;
        }
        
        Debug.Log($"🎮 Camera controls {(enabled ? "enabled" : "disabled")}");
    }
    
    public void SetPosition(Vector3 newPosition)
    {
        transform.position = newPosition;
        targetPosition = newPosition;
        velocity = Vector3.zero;
    }
    
    public void SetRotation(Vector3 newRotation)
    {
        transform.rotation = Quaternion.Euler(newRotation);
        rotationX = newRotation.x;
        rotationY = newRotation.y;
    }
    
    public void FocusOnObject(Transform target, float distance = 3f)
    {
        if (target == null) return;
        
        Vector3 direction = (transform.position - target.position).normalized;
        Vector3 newPosition = target.position + direction * distance;
        
        // Ensure position is within bounds
        newPosition.x = Mathf.Clamp(newPosition.x, -roomBounds.x/2f, roomBounds.x/2f);
        newPosition.y = Mathf.Clamp(newPosition.y, minHeight, maxHeight);
        newPosition.z = Mathf.Clamp(newPosition.z, -roomBounds.z/2f, roomBounds.z/2f);
        
        SetPosition(newPosition);
        transform.LookAt(target.position);
        
        // Update rotation values
        Vector3 currentRotation = transform.eulerAngles;
        rotationY = currentRotation.y;
        rotationX = currentRotation.x;
        
        Debug.Log($"🎯 Camera focused on {target.name}");
    }
    
    // Preset camera positions
    [ContextMenu("📷 Bedroom Overview")]
    public void SetBedroomOverview()
    {
        SetPosition(new Vector3(4f, 2.5f, -4f));
        SetRotation(new Vector3(15f, 45f, 0f));
        Debug.Log("📷 Camera set to bedroom overview");
    }
    
    [ContextMenu("🛏️ Bed Focus")]
    public void SetBedFocus()
    {
        SetPosition(new Vector3(2f, 1.5f, -1f));
        SetRotation(new Vector3(10f, 135f, 0f));
        Debug.Log("🛏️ Camera focused on bed");
    }
    
    [ContextMenu("🪟 Window View")]
    public void SetWindowView()
    {
        SetPosition(new Vector3(-2f, 1.8f, 2f));
        SetRotation(new Vector3(0f, -45f, 0f));
        Debug.Log("🪟 Camera set to window view");
    }
    
    [ContextMenu("🚪 Door View")]
    public void SetDoorView()
    {
        SetPosition(new Vector3(0f, 1.8f, -4f));
        SetRotation(new Vector3(0f, 0f, 0f));
        Debug.Log("🚪 Camera set to door view");
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw room bounds
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireCube(Vector3.zero, roomBounds);
        
        // Draw height constraints
        Gizmos.color = Color.red;
        Gizmos.DrawWireCube(new Vector3(0, minHeight, 0), new Vector3(roomBounds.x, 0.1f, roomBounds.z));
        Gizmos.color = Color.green;
        Gizmos.DrawWireCube(new Vector3(0, maxHeight, 0), new Vector3(roomBounds.x, 0.1f, roomBounds.z));
    }
}
