# Complete Unity 3D Cozy Bedroom Setup Instructions

## 🚀 Quick Start Guide

### Step 1: Create Unity Project
1. Open Unity Hub
2. Click "New Project"
3. Select "3D (URP)" template
4. Name: "CozyBedroomEditor"
5. Click "Create Project"

### Step 2: Install Required Packages
1. Open Window > Package Manager
2. Install these packages:
   - Universal Render Pipeline (should be included)
   - ProBuilder
   - TextMeshPro
   - Post Processing

### Step 3: Copy Scripts to Unity
1. Create folder: `Assets/Scripts/`
2. Copy all 4 scripts from `Unity-Project/Assets/Scripts/` to this folder:
   - `BedroomMaterialManager.cs`
   - `BedroomCameraController.cs`
   - `BedroomSceneSetup.cs`
   - `BedroomMaterialCreator.cs`

### Step 4: Create Materials
1. In Unity, create empty GameObject named "MaterialCreator"
2. Attach `BedroomMaterialCreator.cs` script
3. In Inspector, right-click on script > "Create All Bedroom Materials"
4. This creates all materials in `Assets/Materials/Bedroom/`

### Step 5: Create Bedroom Scene
1. Create empty GameObject named "SceneSetup"
2. Attach `BedroomSceneSetup.cs` script
3. In Inspector, right-click on script > "Create Complete Bedroom Scene"
4. This creates the entire bedroom with furniture and lighting

### Step 6: Setup Material Manager
1. Find the "MaterialManager" GameObject created in Step 5
2. In Inspector, assign these references:
   - **Material UI Canvas**: Create UI > Canvas, name it "MaterialUI"
   - **Floor Renderer**: Drag "Floor" from scene
   - **Wall Renderers**: Drag all 4 walls from scene
   - **Ceiling Renderer**: Drag "Ceiling" from scene
   - **Floor Materials**: Drag 4 floor materials from Assets/Materials/Bedroom/Floors/
   - **Wall Materials**: Drag 4 wall materials from Assets/Materials/Bedroom/Walls/
   - **Ceiling Materials**: Drag 4 ceiling materials from Assets/Materials/Bedroom/Ceilings/

### Step 7: Setup Camera Controller
1. Find "Main Camera" in scene
2. Ensure `BedroomCameraController.cs` is attached
3. In Inspector, assign:
   - **Camera UI Canvas**: Create UI > Canvas, name it "CameraUI"
   - **Target**: Create empty GameObject at (0, 1.2, 0), name it "CameraTarget"

### Step 8: Configure UI Canvases
1. **MaterialUI Canvas**:
   - Canvas Scaler: Scale With Screen Size
   - Reference Resolution: 1920x1080
   - Sort Order: 10

2. **CameraUI Canvas**:
   - Canvas Scaler: Scale With Screen Size
   - Reference Resolution: 1920x1080
   - Sort Order: 5

### Step 9: Test the Scene
1. Press Play
2. You should see:
   - Material control panel on the left
   - Camera control panel on bottom right
   - Mouse controls working
   - Material dropdowns functional

### Step 10: Build for WebGL
1. File > Build Settings
2. Add current scene
3. Switch to WebGL platform
4. Player Settings:
   - Company Name: Pionare
   - Product Name: Cozy Bedroom Editor
   - WebGL Template: Default
   - Compression Format: Gzip
5. Build to: `YourProject/public/unity/cozy-bedroom/Build/`

## 🎮 Controls Reference

### Mouse Controls
- **Left Click + Drag**: Rotate camera around bedroom
- **Scroll Wheel**: Zoom in/out
- **UI Clicks**: Interact with material and camera panels

### Keyboard Shortcuts
- **1-4**: Apply camera presets (Overview, Bed Focus, Corner, Detail)
- **R**: Reset camera to default position
- **T**: Toggle material UI panel
- **C**: Toggle camera UI panel
- **Space**: Randomize all materials

### UI Controls
- **Material Panel (Left)**:
  - Floor Material dropdown
  - Wall Material dropdown
  - Ceiling Material dropdown
  - Reset Materials button
  - Random Materials button

- **Camera Panel (Bottom Right)**:
  - 4 Preset buttons
  - Zoom slider
  - Reset Camera button
  - Smooth Toggle button

## 🎨 Material Options

### Floor Materials
1. **Hardwood Oak** - Warm brown wood flooring
2. **Cozy Carpet** - Soft beige carpet
3. **Modern Laminate** - Contemporary gray laminate
4. **Bamboo Flooring** - Natural bamboo finish

### Wall Materials
1. **Warm Cream** - Classic cream paint
2. **Floral Wallpaper** - Subtle floral pattern
3. **Wood Paneling** - Rustic wood panels
4. **Calming Blue** - Peaceful blue paint

### Ceiling Materials
1. **Classic White** - Traditional white plaster
2. **Exposed Beams** - Rustic wooden beams
3. **Textured Cream** - Textured cream finish
4. **Coffered Design** - Elegant coffered ceiling

## 📷 Camera Presets

1. **Overview** (Key: 1)
   - Full room view from corner
   - Best for seeing overall design

2. **Bed Focus** (Key: 2)
   - Close view of bed area
   - Good for detailed bed inspection

3. **Corner View** (Key: 3)
   - Diagonal room perspective
   - Shows room depth and layout

4. **Detail View** (Key: 4)
   - Close-up for material inspection
   - Best for seeing material textures

## 🔧 Troubleshooting

### Common Issues

**Materials not changing:**
- Check that renderers are assigned in MaterialManager
- Verify materials are in correct folders
- Ensure UI Canvas is properly configured

**Camera not responding:**
- Check that CameraController script is attached to Main Camera
- Verify Target is assigned
- Make sure Camera UI Canvas exists

**UI not visible:**
- Check Canvas render mode is Screen Space - Overlay
- Verify Canvas Scaler settings
- Ensure Sort Order is correct

**Build errors:**
- Make sure all scripts compile without errors
- Check that all materials and prefabs are included
- Verify WebGL platform is selected

### Performance Tips

**For better performance:**
- Use lower resolution textures for WebGL
- Reduce shadow quality in lighting settings
- Optimize material count
- Use LOD groups for furniture if needed

## 🎯 Expected Results

After completing setup, you should have:
- ✅ Realistic 3D cozy bedroom
- ✅ Built-in Unity UI controls
- ✅ 4 material options each for floor, walls, ceiling
- ✅ Smooth camera controls with presets
- ✅ Keyboard shortcuts for quick access
- ✅ Professional lighting and shadows
- ✅ WebGL build ready for deployment

The bedroom will look like a real cozy room with proper furniture, lighting, and materials that change instantly when selected from the UI panels!
