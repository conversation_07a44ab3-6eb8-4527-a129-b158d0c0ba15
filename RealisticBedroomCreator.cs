using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Rendering;
using UnityEngine.Rendering.HighDefinition;

/// <summary>
/// RealisticBedroomCreator - Creates photorealistic bedroom with HDRP
/// Complete system for realistic room generation and customization
/// </summary>
public class RealisticBedroomCreator : MonoBehaviour
{
    [Header("🛏️ REALISTIC BEDROOM CREATOR")]
    [Space(10)]
    [TextArea(3, 5)]
    public string instructions = "1. Click 'Create Realistic Bedroom'\n2. Use WASD + Mouse to explore\n3. Press T for material UI\n4. Press L for lighting controls\n5. Press R to reset camera";
    
    [Header("Room Configuration")]
    [SerializeField] private Vector3 roomSize = new Vector3(6f, 3f, 5f);
    [SerializeField] private bool createAdvancedLighting = true;
    [SerializeField] private bool createRealisticMaterials = true;
    [SerializeField] private bool setupCameraControls = true;
    [SerializeField] private bool createUI = true;
    
    [Header("Lighting Settings")]
    [SerializeField] private Color sunlightColor = new Color(1f, 0.95f, 0.8f);
    [SerializeField] private float sunlightIntensity = 3f;
    [SerializeField] private Color ambientColor = new Color(0.2f, 0.3f, 0.4f);
    
    // Private variables
    private GameObject bedroomParent;
    private Camera mainCamera;
    private Volume globalVolume;
    
    // Material arrays
    private Material[] floorMaterials;
    private Material[] wallMaterials;
    private Material[] ceilingMaterials;
    private Material[] furnitureMaterials;
    
    // UI Components
    private Canvas uiCanvas;
    private GameObject materialPanel;
    private bool isUIVisible = true;
    
    void Start()
    {
        Debug.Log("🛏️ Realistic Bedroom Creator initialized");
        Debug.Log("📋 Click 'Create Realistic Bedroom' in the Inspector to begin!");
    }
    
    [ContextMenu("🛏️ Create Realistic Bedroom")]
    public void CreateRealisticBedroom()
    {
        Debug.Log("🚀 Creating photorealistic bedroom with HDRP...");
        
        // Step 1: Clear existing content
        ClearExistingBedroom();
        
        // Step 2: Create room structure
        CreateRoomStructure();
        
        // Step 3: Create realistic furniture
        CreateRealisticFurniture();
        
        // Step 4: Setup HDRP lighting
        if (createAdvancedLighting)
        {
            SetupHDRPLighting();
        }
        
        // Step 5: Create realistic materials
        if (createRealisticMaterials)
        {
            CreateRealisticMaterials();
        }
        
        // Step 6: Setup camera
        if (setupCameraControls)
        {
            SetupRealisticCamera();
        }
        
        // Step 7: Create UI
        if (createUI)
        {
            CreateMaterialUI();
        }
        
        // Step 8: Configure HDRP settings
        ConfigureHDRPSettings();
        
        Debug.Log("✅ PHOTOREALISTIC BEDROOM CREATED!");
        Debug.Log("🎮 Controls: WASD + Mouse | T = Materials | L = Lighting | R = Reset");
    }
    
    private void ClearExistingBedroom()
    {
        // Remove existing bedroom objects
        GameObject[] existingRooms = {
            GameObject.Find("RealisticBedroom"),
            GameObject.Find("BedroomLighting"),
            GameObject.Find("BedroomUI")
        };
        
        foreach (GameObject room in existingRooms)
        {
            if (room != null)
            {
                DestroyImmediate(room);
            }
        }
        
        Debug.Log("🧹 Cleared existing bedroom objects");
    }
    
    private void CreateRoomStructure()
    {
        Debug.Log("🏗️ Creating room structure...");
        
        // Create main bedroom parent
        bedroomParent = new GameObject("RealisticBedroom");
        bedroomParent.transform.position = Vector3.zero;
        
        // Create room components
        CreateFloor();
        CreateWalls();
        CreateCeiling();
        CreateWindows();
        
        Debug.Log("✅ Room structure created");
    }
    
    private void CreateFloor()
    {
        GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
        floor.name = "Floor";
        floor.transform.SetParent(bedroomParent.transform);
        floor.transform.position = Vector3.zero;
        floor.transform.localScale = new Vector3(roomSize.x / 10f, 1f, roomSize.z / 10f);
        
        // Add realistic floor material
        Renderer floorRenderer = floor.GetComponent<Renderer>();
        floorRenderer.material = CreateHDRPMaterial("HardwoodFloor", new Color(0.6f, 0.4f, 0.2f), 0.1f, 0.8f);
    }
    
    private void CreateWalls()
    {
        // North Wall
        CreateWall("Wall_North", new Vector3(0, roomSize.y/2f, roomSize.z/2f), 
                  new Vector3(roomSize.x, roomSize.y, 0.1f));
        
        // South Wall  
        CreateWall("Wall_South", new Vector3(0, roomSize.y/2f, -roomSize.z/2f), 
                  new Vector3(roomSize.x, roomSize.y, 0.1f));
        
        // East Wall
        CreateWall("Wall_East", new Vector3(roomSize.x/2f, roomSize.y/2f, 0), 
                  new Vector3(0.1f, roomSize.y, roomSize.z));
        
        // West Wall
        CreateWall("Wall_West", new Vector3(-roomSize.x/2f, roomSize.y/2f, 0), 
                  new Vector3(0.1f, roomSize.y, roomSize.z));
    }
    
    private void CreateWall(string name, Vector3 position, Vector3 scale)
    {
        GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        wall.name = name;
        wall.transform.SetParent(bedroomParent.transform);
        wall.transform.position = position;
        wall.transform.localScale = scale;
        
        // Add realistic wall material
        Renderer wallRenderer = wall.GetComponent<Renderer>();
        wallRenderer.material = CreateHDRPMaterial("WallPaint", new Color(0.95f, 0.9f, 0.85f), 0f, 0.3f);
    }
    
    private void CreateCeiling()
    {
        GameObject ceiling = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ceiling.name = "Ceiling";
        ceiling.transform.SetParent(bedroomParent.transform);
        ceiling.transform.position = new Vector3(0, roomSize.y, 0);
        ceiling.transform.rotation = Quaternion.Euler(180, 0, 0);
        ceiling.transform.localScale = new Vector3(roomSize.x / 10f, 1f, roomSize.z / 10f);
        
        // Add realistic ceiling material
        Renderer ceilingRenderer = ceiling.GetComponent<Renderer>();
        ceilingRenderer.material = CreateHDRPMaterial("CeilingPaint", Color.white, 0f, 0.2f);
    }
    
    private void CreateWindows()
    {
        // Create window opening in south wall
        GameObject window = GameObject.CreatePrimitive(PrimitiveType.Cube);
        window.name = "Window";
        window.transform.SetParent(bedroomParent.transform);
        window.transform.position = new Vector3(1.5f, 1.5f, roomSize.z/2f - 0.05f);
        window.transform.localScale = new Vector3(1.5f, 1.2f, 0.02f);
        
        // Create glass material
        Renderer windowRenderer = window.GetComponent<Renderer>();
        windowRenderer.material = CreateGlassMaterial();
    }
    
    private void CreateRealisticFurniture()
    {
        Debug.Log("🛏️ Creating realistic furniture...");
        
        GameObject furniture = new GameObject("Furniture");
        furniture.transform.SetParent(bedroomParent.transform);
        
        // Create detailed furniture pieces
        CreateKingSizeBed(furniture.transform);
        CreateNightstands(furniture.transform);
        CreateDresserWithMirror(furniture.transform);
        CreateComfortableChair(furniture.transform);
        CreateDecorations(furniture.transform);
        CreateLighting(furniture.transform);
        
        Debug.Log("✅ Realistic furniture created");
    }
    
    private void CreateKingSizeBed(Transform parent)
    {
        GameObject bed = new GameObject("KingSizeBed");
        bed.transform.SetParent(parent);
        bed.transform.position = new Vector3(0, 0, 0.5f);
        
        // Bed frame (wooden)
        GameObject frame = GameObject.CreatePrimitive(PrimitiveType.Cube);
        frame.name = "BedFrame";
        frame.transform.SetParent(bed.transform);
        frame.transform.localPosition = Vector3.zero;
        frame.transform.localScale = new Vector3(2.4f, 0.5f, 1.8f);
        frame.GetComponent<Renderer>().material = CreateHDRPMaterial("DarkWood", new Color(0.3f, 0.2f, 0.1f), 0.1f, 0.7f);
        
        // Mattress (fabric)
        GameObject mattress = GameObject.CreatePrimitive(PrimitiveType.Cube);
        mattress.name = "Mattress";
        mattress.transform.SetParent(bed.transform);
        mattress.transform.localPosition = new Vector3(0, 0.3f, 0);
        mattress.transform.localScale = new Vector3(2.2f, 0.4f, 1.7f);
        mattress.GetComponent<Renderer>().material = CreateHDRPMaterial("Fabric", new Color(0.9f, 0.9f, 0.85f), 0f, 0.1f);
        
        // Headboard (upholstered)
        GameObject headboard = GameObject.CreatePrimitive(PrimitiveType.Cube);
        headboard.name = "Headboard";
        headboard.transform.SetParent(bed.transform);
        headboard.transform.localPosition = new Vector3(0, 1f, 0.9f);
        headboard.transform.localScale = new Vector3(2.4f, 1.5f, 0.15f);
        headboard.GetComponent<Renderer>().material = CreateHDRPMaterial("Upholstery", new Color(0.4f, 0.3f, 0.25f), 0f, 0.3f);
        
        // Pillows
        for (int i = 0; i < 4; i++)
        {
            GameObject pillow = GameObject.CreatePrimitive(PrimitiveType.Cube);
            pillow.name = $"Pillow_{i + 1}";
            pillow.transform.SetParent(bed.transform);
            pillow.transform.localPosition = new Vector3((i % 2 - 0.5f) * 0.8f, 0.6f, 0.6f - (i / 2) * 0.3f);
            pillow.transform.localScale = new Vector3(0.5f, 0.2f, 0.4f);
            pillow.GetComponent<Renderer>().material = CreateHDRPMaterial("PillowFabric", new Color(0.95f, 0.95f, 0.9f), 0f, 0.1f);
        }
        
        // Bedsheets
        GameObject sheets = GameObject.CreatePrimitive(PrimitiveType.Cube);
        sheets.name = "Bedsheets";
        sheets.transform.SetParent(bed.transform);
        sheets.transform.localPosition = new Vector3(0, 0.52f, -0.2f);
        sheets.transform.localScale = new Vector3(2.1f, 0.02f, 1.2f);
        sheets.GetComponent<Renderer>().material = CreateHDRPMaterial("Bedsheets", new Color(0.8f, 0.85f, 0.9f), 0f, 0.2f);
    }

    private void CreateNightstands(Transform parent)
    {
        // Left nightstand
        GameObject leftNightstand = CreateNightstand("Nightstand_Left", new Vector3(-1.8f, 0.4f, 0.5f));
        leftNightstand.transform.SetParent(parent);

        // Right nightstand
        GameObject rightNightstand = CreateNightstand("Nightstand_Right", new Vector3(1.8f, 0.4f, 0.5f));
        rightNightstand.transform.SetParent(parent);
    }

    private GameObject CreateNightstand(string name, Vector3 position)
    {
        GameObject nightstand = GameObject.CreatePrimitive(PrimitiveType.Cube);
        nightstand.name = name;
        nightstand.transform.position = position;
        nightstand.transform.localScale = new Vector3(0.7f, 0.8f, 0.5f);
        nightstand.GetComponent<Renderer>().material = CreateHDRPMaterial("WoodFurniture", new Color(0.4f, 0.3f, 0.2f), 0.1f, 0.6f);

        // Add lamp on top
        GameObject lamp = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        lamp.name = name + "_Lamp";
        lamp.transform.SetParent(nightstand.transform);
        lamp.transform.localPosition = new Vector3(0, 0.6f, 0);
        lamp.transform.localScale = new Vector3(0.4f, 0.5f, 0.4f);
        lamp.GetComponent<Renderer>().material = CreateHDRPMaterial("LampBase", new Color(0.8f, 0.8f, 0.8f), 0.8f, 0.9f);

        return nightstand;
    }

    private void CreateDresserWithMirror(Transform parent)
    {
        GameObject dresser = GameObject.CreatePrimitive(PrimitiveType.Cube);
        dresser.name = "Dresser";
        dresser.transform.SetParent(parent);
        dresser.transform.position = new Vector3(-2.5f, 0.6f, -2f);
        dresser.transform.localScale = new Vector3(1.8f, 1.2f, 0.7f);
        dresser.GetComponent<Renderer>().material = CreateHDRPMaterial("WoodFurniture", new Color(0.4f, 0.3f, 0.2f), 0.1f, 0.6f);

        // Mirror above dresser
        GameObject mirror = GameObject.CreatePrimitive(PrimitiveType.Cube);
        mirror.name = "Mirror";
        mirror.transform.SetParent(dresser.transform);
        mirror.transform.localPosition = new Vector3(0, 1.3f, -0.4f);
        mirror.transform.localScale = new Vector3(0.9f, 1f, 0.05f);
        mirror.GetComponent<Renderer>().material = CreateMirrorMaterial();
    }

    private void CreateComfortableChair(Transform parent)
    {
        GameObject chair = GameObject.CreatePrimitive(PrimitiveType.Cube);
        chair.name = "ComfortableChair";
        chair.transform.SetParent(parent);
        chair.transform.position = new Vector3(2.5f, 0.4f, -1.5f);
        chair.transform.localScale = new Vector3(0.8f, 0.8f, 0.8f);
        chair.GetComponent<Renderer>().material = CreateHDRPMaterial("ChairFabric", new Color(0.6f, 0.5f, 0.4f), 0f, 0.3f);

        // Chair back
        GameObject chairBack = GameObject.CreatePrimitive(PrimitiveType.Cube);
        chairBack.name = "ChairBack";
        chairBack.transform.SetParent(chair.transform);
        chairBack.transform.localPosition = new Vector3(0, 0.8f, 0.35f);
        chairBack.transform.localScale = new Vector3(1f, 1f, 0.15f);
        chairBack.GetComponent<Renderer>().material = CreateHDRPMaterial("ChairFabric", new Color(0.6f, 0.5f, 0.4f), 0f, 0.3f);
    }

    private void CreateDecorations(Transform parent)
    {
        // Wall art
        GameObject painting = GameObject.CreatePrimitive(PrimitiveType.Cube);
        painting.name = "WallArt";
        painting.transform.SetParent(parent);
        painting.transform.position = new Vector3(0, 2f, 2.45f);
        painting.transform.localScale = new Vector3(1.5f, 1f, 0.05f);
        painting.GetComponent<Renderer>().material = CreateHDRPMaterial("Painting", new Color(0.8f, 0.7f, 0.6f), 0f, 0.1f);

        // Plant in corner
        GameObject plant = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        plant.name = "Plant";
        plant.transform.SetParent(parent);
        plant.transform.position = new Vector3(-2.8f, 0.5f, 2.2f);
        plant.transform.localScale = new Vector3(0.4f, 1f, 0.4f);
        plant.GetComponent<Renderer>().material = CreateHDRPMaterial("PlantGreen", new Color(0.2f, 0.6f, 0.2f), 0f, 0.3f);

        // Luxury rug under bed
        GameObject rug = GameObject.CreatePrimitive(PrimitiveType.Plane);
        rug.name = "LuxuryRug";
        rug.transform.SetParent(parent);
        rug.transform.position = new Vector3(0, 0.01f, 0);
        rug.transform.localScale = new Vector3(0.4f, 1f, 0.3f);
        rug.GetComponent<Renderer>().material = CreateHDRPMaterial("Rug", new Color(0.7f, 0.6f, 0.5f), 0f, 0.2f);

        // Books on nightstand
        for (int i = 0; i < 3; i++)
        {
            GameObject book = GameObject.CreatePrimitive(PrimitiveType.Cube);
            book.name = $"Book_{i + 1}";
            book.transform.SetParent(parent);
            book.transform.position = new Vector3(1.5f + i * 0.05f, 0.85f, 0.3f + i * 0.1f);
            book.transform.localScale = new Vector3(0.15f, 0.02f, 0.2f);
            book.GetComponent<Renderer>().material = CreateHDRPMaterial($"Book{i}",
                new Color(Random.Range(0.3f, 0.8f), Random.Range(0.3f, 0.8f), Random.Range(0.3f, 0.8f)), 0f, 0.4f);
        }
    }

    private void CreateLighting(Transform parent)
    {
        GameObject lightingParent = new GameObject("BedroomLighting");
        lightingParent.transform.SetParent(parent);

        // Ceiling light fixture
        GameObject ceilingFixture = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        ceilingFixture.name = "CeilingLightFixture";
        ceilingFixture.transform.SetParent(lightingParent.transform);
        ceilingFixture.transform.position = new Vector3(0, roomSize.y - 0.1f, 0);
        ceilingFixture.transform.localScale = new Vector3(0.6f, 0.1f, 0.6f);
        ceilingFixture.GetComponent<Renderer>().material = CreateHDRPMaterial("LightFixture", new Color(0.9f, 0.9f, 0.9f), 0.8f, 0.9f);
    }

    private void SetupHDRPLighting()
    {
        Debug.Log("💡 Setting up HDRP lighting...");

        // Create lighting parent
        GameObject lightingParent = new GameObject("HDRPLighting");

        // Main directional light (sunlight)
        GameObject sunLight = new GameObject("SunLight");
        sunLight.transform.SetParent(lightingParent.transform);
        sunLight.transform.position = new Vector3(5, 8, 5);
        sunLight.transform.rotation = Quaternion.Euler(45f, -45f, 0);

        Light directionalLight = sunLight.AddComponent<Light>();
        directionalLight.type = LightType.Directional;
        directionalLight.color = sunlightColor;
        directionalLight.intensity = sunlightIntensity;
        directionalLight.shadows = LightShadows.Soft;

        // Configure HDRP light
        HDAdditionalLightData hdLight = sunLight.GetComponent<HDAdditionalLightData>();
        if (hdLight == null) hdLight = sunLight.AddComponent<HDAdditionalLightData>();
        hdLight.lightUnit = LightUnit.Lux;
        hdLight.intensity = 50000f; // Realistic sunlight intensity
        hdLight.enableShadows = true;
        hdLight.shadowResolution = 512;

        // Ceiling light
        GameObject ceilingLight = new GameObject("CeilingLight");
        ceilingLight.transform.SetParent(lightingParent.transform);
        ceilingLight.transform.position = new Vector3(0, roomSize.y - 0.2f, 0);

        Light pointLight = ceilingLight.AddComponent<Light>();
        pointLight.type = LightType.Point;
        pointLight.color = new Color(1f, 0.95f, 0.8f);
        pointLight.intensity = 2f;
        pointLight.range = 8f;
        pointLight.shadows = LightShadows.Soft;

        // Configure HDRP point light
        HDAdditionalLightData hdPointLight = ceilingLight.GetComponent<HDAdditionalLightData>();
        if (hdPointLight == null) hdPointLight = ceilingLight.AddComponent<HDAdditionalLightData>();
        hdPointLight.lightUnit = LightUnit.Lumen;
        hdPointLight.intensity = 1000f;
        hdPointLight.enableShadows = true;

        // Bedside lamps
        CreateBedsideLamp("BedsideLamp_Left", lightingParent.transform, new Vector3(-1.8f, 1.2f, 0.5f));
        CreateBedsideLamp("BedsideLamp_Right", lightingParent.transform, new Vector3(1.8f, 1.2f, 0.5f));

        Debug.Log("✅ HDRP lighting setup complete");
    }

    private void CreateBedsideLamp(string name, Transform parent, Vector3 position)
    {
        GameObject lamp = new GameObject(name);
        lamp.transform.SetParent(parent);
        lamp.transform.position = position;

        Light spotLight = lamp.AddComponent<Light>();
        spotLight.type = LightType.Spot;
        spotLight.color = new Color(1f, 0.9f, 0.7f);
        spotLight.intensity = 1.5f;
        spotLight.range = 4f;
        spotLight.spotAngle = 45f;
        spotLight.shadows = LightShadows.Soft;

        // Configure HDRP spot light
        HDAdditionalLightData hdSpotLight = lamp.GetComponent<HDAdditionalLightData>();
        if (hdSpotLight == null) hdSpotLight = lamp.AddComponent<HDAdditionalLightData>();
        hdSpotLight.lightUnit = LightUnit.Lumen;
        hdSpotLight.intensity = 300f;
        hdSpotLight.enableShadows = true;

        lamp.transform.rotation = Quaternion.Euler(45f, 0, 0);
    }

    private Material CreateHDRPMaterial(string name, Color baseColor, float metallic, float smoothness)
    {
        Material material = new Material(Shader.Find("HDRP/Lit"));
        material.name = name;
        material.SetColor("_BaseColor", baseColor);
        material.SetFloat("_Metallic", metallic);
        material.SetFloat("_Smoothness", smoothness);

        // Enable emission for realistic look
        if (name.Contains("Light"))
        {
            material.SetColor("_EmissionColor", baseColor * 2f);
            material.EnableKeyword("_EMISSION");
        }

        return material;
    }

    private Material CreateGlassMaterial()
    {
        Material glass = new Material(Shader.Find("HDRP/Lit"));
        glass.name = "RealisticGlass";
        glass.SetFloat("_SurfaceType", 1); // Transparent
        glass.SetFloat("_BlendMode", 0); // Alpha
        glass.SetColor("_BaseColor", new Color(0.9f, 0.95f, 1f, 0.3f));
        glass.SetFloat("_Metallic", 0f);
        glass.SetFloat("_Smoothness", 0.95f);
        glass.SetFloat("_IOR", 1.5f); // Index of refraction for glass

        return glass;
    }

    private Material CreateMirrorMaterial()
    {
        Material mirror = new Material(Shader.Find("HDRP/Lit"));
        mirror.name = "RealisticMirror";
        mirror.SetColor("_BaseColor", new Color(0.9f, 0.9f, 0.9f));
        mirror.SetFloat("_Metallic", 1f);
        mirror.SetFloat("_Smoothness", 1f);

        return mirror;
    }

    private void CreateRealisticMaterials()
    {
        Debug.Log("🎨 Creating realistic HDRP materials...");

        // Create material arrays for customization
        floorMaterials = new Material[4];
        wallMaterials = new Material[4];
        ceilingMaterials = new Material[4];
        furnitureMaterials = new Material[4];

        // Floor materials
        floorMaterials[0] = CreateHDRPMaterial("OakHardwood", new Color(0.6f, 0.4f, 0.2f), 0.1f, 0.8f);
        floorMaterials[1] = CreateHDRPMaterial("LuxuryCarpet", new Color(0.8f, 0.7f, 0.6f), 0f, 0.2f);
        floorMaterials[2] = CreateHDRPMaterial("ModernTile", new Color(0.5f, 0.5f, 0.5f), 0.2f, 0.9f);
        floorMaterials[3] = CreateHDRPMaterial("BambooFlooring", new Color(0.7f, 0.6f, 0.3f), 0.1f, 0.7f);

        // Wall materials
        wallMaterials[0] = CreateHDRPMaterial("WarmCream", new Color(0.95f, 0.9f, 0.8f), 0f, 0.3f);
        wallMaterials[1] = CreateHDRPMaterial("ElegantWallpaper", new Color(0.9f, 0.85f, 0.9f), 0f, 0.1f);
        wallMaterials[2] = CreateHDRPMaterial("WoodPaneling", new Color(0.4f, 0.3f, 0.2f), 0f, 0.6f);
        wallMaterials[3] = CreateHDRPMaterial("SoftBlue", new Color(0.7f, 0.8f, 0.9f), 0f, 0.4f);

        // Ceiling materials
        ceilingMaterials[0] = CreateHDRPMaterial("PureWhite", Color.white, 0f, 0.2f);
        ceilingMaterials[1] = CreateHDRPMaterial("WoodBeams", new Color(0.6f, 0.4f, 0.2f), 0.1f, 0.5f);
        ceilingMaterials[2] = CreateHDRPMaterial("TexturedCream", new Color(1f, 0.97f, 0.86f), 0f, 0.3f);
        ceilingMaterials[3] = CreateHDRPMaterial("ModernConcrete", new Color(0.7f, 0.7f, 0.7f), 0f, 0.4f);

        // Furniture materials
        furnitureMaterials[0] = CreateHDRPMaterial("DarkWalnut", new Color(0.3f, 0.2f, 0.1f), 0.1f, 0.7f);
        furnitureMaterials[1] = CreateHDRPMaterial("LightOak", new Color(0.7f, 0.5f, 0.3f), 0.1f, 0.6f);
        furnitureMaterials[2] = CreateHDRPMaterial("ModernMetal", new Color(0.8f, 0.8f, 0.8f), 0.9f, 0.9f);
        furnitureMaterials[3] = CreateHDRPMaterial("SoftFabric", new Color(0.9f, 0.9f, 0.85f), 0f, 0.1f);

        Debug.Log("✅ Realistic materials created");
    }

    private void SetupRealisticCamera()
    {
        Debug.Log("📷 Setting up realistic camera...");

        // Find or create main camera
        mainCamera = Camera.main;
        if (mainCamera == null)
        {
            GameObject cameraObj = new GameObject("Main Camera");
            mainCamera = cameraObj.AddComponent<Camera>();
            cameraObj.tag = "MainCamera";
        }

        // Position camera for optimal bedroom view
        mainCamera.transform.position = new Vector3(4, 2.5f, -4);
        mainCamera.transform.LookAt(new Vector3(0, 1.2f, 0));

        // Configure camera settings for realism
        mainCamera.fieldOfView = 60f;
        mainCamera.nearClipPlane = 0.1f;
        mainCamera.farClipPlane = 100f;

        // Add HDRP camera data
        HDAdditionalCameraData hdCamera = mainCamera.GetComponent<HDAdditionalCameraData>();
        if (hdCamera == null) hdCamera = mainCamera.gameObject.AddComponent<HDAdditionalCameraData>();

        // Configure HDRP camera settings
        hdCamera.antialiasing = HDAdditionalCameraData.AntialiasingMode.TemporalAntialiasing;
        hdCamera.dithering = true;
        hdCamera.stopNaNs = true;

        // Add camera controller
        RealisticCameraController cameraController = mainCamera.GetComponent<RealisticCameraController>();
        if (cameraController == null)
        {
            cameraController = mainCamera.gameObject.AddComponent<RealisticCameraController>();
        }

        Debug.Log("✅ Realistic camera setup complete");
    }

    private void ConfigureHDRPSettings()
    {
        Debug.Log("⚙️ Configuring HDRP settings...");

        // Create or find global volume
        globalVolume = FindObjectOfType<Volume>();
        if (globalVolume == null)
        {
            GameObject volumeObj = new GameObject("Global Volume");
            globalVolume = volumeObj.AddComponent<Volume>();
            globalVolume.isGlobal = true;
        }

        // Configure ambient lighting
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
        RenderSettings.ambientSkyColor = new Color(0.53f, 0.81f, 0.92f);
        RenderSettings.ambientEquatorColor = new Color(1f, 0.95f, 0.8f);
        RenderSettings.ambientGroundColor = new Color(0.18f, 0.18f, 0.18f);
        RenderSettings.ambientIntensity = 0.3f;

        // Configure fog for atmosphere
        RenderSettings.fog = true;
        RenderSettings.fogColor = new Color(0.9f, 0.9f, 0.85f);
        RenderSettings.fogMode = FogMode.ExponentialSquared;
        RenderSettings.fogDensity = 0.005f;

        Debug.Log("✅ HDRP settings configured");
    }

    private void CreateMaterialUI()
    {
        Debug.Log("🖥️ Creating material UI...");

        // Create UI Canvas
        GameObject canvasObj = new GameObject("BedroomUI");
        uiCanvas = canvasObj.AddComponent<Canvas>();
        uiCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
        uiCanvas.sortingOrder = 100;

        CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);

        canvasObj.AddComponent<GraphicRaycaster>();

        // Create material panel
        materialPanel = new GameObject("MaterialPanel");
        materialPanel.transform.SetParent(uiCanvas.transform, false);

        Image panelBg = materialPanel.AddComponent<Image>();
        panelBg.color = new Color(0, 0, 0, 0.8f);

        RectTransform panelRect = materialPanel.GetComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0, 0);
        panelRect.anchorMax = new Vector2(0.3f, 1);
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;

        VerticalLayoutGroup layout = materialPanel.AddComponent<VerticalLayoutGroup>();
        layout.padding = new RectOffset(20, 20, 20, 20);
        layout.spacing = 15;
        layout.childControlHeight = false;
        layout.childControlWidth = true;
        layout.childForceExpandWidth = true;

        // Create UI title
        CreateUITitle("🛏️ Realistic Bedroom Designer", materialPanel.transform);

        // Create material controls
        CreateMaterialControls();

        // Create lighting controls
        CreateLightingControls();

        // Create action buttons
        CreateActionButtons();

        Debug.Log("✅ Material UI created");
    }

    private void CreateUITitle(string titleText, Transform parent)
    {
        GameObject titleObj = new GameObject("Title");
        titleObj.transform.SetParent(parent, false);

        Text title = titleObj.AddComponent<Text>();
        title.text = titleText;
        title.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        title.fontSize = 20;
        title.fontStyle = FontStyle.Bold;
        title.color = new Color(1f, 0.84f, 0f);
        title.alignment = TextAnchor.MiddleCenter;

        Outline outline = titleObj.AddComponent<Outline>();
        outline.effectColor = Color.black;
        outline.effectDistance = new Vector2(1, 1);

        RectTransform titleRect = titleObj.GetComponent<RectTransform>();
        titleRect.sizeDelta = new Vector2(0, 40);
    }

    private void CreateMaterialControls()
    {
        // Floor material dropdown
        CreateMaterialDropdown("Floor Material", new string[] { "Oak Hardwood", "Luxury Carpet", "Modern Tile", "Bamboo" },
                              (int value) => ChangeMaterial("Floor", floorMaterials[value]));

        // Wall material dropdown
        CreateMaterialDropdown("Wall Material", new string[] { "Warm Cream", "Elegant Wallpaper", "Wood Paneling", "Soft Blue" },
                              (int value) => ChangeMaterial("Wall", wallMaterials[value]));

        // Ceiling material dropdown
        CreateMaterialDropdown("Ceiling Material", new string[] { "Pure White", "Wood Beams", "Textured Cream", "Modern Concrete" },
                              (int value) => ChangeMaterial("Ceiling", ceilingMaterials[value]));
    }

    private void CreateMaterialDropdown(string labelText, string[] options, UnityEngine.Events.UnityAction<int> onValueChanged)
    {
        GameObject container = new GameObject(labelText + "Container");
        container.transform.SetParent(materialPanel.transform, false);

        VerticalLayoutGroup containerLayout = container.AddComponent<VerticalLayoutGroup>();
        containerLayout.spacing = 5;

        RectTransform containerRect = container.GetComponent<RectTransform>();
        containerRect.sizeDelta = new Vector2(0, 70);

        // Label
        GameObject labelObj = new GameObject("Label");
        labelObj.transform.SetParent(container.transform, false);

        Text label = labelObj.AddComponent<Text>();
        label.text = labelText;
        label.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        label.fontSize = 14;
        label.fontStyle = FontStyle.Bold;
        label.color = new Color(1f, 0.84f, 0f);

        RectTransform labelRect = labelObj.GetComponent<RectTransform>();
        labelRect.sizeDelta = new Vector2(0, 25);

        // Dropdown
        GameObject dropdownObj = new GameObject("Dropdown");
        dropdownObj.transform.SetParent(container.transform, false);

        Image dropdownBg = dropdownObj.AddComponent<Image>();
        dropdownBg.color = new Color(0.2f, 0.2f, 0.2f, 0.9f);

        Dropdown dropdown = dropdownObj.AddComponent<Dropdown>();
        dropdown.options.Clear();
        foreach (string option in options)
        {
            dropdown.options.Add(new Dropdown.OptionData(option));
        }

        dropdown.onValueChanged.AddListener(onValueChanged);
        dropdown.value = 0;
        dropdown.RefreshShownValue();

        RectTransform dropdownRect = dropdownObj.GetComponent<RectTransform>();
        dropdownRect.sizeDelta = new Vector2(0, 35);
    }

    private void CreateLightingControls()
    {
        // Lighting intensity slider
        CreateSlider("Sunlight Intensity", 0f, 10f, sunlightIntensity, (float value) => {
            sunlightIntensity = value;
            UpdateLighting();
        });
    }

    private void CreateSlider(string labelText, float minValue, float maxValue, float currentValue, UnityEngine.Events.UnityAction<float> onValueChanged)
    {
        GameObject container = new GameObject(labelText + "Container");
        container.transform.SetParent(materialPanel.transform, false);

        VerticalLayoutGroup containerLayout = container.AddComponent<VerticalLayoutGroup>();
        containerLayout.spacing = 5;

        RectTransform containerRect = container.GetComponent<RectTransform>();
        containerRect.sizeDelta = new Vector2(0, 60);

        // Label
        GameObject labelObj = new GameObject("Label");
        labelObj.transform.SetParent(container.transform, false);

        Text label = labelObj.AddComponent<Text>();
        label.text = labelText;
        label.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        label.fontSize = 14;
        label.fontStyle = FontStyle.Bold;
        label.color = new Color(1f, 0.84f, 0f);

        RectTransform labelRect = labelObj.GetComponent<RectTransform>();
        labelRect.sizeDelta = new Vector2(0, 25);

        // Slider
        GameObject sliderObj = new GameObject("Slider");
        sliderObj.transform.SetParent(container.transform, false);

        Slider slider = sliderObj.AddComponent<Slider>();
        slider.minValue = minValue;
        slider.maxValue = maxValue;
        slider.value = currentValue;
        slider.onValueChanged.AddListener(onValueChanged);

        RectTransform sliderRect = sliderObj.GetComponent<RectTransform>();
        sliderRect.sizeDelta = new Vector2(0, 25);
    }

    private void CreateActionButtons()
    {
        GameObject buttonContainer = new GameObject("ActionButtons");
        buttonContainer.transform.SetParent(materialPanel.transform, false);

        VerticalLayoutGroup buttonLayout = buttonContainer.AddComponent<VerticalLayoutGroup>();
        buttonLayout.spacing = 10;

        RectTransform buttonContainerRect = buttonContainer.GetComponent<RectTransform>();
        buttonContainerRect.sizeDelta = new Vector2(0, 150);

        // Reset button
        CreateButton("🔄 Reset Materials", buttonContainer.transform, ResetMaterials, new Color(0.8f, 0.3f, 0.3f));

        // Random button
        CreateButton("🎲 Random Design", buttonContainer.transform, RandomizeDesign, new Color(0.3f, 0.7f, 0.3f));

        // Toggle UI button
        CreateButton("👁️ Toggle UI", buttonContainer.transform, ToggleUI, new Color(0.3f, 0.5f, 0.8f));
    }

    private void CreateButton(string buttonText, Transform parent, UnityEngine.Events.UnityAction onClick, Color buttonColor)
    {
        GameObject buttonObj = new GameObject("Button_" + buttonText);
        buttonObj.transform.SetParent(parent, false);

        Image buttonBg = buttonObj.AddComponent<Image>();
        buttonBg.color = buttonColor;

        Button button = buttonObj.AddComponent<Button>();
        button.targetGraphic = buttonBg;
        button.onClick.AddListener(onClick);

        // Button text
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(buttonObj.transform, false);

        Text text = textObj.AddComponent<Text>();
        text.text = buttonText;
        text.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        text.fontSize = 12;
        text.fontStyle = FontStyle.Bold;
        text.color = Color.white;
        text.alignment = TextAnchor.MiddleCenter;

        RectTransform textRect = textObj.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;

        RectTransform buttonRect = buttonObj.GetComponent<RectTransform>();
        buttonRect.sizeDelta = new Vector2(0, 35);
    }

    // UI Action Methods
    private void ChangeMaterial(string objectType, Material newMaterial)
    {
        GameObject[] objects = null;

        switch (objectType)
        {
            case "Floor":
                objects = new GameObject[] { GameObject.Find("Floor") };
                break;
            case "Wall":
                objects = new GameObject[] {
                    GameObject.Find("Wall_North"),
                    GameObject.Find("Wall_South"),
                    GameObject.Find("Wall_East"),
                    GameObject.Find("Wall_West")
                };
                break;
            case "Ceiling":
                objects = new GameObject[] { GameObject.Find("Ceiling") };
                break;
        }

        if (objects != null)
        {
            foreach (GameObject obj in objects)
            {
                if (obj != null)
                {
                    Renderer renderer = obj.GetComponent<Renderer>();
                    if (renderer != null)
                    {
                        renderer.material = newMaterial;
                    }
                }
            }
        }

        Debug.Log($"🎨 Changed {objectType} material to {newMaterial.name}");
    }

    private void UpdateLighting()
    {
        Light sunLight = GameObject.Find("SunLight")?.GetComponent<Light>();
        if (sunLight != null)
        {
            sunLight.intensity = sunlightIntensity;
            HDAdditionalLightData hdLight = sunLight.GetComponent<HDAdditionalLightData>();
            if (hdLight != null)
            {
                hdLight.intensity = sunlightIntensity * 10000f;
            }
        }

        Debug.Log($"💡 Updated sunlight intensity to {sunlightIntensity}");
    }

    private void ResetMaterials()
    {
        if (floorMaterials != null && floorMaterials.Length > 0)
        {
            ChangeMaterial("Floor", floorMaterials[0]);
            ChangeMaterial("Wall", wallMaterials[0]);
            ChangeMaterial("Ceiling", ceilingMaterials[0]);
        }

        sunlightIntensity = 3f;
        UpdateLighting();

        Debug.Log("🔄 Materials reset to defaults");
    }

    private void RandomizeDesign()
    {
        if (floorMaterials != null && floorMaterials.Length > 0)
        {
            ChangeMaterial("Floor", floorMaterials[Random.Range(0, floorMaterials.Length)]);
            ChangeMaterial("Wall", wallMaterials[Random.Range(0, wallMaterials.Length)]);
            ChangeMaterial("Ceiling", ceilingMaterials[Random.Range(0, ceilingMaterials.Length)]);
        }

        sunlightIntensity = Random.Range(1f, 8f);
        UpdateLighting();

        Debug.Log("🎲 Design randomized!");
    }

    private void ToggleUI()
    {
        isUIVisible = !isUIVisible;
        if (materialPanel != null)
        {
            materialPanel.SetActive(isUIVisible);
        }

        Debug.Log($"👁️ UI {(isUIVisible ? "shown" : "hidden")}");
    }

    // Keyboard shortcuts
    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.T))
        {
            ToggleUI();
        }

        if (Input.GetKeyDown(KeyCode.R))
        {
            ResetMaterials();
        }

        if (Input.GetKeyDown(KeyCode.Space))
        {
            RandomizeDesign();
        }

        if (Input.GetKeyDown(KeyCode.L))
        {
            sunlightIntensity = sunlightIntensity > 5f ? 1f : sunlightIntensity + 1f;
            UpdateLighting();
        }
    }

    [ContextMenu("🧹 Clear Bedroom")]
    public void ClearBedroom()
    {
        ClearExistingBedroom();
        Debug.Log("🧹 Bedroom cleared");
    }

    [ContextMenu("🎨 Apply Random Materials")]
    public void ApplyRandomMaterials()
    {
        RandomizeDesign();
    }
}
