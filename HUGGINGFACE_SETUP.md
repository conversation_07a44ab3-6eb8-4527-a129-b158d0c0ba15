# Hugging Face AI Image Generation Setup

This guide will help you set up AI-powered room customization using Hugging Face's free Stable Diffusion API.

## 🚀 Quick Setup (5 minutes)

### Step 1: Get Your Free Hugging Face API Key

1. **Visit Hugging Face**: Go to [https://huggingface.co](https://huggingface.co)
2. **Create Account**: Sign up for a free account (if you don't have one)
3. **Generate API Key**: 
   - Go to [https://huggingface.co/settings/tokens](https://huggingface.co/settings/tokens)
   - Click "New token"
   - Name it "Pionare Room Generator"
   - Select "Read" permissions
   - Click "Generate"
   - **Copy the token** (you'll need it in the next step)

### Step 2: Configure Your Environment

1. **Create Environment File**:
   ```bash
   cp .env.example .env.local
   ```

2. **Add Your API Key**:
   Open `.env.local` and replace `your_huggingface_api_key_here` with your actual API key:
   ```
   NEXT_PUBLIC_HUGGINGFACE_API_KEY=hf_your_actual_api_key_here
   ```

3. **Restart Your Development Server**:
   ```bash
   npm run dev
   ```

## ✅ Test the Integration

1. **Open Virtual Showroom**: Go to `http://localhost:3000/virtual-showroom`
2. **Navigate to Gallery**: Click on "Gallery" tab
3. **Select a Room**: Click on any room card
4. **Customize Materials**: Choose different flooring, walls, ceiling, and lighting
5. **Apply Configuration**: Click "Apply Configuration"
6. **Wait for AI Generation**: The system will generate a customized room image (takes 5-10 seconds)

## 🎯 How It Works

### AI Image Generation Process

1. **User Selects Materials**: User chooses flooring, walls, ceiling, and lighting options
2. **Prompt Creation**: System creates a detailed text prompt describing the desired room
3. **AI Generation**: Hugging Face Stable Diffusion generates a new room image
4. **Result Display**: User sees before/after comparison with their customized room

### Example Generated Prompt

```
A beautiful luxury hotel suite interior design, hospitality space, 
featuring premium oak hardwood flooring, 
clean white painted walls, 
smooth white gypsum ceiling, 
and modern LED recessed lighting.

Professional interior photography, high resolution, 
realistic lighting, modern architecture, 
clean and elegant design, 
photorealistic rendering, 
architectural visualization, 
interior design magazine quality,
8k resolution, detailed textures,
warm and inviting atmosphere.
```

## 🆓 Free Tier Limits

- **Hugging Face Free Tier**: 1,000 API calls per month
- **Image Generation Time**: 5-10 seconds per image
- **Image Quality**: 768x512 pixels (high quality)
- **No Credit Card Required**: Completely free to start

## 🔧 Advanced Configuration

### Using Higher Quality Models

The system supports multiple AI models:

1. **Stable Diffusion v1.5** (Default - Fast)
2. **Stable Diffusion XL** (Higher Quality - Slower)

To use SDXL, the system automatically falls back to it for better quality.

### Fallback System

If AI generation fails or API key is not configured:
- System uses enhanced versions of original images
- Applies filters to simulate material changes
- Still provides visual feedback to users

## 🎨 Customization Options

### Material Categories

Each category has 4 professional options:

**Flooring:**
- Premium Oak Hardwood ($85/sqm)
- Carrara White Marble ($120/sqm)
- Luxury Vinyl Plank ($45/sqm)
- Premium Wool Carpet ($65/sqm)

**Walls:**
- Premium White Paint ($15/sqm)
- Designer Wallpaper ($35/sqm)
- Solid Wood Paneling ($75/sqm)
- Exposed Brick Wall ($55/sqm)

**Ceiling:**
- Smooth Gypsum Ceiling ($25/sqm)
- Textured Gypsum Ceiling ($35/sqm)
- Suspended Grid Ceiling ($45/sqm)
- Wood Plank Ceiling ($75/sqm)

**Lighting:**
- LED Recessed Lighting ($150/unit)
- Modern Pendant Lights ($200/unit)
- Crystal Chandelier ($800/unit)
- Track Lighting System ($120/unit)

## 🚨 Troubleshooting

### Common Issues

1. **"Failed to generate customized image"**
   - Check your API key is correct
   - Ensure you have API calls remaining
   - Try again (sometimes the service is busy)

2. **Images not loading**
   - Check your internet connection
   - Verify the API key has "Read" permissions

3. **Slow generation**
   - Hugging Face free tier can be slower during peak times
   - Consider upgrading to paid tier for faster generation

### Getting Help

- **Hugging Face Documentation**: [https://huggingface.co/docs](https://huggingface.co/docs)
- **API Status**: [https://status.huggingface.co](https://status.huggingface.co)

## 🎉 You're Ready!

Your AI-powered room customization system is now ready! Users can:

1. ✅ Browse 6 different room types
2. ✅ Select from 16 professional material options
3. ✅ See real-time cost calculations
4. ✅ Generate AI-customized room images
5. ✅ Save and share their designs

The system provides a professional interior design experience with realistic pricing and AI-generated visualizations!
