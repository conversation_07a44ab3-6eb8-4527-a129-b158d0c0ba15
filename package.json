{"name": "pionare", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@babylonjs/core": "^8.11.0", "@babylonjs/loaders": "^8.11.0", "@babylonjs/materials": "^8.11.0", "@mediapipe/camera_utils": "^0.3.1675466862", "@mediapipe/drawing_utils": "^0.3.1675466124", "@mediapipe/selfie_segmentation": "^0.1.1675465747", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@tensorflow-models/body-pix": "^2.2.1", "@tensorflow-models/deeplab": "^0.2.2", "@tensorflow/tfjs": "^4.22.0", "@types/fabric": "^5.3.10", "@types/three": "^0.177.0", "aframe": "^1.7.1", "aframe-event-set-component": "^5.1.0", "aframe-react": "^4.4.0", "bootstrap-icons": "^1.13.1", "fabric": "^6.6.7", "framer-motion": "^12.9.4", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "three": "^0.177.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}