// Unity WebGL Loader - Cozy Bedroom with Built-in UI Controls
// This simulates the Unity bedroom environment with in-Unity material controls

console.log('🛏️ Unity Cozy Bedroom Loader initialized');

// Mock Unity instance creation for bedroom
window.createUnityInstance = function(canvas, config, progressCallback) {
  console.log('🔧 Creating Unity Cozy Bedroom instance with config:', config);
  
  return new Promise((resolve) => {
    // Simulate loading progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 0.08; // Slightly slower loading for realism
      if (progressCallback) {
        progressCallback(progress);
      }
      
      if (progress >= 1) {
        clearInterval(interval);
        
        // Create bedroom scene visualization
        createBedroomScene(canvas);
        
        // Mock Unity instance with bedroom-specific methods
        const unityInstance = {
          SendMessage: function(objectName, methodName, value) {
            console.log(`📤 Unity Bedroom SendMessage: ${objectName}.${methodName}(${value})`);
            
            try {
              const data = JSON.parse(value);
              console.log('🛏️ Bedroom material update received:', data);
              
              // Simulate material change in Unity bedroom
              setTimeout(() => {
                console.log(`✅ Bedroom ${data.elementType} material changed to ${data.materialName || data.name} in Unity`);
                updateBedroomMaterial(canvas, data);
              }, 100);
              
            } catch (e) {
              console.log('📤 Unity bedroom message:', value);
            }
          },
          
          Quit: function() {
            console.log('🛑 Unity bedroom instance quit');
          },
          
          // Bedroom-specific methods
          SetCameraPreset: function(presetIndex) {
            console.log(`📷 Bedroom camera preset ${presetIndex} activated`);
          },
          
          ToggleUI: function() {
            console.log('🎮 Bedroom UI panels toggled');
          }
        };
        
        console.log('✅ Unity Cozy Bedroom instance created successfully');
        console.log('🎮 Built-in material and camera controls are now active');
        resolve(unityInstance);
      }
    }, 250); // Slightly slower for bedroom loading
  });
};

function createBedroomScene(canvas) {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // Clear canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  // Create bedroom background
  const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
  gradient.addColorStop(0, '#2C3E50');
  gradient.addColorStop(1, '#34495E');
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // Draw bedroom elements
  drawBedroomElements(ctx, canvas);
  
  // Add Unity UI overlay
  addUnityUIOverlay(ctx, canvas);
  
  console.log('🛏️ Bedroom scene created with Unity UI controls');
}

function drawBedroomElements(ctx, canvas) {
  const width = canvas.width;
  const height = canvas.height;
  
  // Draw floor
  ctx.fillStyle = '#8B4513'; // Hardwood color
  ctx.fillRect(0, height * 0.7, width, height * 0.3);
  
  // Draw walls
  ctx.fillStyle = '#F5F5DC'; // Cream color
  ctx.fillRect(0, height * 0.2, width, height * 0.5);
  
  // Draw ceiling
  ctx.fillStyle = '#FFFFFF'; // White color
  ctx.fillRect(0, 0, width, height * 0.2);
  
  // Draw bed outline
  ctx.strokeStyle = '#654321';
  ctx.lineWidth = 3;
  ctx.strokeRect(width * 0.3, height * 0.4, width * 0.4, height * 0.25);
  
  // Draw furniture outlines
  ctx.strokeRect(width * 0.1, height * 0.45, width * 0.15, height * 0.15); // Nightstand
  ctx.strokeRect(width * 0.75, height * 0.45, width * 0.15, height * 0.15); // Nightstand
  
  // Add bedroom text
  ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
  ctx.font = 'bold 24px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('🛏️ Unity Cozy Bedroom', width / 2, height / 2 - 40);
  
  ctx.font = '16px Arial';
  ctx.fillText('Built-in Unity UI Controls Active', width / 2, height / 2);
  ctx.fillText('Use left panel for materials, bottom for camera', width / 2, height / 2 + 25);
}

function addUnityUIOverlay(ctx, canvas) {
  const width = canvas.width;
  const height = canvas.height;
  
  // Left panel for material controls
  ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
  ctx.fillRect(0, 0, width * 0.25, height);
  
  // Panel header
  ctx.fillStyle = '#FFD700';
  ctx.font = 'bold 18px Arial';
  ctx.textAlign = 'left';
  ctx.fillText('🛏️ Bedroom Materials', 15, 30);
  
  // Material sections
  ctx.fillStyle = 'white';
  ctx.font = '14px Arial';
  ctx.fillText('Floor Material:', 15, 70);
  ctx.fillText('▼ Hardwood Oak', 15, 90);
  
  ctx.fillText('Wall Material:', 15, 130);
  ctx.fillText('▼ Warm Cream', 15, 150);
  
  ctx.fillText('Ceiling Material:', 15, 190);
  ctx.fillText('▼ Classic White', 15, 210);
  
  // Reset button
  ctx.fillStyle = 'rgba(220, 53, 69, 0.8)';
  ctx.fillRect(15, 240, 150, 30);
  ctx.fillStyle = 'white';
  ctx.textAlign = 'center';
  ctx.fillText('🔄 Reset Materials', 90, 260);
  
  // Bottom panel for camera controls
  ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
  ctx.fillRect(width * 0.6, height - 80, width * 0.4, 80);
  
  // Camera controls
  ctx.fillStyle = '#FFD700';
  ctx.font = 'bold 14px Arial';
  ctx.textAlign = 'left';
  ctx.fillText('📷 Camera Controls', width * 0.62, height - 55);
  
  ctx.fillStyle = 'white';
  ctx.font = '12px Arial';
  ctx.fillText('1: Overview  2: Bed Focus  3: Corner  4: Detail', width * 0.62, height - 35);
  ctx.fillText('Mouse: Rotate | Scroll: Zoom | R: Reset', width * 0.62, height - 15);
}

function updateBedroomMaterial(canvas, materialData) {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // Redraw scene with new material
  createBedroomScene(canvas);
  
  // Apply material color overlay
  ctx.save();
  ctx.globalCompositeOperation = 'multiply';
  ctx.globalAlpha = 0.6;
  
  const width = canvas.width;
  const height = canvas.height;
  
  if (materialData.elementType === 'floor') {
    ctx.fillStyle = materialData.color || '#8B4513';
    ctx.fillRect(0, height * 0.7, width, height * 0.3);
    console.log(`🛏️ Bedroom floor updated to: ${materialData.materialName || materialData.name}`);
  } else if (materialData.elementType === 'walls') {
    ctx.fillStyle = materialData.color || '#F5F5DC';
    ctx.fillRect(0, height * 0.2, width, height * 0.5);
    console.log(`🛏️ Bedroom walls updated to: ${materialData.materialName || materialData.name}`);
  } else if (materialData.elementType === 'ceiling') {
    ctx.fillStyle = materialData.color || '#FFFFFF';
    ctx.fillRect(0, 0, width, height * 0.2);
    console.log(`🛏️ Bedroom ceiling updated to: ${materialData.materialName || materialData.name}`);
  }
  
  ctx.restore();
  
  // Update UI panel to show current selection
  updateUIPanel(ctx, canvas, materialData);
}

function updateUIPanel(ctx, canvas, materialData) {
  const width = canvas.width;
  
  // Clear and redraw the relevant UI section
  ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
  
  if (materialData.elementType === 'floor') {
    ctx.fillRect(0, 60, width * 0.25, 40);
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Floor Material:', 15, 80);
    ctx.fillText(`▼ ${materialData.materialName || materialData.name}`, 15, 100);
  } else if (materialData.elementType === 'walls') {
    ctx.fillRect(0, 120, width * 0.25, 40);
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Wall Material:', 15, 140);
    ctx.fillText(`▼ ${materialData.materialName || materialData.name}`, 15, 160);
  } else if (materialData.elementType === 'ceiling') {
    ctx.fillRect(0, 180, width * 0.25, 40);
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Ceiling Material:', 15, 200);
    ctx.fillText(`▼ ${materialData.materialName || materialData.name}`, 15, 220);
  }
}

console.log('📦 Unity Cozy Bedroom WebGL Loader ready with built-in UI controls');
