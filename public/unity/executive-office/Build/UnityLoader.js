// Unity WebGL Loader - Placeholder for Executive Office
// This would normally be generated by Unity WebGL build

console.log('🎮 Unity WebGL Loader - Executive Office');

// Mock Unity instance creation
window.createUnityInstance = function(canvas, config, progressCallback) {
  console.log('🔧 Creating Unity instance with config:', config);
  
  return new Promise((resolve) => {
    // Simulate loading progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 0.1;
      if (progressCallback) {
        progressCallback(progress);
      }
      
      if (progress >= 1) {
        clearInterval(interval);
        
        // Mock Unity instance
        const unityInstance = {
          SendMessage: function(objectName, methodName, value) {
            console.log(`📤 Unity SendMessage: ${objectName}.${methodName}(${value})`);
            
            try {
              const data = JSON.parse(value);
              console.log('🎨 Material update received:', data);
              
              // Simulate material change in Unity
              setTimeout(() => {
                console.log(`✅ ${data.elementType} material changed to ${data.name} in Unity`);
              }, 100);
              
            } catch (e) {
              console.log('📤 Unity message:', value);
            }
          },
          
          Quit: function() {
            console.log('🛑 Unity instance quit');
          }
        };
        
        console.log('✅ Unity Executive Office instance created successfully');
        resolve(unityInstance);
      }
    }, 200);
  });
};

console.log('📦 Unity WebGL Loader ready');
