// Unity WebGL Loader - Placeholder for Executive Office
// This would normally be generated by Unity WebGL build

console.log('🎮 Unity WebGL Loader - Executive Office');

// Mock Unity instance creation
window.createUnityInstance = function(canvas, config, progressCallback) {
  console.log('🔧 Creating Unity instance with config:', config);
  
  return new Promise((resolve) => {
    // Simulate loading progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 0.1;
      if (progressCallback) {
        progressCallback(progress);
      }
      
      if (progress >= 1) {
        clearInterval(interval);
        
        // Mock Unity instance
        const unityInstance = {
          SendMessage: function(objectName, methodName, value) {
            console.log(`📤 Unity SendMessage: ${objectName}.${methodName}(${value})`);

            try {
              const data = JSON.parse(value);
              console.log('🎨 Material update received in Unity:', {
                elementType: data.elementType,
                materialName: data.name,
                color: data.color,
                texture: data.texture
              });

              // Simulate realistic material change in Unity scene
              setTimeout(() => {
                console.log(`✅ Unity Scene: ${data.elementType} material successfully changed to "${data.name}" (${data.color})`);

                // Simulate visual feedback
                if (data.elementType === 'floor') {
                  console.log('🏢 Unity: Floor texture and color updated in executive office');
                } else if (data.elementType === 'walls') {
                  console.log('🏢 Unity: Wall materials updated with new texture');
                } else if (data.elementType === 'ceiling') {
                  console.log('🏢 Unity: Ceiling design and color applied');
                }
              }, 150);

            } catch (e) {
              console.log('📤 Unity message:', value);
            }
          },

          Quit: function() {
            console.log('🛑 Unity instance quit');
          }
        };
        
        console.log('✅ Unity Executive Office instance created successfully');
        resolve(unityInstance);
      }
    }, 200);
  });
};

console.log('📦 Unity WebGL Loader ready');
